import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Award,
  Plus,
  Edit,
  Trash2,
  Calendar,
  Building,
  FileText,
  Link as LinkIcon,
  Upload,
  X,
  Save
} from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from './DashboardLayout';
import Button from '../ui/Button';
import Card from '../ui/Card';
import { CardBody, CardTitle } from '../ui/Card';

interface AchievementFile {
  path: string;
  original_name: string;
  mime_type: string;
  size: number;
}

interface Achievement {
  id: number;
  freelance_profile_id: number;
  title: string;
  organization: string;
  date_obtained: string;
  description: string;
  file_path: string | null;
  files: AchievementFile[];
  achievement_url: string | null;
  created_at?: string;
  updated_at?: string;
}

const AchievementsManagement: React.FC = () => {
  const navigate = useNavigate();
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedAchievement, setSelectedAchievement] = useState<Achievement | null>(null);
  const [filePreview, setFilePreview] = useState<string | null>(null);
  const [filePreviews, setFilePreviews] = useState<string[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [saving, setSaving] = useState<boolean>(false);

  useEffect(() => {
    fetchAchievements();
  }, []);

  const fetchAchievements = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem("token");
      if (!token) {
        navigate('/login');
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/achievements`, {
        method: "GET",
        headers: { "Authorization": `Bearer ${token}` }
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des réalisations');
      }

      const data = await response.json();
      setAchievements(data.achievements || []);
      setLoading(false);
    } catch (error) {
      console.error("Erreur lors de la récupération des réalisations", error);
      setError("Impossible de charger vos réalisations. Veuillez réessayer plus tard.");
      setLoading(false);
    }
  };

  const handleChange = (field: keyof Achievement, value: string) => {
    setSelectedAchievement((prev) => prev ? { ...prev, [field]: value } : null);
  };

  // const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const file = e.target.files?.[0];
  //   if (file) {
  //     setSelectedFile(file);

  //     // Créer un aperçu du fichier
  //     const fileUrl = URL.createObjectURL(file);
  //     setFilePreview(fileUrl);
  //   }
  // };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (!files) return;

      const previews: string[] = [];
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        previews.push(URL.createObjectURL(file));
      }

      setFilePreviews(previews);
      setSelectedFiles(Array.from(files)); // à toi d’avoir `selectedFiles` si tu veux les envoyer
  };


  const handleSave = async () => {
    if (!selectedAchievement) return;

    try {
      setSaving(true);
      const token = localStorage.getItem("token");
      if (!token) {
        navigate('/login');
        return;
      }

      const formData = new FormData();
      formData.append('title', selectedAchievement.title);
      formData.append('organization', selectedAchievement.organization);
      formData.append('date_obtained', selectedAchievement.date_obtained);
      formData.append('description', selectedAchievement.description);

      if (selectedAchievement.achievement_url) {
        formData.append('achievement_url', selectedAchievement.achievement_url);
      }

      // if (selectedFile) {
      //   formData.append('file', selectedFile);
      // }

      // ✅ Envoi multiple : ajoute chaque fichier dans `files[]`
      if (selectedFiles && selectedFiles.length > 0) {
        selectedFiles.forEach((file) => {
          formData.append('files[]', file);
        });
      }

      const method = selectedAchievement.id ? "POST" : "POST";
      const url = selectedAchievement.id
        ? `${API_BASE_URL}/api/achievements/${selectedAchievement.id}`
        : `${API_BASE_URL}/api/achievements`;

      // Si c'est une mise à jour, utiliser la méthode PUT avec _method=PUT pour le support de FormData
      if (selectedAchievement.id) {
        formData.append('_method', 'POST');
      }

      const response = await fetch(url, {
        method,
        headers: {
          "Authorization": `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error('Erreur lors de l\'enregistrement de la réalisation');
      }

      const data = await response.json();

      if (selectedAchievement.id) {
        setAchievements((prev) => prev.map(ach => ach.id === data.achievement.id ? data.achievement : ach));
      } else {
        setAchievements((prev) => [...prev, data.achievement]);
      }

      // Réinitialiser les états
      setSelectedAchievement(null);
      setFilePreview(null);
      setSelectedFile(null);
      setFilePreviews([]);
      setSelectedFiles([]);

      // Rafraîchir la liste des réalisations
      fetchAchievements();
    } catch (error) {
      console.error("Erreur lors de l'enregistrement de la réalisation", error);
      setError("Erreur lors de l'enregistrement de la réalisation. Veuillez réessayer.");
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (id: number) => {
    // Utiliser window.confirm au lieu de confirm global pour éviter l'erreur ESLint
    if (!window.confirm("Êtes-vous sûr de vouloir supprimer cette réalisation ?")) {
      return;
    }

    try {
      const token = localStorage.getItem("token");
      if (!token) {
        navigate('/login');
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/achievements/${id}`, {
        method: "DELETE",
        headers: { "Authorization": `Bearer ${token}` }
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la suppression de la réalisation');
      }

      setAchievements((prev) => prev.filter(ach => ach.id !== id));
    } catch (error) {
      console.error("Erreur lors de la suppression de la réalisation", error);
      setError("Erreur lors de la suppression de la réalisation. Veuillez réessayer.");
    }
  };

  const handleAddNew = () => {
    setSelectedAchievement({
      id: 0,
      freelance_profile_id: 0,
      title: "",
      organization: "",
      date_obtained: "",
      description: "",
      file_path: null,
      files:[],
      achievement_url: null
    });
    setFilePreview(null);
    setFilePreviews([]);
    setSelectedFile(null);
    setSelectedFiles([]);
  };

  const handleCancel = () => {
    setSelectedAchievement(null);
    setFilePreview(null);
    setFilePreviews([]);
    setSelectedFile(null);
    setSelectedFiles([]);
  };

  return (
    <DashboardLayout
      title="Gestion des réalisations"
      subtitle="Ajoutez et gérez vos réalisations et certifications professionnelles"
      actions={
        <Button
          variant="primary"
          leftIcon={<Plus className="h-5 w-5" />}
          onClick={handleAddNew}
          disabled={!!selectedAchievement}
        >
          Ajouter une réalisation
        </Button>
      }
    >
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-red-700">
          {error}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      ) : (
        <div className="space-y-6">
          {selectedAchievement && (
            <Card className="mb-6">
              <CardBody className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <CardTitle className="text-xl">
                    {selectedAchievement.id ? "Modifier la réalisation" : "Ajouter une nouvelle réalisation"}
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCancel}
                    className="p-1"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                      Titre *
                    </label>
                    <input
                      type="text"
                      id="title"
                      value={selectedAchievement.title}
                      onChange={(e) => handleChange("title", e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="organization" className="block text-sm font-medium text-gray-700 mb-1">
                      Organisation
                    </label>
                    <input
                      type="text"
                      id="organization"
                      value={selectedAchievement.organization}
                      onChange={(e) => handleChange("organization", e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>

                  <div>
                    <label htmlFor="date_obtained" className="block text-sm font-medium text-gray-700 mb-1">
                      Date
                    </label>
                    <input
                      type="date"
                      id="date_obtained"
                      value={selectedAchievement.date_obtained}
                      onChange={(e) => handleChange("date_obtained", e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>

                  <div>
                    <label htmlFor="achievement_url" className="block text-sm font-medium text-gray-700 mb-1">
                      URL (lien externe)
                    </label>
                    <input
                      type="url"
                      id="achievement_url"
                      value={selectedAchievement.achievement_url || ''}
                      onChange={(e) => handleChange("achievement_url", e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="https://exemple.com/certification"
                    />
                  </div>
                </div>

                <div className="mt-4">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    id="description"
                    value={selectedAchievement.description}
                    onChange={(e) => handleChange("description", e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    rows={4}
                  />
                </div>

                <div className="mt-4">
                  <label htmlFor="file" className="block text-sm font-medium text-gray-700 mb-1">
                    Fichiers (Images ou PDF de réalisation)
                  </label>
                  
                  <input
                    type="file"
                    id="file"
                    multiple
                    onChange={handleFileChange} // mis à jour pour gérer plusieurs fichiers
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    accept=".pdf,.jpg,.jpeg,.png"
                  />

                  {/* Aperçus des nouveaux fichiers sélectionnés */}
                  {filePreviews.length > 0 && (
                    <div className="mt-2 grid grid-cols-2 md:grid-cols-3 gap-4">
                      {filePreviews.map((src, idx) => (
                        <div key={idx} className="relative">
                          <img
                            src={src}
                            alt={`Aperçu ${idx + 1}`}
                            className="max-h-40 rounded-md border border-gray-300 object-cover"
                          />
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Aperçus des fichiers existants (en modification) */}
                  {selectedAchievement?.files?.length > 0 && filePreviews.length === 0 && (
                    <div className="mt-4">
                      <p className="text-sm font-medium text-gray-600 mb-2">Fichiers existants :</p>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {selectedAchievement.files.map((file: any, index: number) => (
                          <div key={index}>
                            {file.mime_type.includes("image") ? (
                              <img
                                src={`${API_BASE_URL}/storage/${file.path}`}
                                alt={`Image ${index + 1}`}
                                className="max-h-40 rounded-md border border-gray-300 object-cover"
                              />
                            ) : (
                              <p className="text-gray-500 text-sm">
                                📄 {file.original_name}
                              </p>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* <div className="mt-4">
                  <label htmlFor="file" className="block text-sm font-medium text-gray-700 mb-1">
                    Fichier (Image de réalisation)
                  </label>
                  <input
                    type="file"
                    id="file"
                    onChange={handleFileChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    accept=".pdf,.jpg,.jpeg,.png"
                  />
                  {filePreview && (
                    <div className="mt-2">
                      <img
                        src={filePreview}
                        alt="Aperçu du fichier"
                        className="max-h-40 rounded-md border border-gray-300"
                      />
                    </div>
                  )}
                  {selectedAchievement.file_path && !filePreview && (
                    <div className="mt-2 text-sm text-gray-600">
                      Fichier actuel: {selectedAchievement.file_path.split('/').pop()}
                    </div>
                  )}
                </div> */}

                <div className="flex justify-end space-x-4 mt-6">
                  <Button
                    variant="outline"
                    onClick={handleCancel}
                  >
                    Annuler
                  </Button>
                  <Button
                    variant="primary"
                    leftIcon={<Save className="h-5 w-5" />}
                    onClick={handleSave}
                    disabled={saving || !selectedAchievement.title}
                  >
                    {saving ? 'Enregistrement...' : 'Enregistrer'}
                  </Button>
                </div>
              </CardBody>
            </Card>
          )}

          {achievements.length === 0 && !selectedAchievement ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg border border-gray-200">
              <Award className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune réalisation</h3>
              <p className="text-gray-600 mb-4">Vous n'avez pas encore ajouté de réalisations ou certifications.</p>
              <Button
                variant="primary"
                leftIcon={<Plus className="h-5 w-5" />}
                onClick={handleAddNew}
              >
                Ajouter une réalisation
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {achievements.map((achievement) => (
                <Card key={achievement.id} className="h-full">
                  {/* <div
                    className="h-32 w-full bg-cover bg-center rounded-t"
                    style={{
                      backgroundImage: `url(${API_BASE_URL}/storage/${achievement.file_path})`,
                    }}
                  ></div> */}
                  <CardBody className="p-6 flex flex-col h-full">
                     <div
                      className="h-48 w-full bg-cover bg-center rounded-t"
                      style={{
                        backgroundImage: `url(${
                          Array.isArray(achievement.files) && achievement.files.length > 0
                            ? `${API_BASE_URL}/storage/${achievement.files[0].path}`
                            : `${API_BASE_URL}/storage/${achievement.file_path}`
                        })`,
                      }}

                      // style={{
                      //   backgroundImage: `url(${API_BASE_URL}/storage/${achievement.file_path})`,
                      // }}
                    ></div>
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex-1">
                        <CardTitle className="text-lg">{achievement.title}</CardTitle>
                        <div className="flex items-center text-gray-600 text-sm mt-1">
                          <Building className="h-4 w-4 mr-1" />
                          <span>{achievement.organization || 'Non spécifié'}</span>
                        </div>
                        {achievement.date_obtained && (
                          <div className="flex items-center text-gray-600 text-sm mt-1">
                            <Calendar className="h-4 w-4 mr-1" />
                            <span>{new Date(achievement.date_obtained).toLocaleDateString('fr-FR', { year: 'numeric', month: 'long', day: 'numeric' })}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex-1">
                      <p className="text-gray-700 text-sm mb-4 line-clamp-3">
                        {achievement.description || 'Aucune description fournie.'}
                      </p>
                    </div>

                    <div className="mt-auto pt-4 border-t border-gray-100 flex justify-between">
                      <div className="flex space-x-2">
                        {achievement.file_path && (
                          <a
                            href={`${API_BASE_URL}/storage/${achievement.file_path}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary-600 hover:text-primary-800 text-sm flex items-center"
                          >
                            <FileText className="h-4 w-4 mr-1" />
                            <span>Fichier</span>
                          </a>
                        )}

                        {achievement.achievement_url && (
                          <a
                            href={achievement.achievement_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary-600 hover:text-primary-800 text-sm flex items-center"
                          >
                            <LinkIcon className="h-4 w-4 mr-1" />
                            <span>Lien</span>
                          </a>
                        )}
                      </div>

                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedAchievement(achievement)}
                          className="p-1"
                        >
                          <Edit className="h-4 w-4 text-gray-600" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(achievement.id)}
                          className="p-1"
                        >
                          <Trash2 className="h-4 w-4 text-red-600" />
                        </Button>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}
    </DashboardLayout>
  );
};

export default AchievementsManagement;
