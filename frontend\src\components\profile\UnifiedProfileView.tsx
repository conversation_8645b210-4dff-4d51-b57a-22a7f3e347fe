import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Edit,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  Award,
  Globe,
  Clock,
  DollarSign,
  Building,
  User,
  FileText,
} from 'lucide-react';
import { useUnifiedProfile } from '../../context/UnifiedProfileContext';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../ui/Button';
import Avatar from '../ui/Avatar';
import { getAvatarUrl, getInitials } from '../../utils/avatarUtils';

const UnifiedProfileView: React.FC = () => {
  const navigate = useNavigate();
  const { profile, loading, error } = useUnifiedProfile();
  
  const handleEditProfile = () => {
    navigate('/dashboard/profile/edit-new');
  };
  
  // Helper function to render availability badge
  const renderAvailabilityBadge = () => {
    if (!profile?.is_professional) return null;
    
    const status = profile.availability_status || 'unavailable';
    const statusColors = {
      available: 'bg-green-100 text-green-800',
      busy: 'bg-amber-100 text-amber-800',
      unavailable: 'bg-red-100 text-red-800',
    };
    
    const statusText = {
      available: 'Available',
      busy: 'Busy',
      unavailable: 'Unavailable',
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[status as keyof typeof statusColors]}`}>
        {statusText[status as keyof typeof statusText]}
      </span>
    );
  };
  
  // Helper function to render skills
  const renderSkills = (skills: string[] = []) => {
    return (
      <div className="flex flex-wrap gap-2">
        {skills.map((skill, index) => (
          <span
            key={index}
            className="bg-primary-50 text-primary-700 px-3 py-1 rounded-full text-sm"
          >
            {skill}
          </span>
        ))}
        {skills.length === 0 && (
          <span className="text-gray-500 italic">No skills added yet</span>
        )}
      </div>
    );
  };
  
  if (loading && !profile) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          <span className="ml-2 text-gray-600">Loading profile...</span>
        </div>
      </DashboardLayout>
    );
  }
  
  if (error || !profile) {
    return (
      <DashboardLayout>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <div className="text-red-500 mb-4">⚠️</div>
          <h2 className="text-lg font-semibold text-red-700 mb-2">Error</h2>
          <p className="text-red-600">{error || 'Profile not found'}</p>
          <Button
            variant="primary"
            className="mt-4"
            onClick={() => navigate('/dashboard')}
          >
            Return to Dashboard
          </Button>
        </div>
      </DashboardLayout>
    );
  }
  
  return (
    <DashboardLayout
      title="My Profile"
      subtitle="View and manage your personal information"
      actions={
        <Button
          variant="primary"
          leftIcon={<Edit className="h-5 w-5" />}
          onClick={handleEditProfile}
        >
          Edit Profile
        </Button>
      }
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left column - Profile summary */}
        <div className="lg:col-span-1 space-y-6">
          {/* Profile card */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="p-6">
              <div className="flex flex-col items-center">
                <div className="relative">
                  <Avatar
                    size="xl"
                    src={getAvatarUrl(profile.avatar)}
                    fallback={getInitials(profile.first_name, profile.last_name)}
                    className="mb-4"
                  />
                  {profile.is_professional && (
                    <div className="absolute -bottom-1 -right-1">
                      {renderAvailabilityBadge()}
                    </div>
                  )}
                </div>
                
                <h2 className="text-xl font-bold text-gray-900">
                  {profile.first_name} {profile.last_name}
                </h2>
                
                {profile.is_professional && profile.title && (
                  <p className="text-gray-600 mt-1">{profile.title}</p>
                )}
                
                {!profile.is_professional && profile.type === 'entreprise' && profile.company_name && (
                  <p className="text-gray-600 mt-1">{profile.company_name}</p>
                )}
                
                <div className="w-full mt-6 space-y-3">
                  <div className="flex items-center text-gray-700">
                    <Mail className="h-5 w-5 text-gray-400 mr-2" />
                    <span className="text-sm">{profile.email}</span>
                  </div>
                  
                  {profile.phone && (
                    <div className="flex items-center text-gray-700">
                      <Phone className="h-5 w-5 text-gray-400 mr-2" />
                      <span className="text-sm">{profile.phone}</span>
                    </div>
                  )}
                  
                  {profile.address && (
                    <div className="flex items-center text-gray-700">
                      <MapPin className="h-5 w-5 text-gray-400 mr-2" />
                      <span className="text-sm">
                        {profile.address}
                        {profile.city && `, ${profile.city}`}
                        {profile.country && `, ${profile.country}`}
                      </span>
                    </div>
                  )}
                  
                  {profile.is_professional && profile.profession && (
                    <div className="flex items-center text-gray-700">
                      <Briefcase className="h-5 w-5 text-gray-400 mr-2" />
                      <span className="text-sm">{profile.profession}</span>
                    </div>
                  )}
                  
                  {profile.is_professional && profile.hourly_rate !== undefined && (
                    <div className="flex items-center text-gray-700">
                      <DollarSign className="h-5 w-5 text-gray-400 mr-2" />
                      <span className="text-sm">{profile.hourly_rate}€ / hour</span>
                    </div>
                  )}
                  
                  {!profile.is_professional && profile.type === 'entreprise' && profile.industry && (
                    <div className="flex items-center text-gray-700">
                      <Building className="h-5 w-5 text-gray-400 mr-2" />
                      <span className="text-sm">{profile.industry}</span>
                    </div>
                  )}
                </div>
                
                <div className="w-full mt-6">
                  <div className="bg-gray-100 h-2 rounded-full overflow-hidden">
                    <div
                      className="bg-primary-600 h-full rounded-full"
                      style={{ width: `${profile.completion_percentage}%` }}
                    ></div>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Profile completion: {profile.completion_percentage}%
                  </p>
                </div>
                
                <div className="w-full mt-6">
                  <Button
                    variant="primary"
                    fullWidth
                    leftIcon={<Edit className="h-5 w-5" />}
                    onClick={handleEditProfile}
                  >
                    Edit Profile
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Right column - Detailed information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Bio section */}
          {profile.bio && (
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <User className="h-5 w-5 text-primary-600 mr-2" />
                  About Me
                </h3>
                <p className="text-gray-700 whitespace-pre-line">{profile.bio}</p>
              </div>
            </div>
          )}
          
          {/* Skills section (for professionals) */}
          {profile.is_professional && profile.skills && profile.skills.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Award className="h-5 w-5 text-primary-600 mr-2" />
                  Skills
                </h3>
                {renderSkills(profile.skills)}
              </div>
            </div>
          )}
          
          {/* Services section (for professionals) */}
          {profile.is_professional && profile.services_offered && profile.services_offered.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Briefcase className="h-5 w-5 text-primary-600 mr-2" />
                  Services Offered
                </h3>
                {renderSkills(profile.services_offered)}
              </div>
            </div>
          )}
          
          {/* Languages section (for professionals) */}
          {profile.is_professional && profile.languages && profile.languages.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Globe className="h-5 w-5 text-primary-600 mr-2" />
                  Languages
                </h3>
                {renderSkills(profile.languages)}
              </div>
            </div>
          )}
          
          {/* Portfolio section (for professionals) */}
          {profile.is_professional && profile.portfolio && profile.portfolio.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <FileText className="h-5 w-5 text-primary-600 mr-2" />
                  Portfolio
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  {profile.portfolio.map((item) => (
                    <div key={item.id} className="group relative">
                      <div className="aspect-w-1 aspect-h-1 rounded-lg overflow-hidden bg-gray-100">
                        {item.type.startsWith('image/') ? (
                          <img
                            src={item.path}
                            alt={item.name}
                            className="object-cover w-full h-full"
                          />
                        ) : (
                          <div className="flex items-center justify-center h-full">
                            <FileText className="h-12 w-12 text-gray-400" />
                            <span className="text-sm text-gray-500 mt-2">{item.name}</span>
                          </div>
                        )}
                      </div>
                      <div className="mt-2">
                        <p className="text-sm text-gray-700 truncate">{item.name}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
          
          {/* Company details section (for client companies) */}
          {!profile.is_professional && profile.type === 'entreprise' && (
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Building className="h-5 w-5 text-primary-600 mr-2" />
                  Company Details
                </h3>
                <div className="space-y-4">
                  {profile.company_name && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Company Name</h4>
                      <p className="text-gray-700">{profile.company_name}</p>
                    </div>
                  )}
                  
                  {profile.industry && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Industry</h4>
                      <p className="text-gray-700">{profile.industry}</p>
                    </div>
                  )}
                  
                  {profile.company_size && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Company Size</h4>
                      <p className="text-gray-700">{profile.company_size}</p>
                    </div>
                  )}
                  
                  {profile.website && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Website</h4>
                      <a
                        href={profile.website.startsWith('http') ? profile.website : `https://${profile.website}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary-600 hover:text-primary-700"
                      >
                        {profile.website}
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
};

export default UnifiedProfileView;
