import React from 'react';
import Avatar from './Avatar';
import Badge from './Badge';

interface ProfileCardProps {
  firstName: string;
  lastName: string;
  title?: string;
  avatarUrl?: string;
  rating?: number;
  availabilityStatus?: string;
  completionPercentage?: number;
  children?: React.ReactNode;
  className?: string;
}

const ProfileCard: React.FC<ProfileCardProps> = ({
  firstName,
  lastName,
  title,
  avatarUrl,
  rating,
  availabilityStatus,
  completionPercentage,
  children,
  className = '',
}) => {
  const getAvailabilityColor = (status?: string) => {
    switch (status) {
      case 'available':
        return 'success';
      case 'busy':
        return 'warning';
      case 'unavailable':
        return 'error';
      default:
        return 'neutral';
    }
  };

  const getAvailabilityText = (status?: string) => {
    switch (status) {
      case 'available':
        return 'Available';
      case 'busy':
        return 'Busy';
      case 'unavailable':
        return 'Unavailable';
      default:
        return 'Not specified';
    }
  };

  const renderStars = (rating: number) => {
    return Array(5).fill(0).map((_, index) => (
      <svg
        key={index}
        className={`h-5 w-5 ${index < Math.floor(rating) ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
      </svg>
    ));
  };

  return (
    <div className={`bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden ${className}`}>
      <div className="p-6 text-center">
        <div className="relative mx-auto mb-4">
          <Avatar
            size="xl"
            src={avatarUrl}
            fallback={`${firstName.charAt(0)}${lastName.charAt(0)}`}
            className="mx-auto"
          />
          {availabilityStatus && (
            <Badge
              variant={getAvailabilityColor(availabilityStatus) as 'success' | 'warning' | 'error' | 'neutral'}
              className="absolute bottom-0 right-0 border-2 border-white"
            >
              {getAvailabilityText(availabilityStatus)}
            </Badge>
          )}
        </div>

        <h3 className="text-xl font-semibold text-neutral-900">
          {firstName} {lastName}
        </h3>
        {title && <p className="text-neutral-600 mt-1">{title}</p>}

        {rating !== undefined && (
          <div className="flex justify-center mt-2">
            {renderStars(rating)}
            <span className="ml-2 text-sm text-neutral-600">{rating.toFixed(1)}</span>
          </div>
        )}

        {completionPercentage !== undefined && (
          <div className="mt-4 pt-4 border-t border-neutral-200">
            <div className="flex justify-between text-sm mb-2">
              <span className="text-neutral-600">Profile Completion</span>
              <span className="font-medium text-neutral-900">{completionPercentage}%</span>
            </div>
            <div className="w-full bg-neutral-200 rounded-full h-2">
              <div
                className="bg-primary-600 h-2 rounded-full"
                style={{ width: `${completionPercentage}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>

      {children && (
        <div className="bg-neutral-50 px-6 py-4 border-t border-neutral-200">
          {children}
        </div>
      )}
    </div>
  );
};

export default ProfileCard;
