.stripe-form-container {
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
}

.stripe-form-container form {
  background: #fff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stripe-form-container h3 {
  margin-bottom: 20px;
  color: #30313d;
  font-size: 1.5rem;
}

.stripe-form-container fieldset {
  border: none;
  padding: 0;
  margin: 0;
}

.form-group {
  margin-bottom: 20px;
}

.error-message {
  color: #df1b41;
  margin: 10px 0;
  font-size: 0.9rem;
}

button[type="submit"] {
  background: #0570de;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 5px;
  font-size: 1rem;
  cursor: pointer;
  width: 100%;
  transition: background-color 0.2s ease;
}

button[type="submit"]:hover {
  background: #0460c0;
}

button[type="submit"]:disabled {
  background: #ccc;
  cursor: not-allowed;
} 