import React, { ReactNode } from 'react';

interface PageTitleProps {
  title: string;
  subtitle?: string;
  icon?: ReactNode;
  actions?: ReactNode;
  className?: string;
}

/**
 * Composant PageTitle pour afficher le titre de la page
 * avec une meilleure accessibilité
 */
const PageTitle: React.FC<PageTitleProps> = ({
  title,
  subtitle,
  icon,
  actions,
  className = ''
}) => {
  return (
    <div className={`mb-8 ${className}`}>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div className="flex items-center">
          {icon && (
            <div className="mr-4 text-primary-600">
              {icon}
            </div>
          )}
          <div>
            <h1 className="text-2xl font-bold text-neutral-900">{title}</h1>
            {subtitle && (
              <p className="mt-1 text-neutral-600">{subtitle}</p>
            )}
          </div>
        </div>

        {actions && (
          <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
};

// Optimiser le composant avec React.memo pour éviter les re-rendus inutiles
export default React.memo(PageTitle);
