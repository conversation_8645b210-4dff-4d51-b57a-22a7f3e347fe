import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import ServiceOfferForm from './ServiceOfferForm';
import Alert from '../ui/Alert';
import type { ServiceOfferFormData } from './types';

const ServiceOfferCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Récupérer le token d'authentification
  const token = localStorage.getItem('token');

  // Récupérer l'utilisateur directement depuis le localStorage sans vérification supplémentaire
  // Puisque cette page est accessible uniquement depuis le dashboard professionnel
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  // Log pour déboguer
  console.log("ServiceOfferCreatePage - Current user:", user);
  console.log("ServiceOfferCreatePage - Current URL:", window.location.href);

  // Ajouter un useEffect pour vérifier l'authentification
  React.useEffect(() => {
    // Vérifier si l'utilisateur est connecté
    if (!token) {
      console.log("No token found, redirecting to login");
      navigate('/login');
    }
  }, [token, navigate]);

  // Gérer la soumission du formulaire
  const handleFormSubmit = async (formData: ServiceOfferFormData) => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Prepare payload
      const payload = {
        title: formData.title,
        description: formData.description,
        price: typeof formData.price === 'string' ? parseFloat(formData.price) : formData.price,
        execution_time: formData.execution_time,
        concepts: formData.concepts,
        revisions: formData.revisions,
        categories: formData.categories,
        is_private: formData.is_private,
        status: formData.status,
      };

      // Si une image est fournie, utiliser FormData
      let requestBody;
      let headers: Record<string, string> = {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json', // Demander explicitement une réponse JSON
      };

      console.log("API URL:", `${API_BASE_URL}/api/service-offers`);
      // Vérifier que token n'est pas null avant d'appeler substring
      console.log("Token:", token ? token.substring(0, 10) + "..." : "No token");

      if (formData.images && formData.images.length > 0) {
        const formDataObj = new FormData();

        formData.images.forEach(file => {
          formDataObj.append('files[]', file);
        });
      // }
      // if (formData.image) {
        

        // Ajouter chaque champ au FormData
        Object.entries(payload).forEach(([key, value]) => {
          if (key === 'categories') {
            // Pour les catégories, les ajouter une par une
            if (Array.isArray(value)) {
              value.forEach((category, index) => {
                formDataObj.append(`categories[${index}]`, category);
              });
            } else {
              formDataObj.append(key, JSON.stringify(value));
            }
          } else if (key === 'is_private') {
            // Convertir le booléen en 1 ou 0 pour le backend
            formDataObj.append(key, value === true ? '1' : '0');
          } else {
            formDataObj.append(key, String(value));
          }
        });

        // Ajouter le fichier avec logs pour déboguer
        // if (formData.image) {
        //   console.log("File info:", {
        //     name: formData.image.name,
        //     type: formData.image.type,
        //     size: formData.image.size
        //   });

        //   // Vérifier si le type de fichier est autorisé
        //   const allowedTypes = [
        //     'image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/svg+xml',
        //     'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        //     'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        //     'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        //     'application/zip', 'application/x-rar-compressed'
        //   ];

        //   const isAllowed = allowedTypes.includes(formData.image.type);
        //   console.log("File type allowed:", isAllowed);

        //   formDataObj.append('files[]', formData.image);
        // } else {
        //   console.log("No file to upload");
        // }

        requestBody = formDataObj;

        // Log pour déboguer
        console.log("FormData entries:");
        // Utiliser Array.from pour convertir l'itérateur en tableau
        Array.from(formDataObj.entries()).forEach(pair => {
          console.log(pair[0] + ': ' + (pair[1] instanceof File ? pair[1].name : pair[1]));
        });
      } else {
        requestBody = JSON.stringify(payload);
        headers['Content-Type'] = 'application/json';

        // Log pour déboguer
        console.log("JSON payload:", payload);
      }

      console.log("Sending request to API...");
      const response = await fetch(`${API_BASE_URL}/api/service-offers`, {
        method: 'POST',
        headers,
        body: requestBody,
        // credentials: 'include' est maintenant inclus par défaut dans api.ts
      });

      console.log("Response status:", response.status);

      // Convertir les en-têtes en objet pour le log
      const headersObj: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        headersObj[key] = value;
      });
      console.log("Response headers:", headersObj);

      // Vérifier si la réponse est OK
      if (!response.ok) {
        // Essayer de lire le corps de la réponse comme texte d'abord
        const responseText = await response.text();
        console.error("API error response (text):", responseText);

        // Essayer de parser le texte en JSON si possible
        let errorMessage = `Erreur lors de la création du service (${response.status})`;
        try {
          const errorData = JSON.parse(responseText);
          console.error("API error response (parsed):", errorData);

          // Extraire le message d'erreur principal
          errorMessage = errorData.message || errorMessage;

          // Si nous avons des erreurs de validation détaillées, les ajouter au message
          if (errorData.errors) {
            const errorDetails = Object.entries(errorData.errors)
              .map(([field, messages]) => {
                const fieldName = field === 'is_private' ? 'Service privé' :
                                 field === 'files.0' ? 'Fichier' : field;
                return `${fieldName}: ${Array.isArray(messages) ? messages.join(', ') : messages}`;
              })
              .join('\n');

            if (errorDetails) {
              errorMessage += '\n\nDétails:\n' + errorDetails;
            }
          }
        } catch (parseError) {
          console.error("Couldn't parse error response as JSON:", parseError);

          // Si la réponse contient du HTML, c'est probablement une redirection
          if (responseText.includes('<!DOCTYPE html>')) {
            errorMessage = "La requête a été redirigée vers la page d'accueil. Vérifiez la configuration du serveur.";
          }
        }

        throw new Error(errorMessage);
      }

      // Lire la réponse comme texte d'abord
      const responseText = await response.text();
      console.log("API response (text):", responseText);

      // Si la réponse est vide, retourner un objet vide
      if (!responseText.trim()) {
        console.log("Empty response, using default data");

        // Afficher un message de succès
        setSuccess('Service créé avec succès (réponse vide)');

        // Rediriger vers la liste des services
        setTimeout(() => {
          navigate(`/dashboard/services?user=${user.id}`);
        }, 1500);

        return { id: Math.floor(Math.random() * 1000) };
      }

      // Si la réponse contient du HTML, c'est probablement une redirection
      if (responseText.includes('<!DOCTYPE html>')) {
        console.error("Received HTML response instead of JSON");

        // Essayer de créer le service manuellement
        setSuccess('Service probablement créé, mais la réponse est incorrecte');

        // Rediriger vers la liste des services
        setTimeout(() => {
          navigate(`/dashboard/services?user=${user.id}`);
        }, 1500);

        throw new Error("La réponse du serveur est une page HTML au lieu de JSON");
      }

      // Sinon, parser le JSON
      try {
        const data = JSON.parse(responseText);
        console.log("Service created successfully:", data);

        // Afficher le message de succès
        setSuccess('Service créé avec succès');

        // Rediriger vers la page de détails du service après un court délai
        setTimeout(() => {
          const serviceId = data.id || data.data?.id;
          if (serviceId) {
            navigate(`/dashboard/services?user=${user.id}`);
          } else {
            // Si pas d'ID, rediriger vers la liste des services
            navigate(`/dashboard/services?user=${user.id}`);
          }
        }, 1500);

        return data;
      } catch (parseError) {
        console.error("Error parsing JSON response:", parseError);

        // Même si le parsing échoue, essayons de créer le service
        setSuccess('Service probablement créé, mais la réponse est incorrecte');

        // Rediriger vers la liste des services
        setTimeout(() => {
          navigate('/dashboard/services');
        }, 1500);

        throw new Error("La réponse du serveur n'est pas au format JSON valide");
      }
    } catch (err) {
      console.error('Erreur:', err);
      setError(err instanceof Error ? err.message : 'Impossible de créer le service');
    } finally {
      setIsLoading(false);
    }
  };

  // Gérer l'annulation du formulaire
  const handleFormCancel = () => {
    navigate('/dashboard/services');
  };

  return (
    <DashboardLayout
      title="Créer un nouveau service"
      subtitle="Remplissez les détails de votre service pour le proposer aux clients"
    >
      {/* Afficher les erreurs */}
      {error && (
        <Alert
          type="error"
          title="Erreur"
          onClose={() => setError(null)}
          className="mb-6"
        >
          {error}
        </Alert>
      )}

      {/* Afficher les succès */}
      {success && (
        <Alert
          type="success"
          title="Succès"
          onClose={() => setSuccess(null)}
          className="mb-6"
        >
          {success}
        </Alert>
      )}

      {/* Formulaire de création */}
      <ServiceOfferForm
        onSubmit={handleFormSubmit}
        onCancel={handleFormCancel}
        isLoading={isLoading}
      />
    </DashboardLayout>
  );
};

export default ServiceOfferCreatePage;
