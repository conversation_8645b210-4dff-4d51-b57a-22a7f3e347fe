import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../Header';
import Footer from '../Footer';
import Section from '../layout/Section';
import Container from '../layout/Container';
import Grid from '../layout/Grid';
import Button from '../ui/Button';
import CallToAction from '../home/<USER>';
import { Check, X, HelpCircle } from 'lucide-react';
import Modal from '../ui/Modal';
import StripeContainer from '../stripe/StripeContainer';

const OffersPage: React.FC = () => {
  const navigate = useNavigate();
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<{
    name: string;
    price: number;
  } | null>(null);

  // Données pour les plans tarifaires
  const pricingPlans = [
    {
      name: 'Gratuit',
      description: 'Pour les artistes qui débutent',
      price: {
        monthly: 0,
        yearly: 0,
      },
      features: [
        { name: 'Profil de base', included: true },
        { name: 'Portfolio limité (3 projets)', included: true },
        { name: 'Accès aux projets publics', included: true },
        { name: 'Messagerie de base', included: true },
        { name: 'Support communautaire', included: true },
        { name: 'Projets premium', included: false },
        { name: 'Mise en avant du profil', included: false },
        { name: 'Support prioritaire', included: false },
      ],
      cta: 'Commencer gratuitement',
      popular: false,
    },
    {
      name: 'Pro',
      description: 'Pour les artistes professionnels',
      price: {
        monthly: 19.99,
        yearly: 14.99,
      },
      features: [
        { name: 'Profil avancé', included: true },
        { name: 'Portfolio illimité', included: true },
        { name: 'Accès aux projets publics et privés', included: true },
        { name: 'Messagerie avancée', included: true },
        { name: 'Support par email', included: true },
        { name: 'Accès aux projets premium', included: true },
        { name: 'Mise en avant du profil', included: true },
        { name: 'Support prioritaire', included: false },
      ],
      cta: 'Commencer l\'essai gratuit',
      popular: true,
    },
    {
      name: 'Entreprise',
      description: 'Pour les studios et agences',
      price: {
        monthly: 49.99,
        yearly: 39.99,
      },
      features: [
        { name: 'Profils multiples', included: true },
        { name: 'Portfolio illimité', included: true },
        { name: 'Accès à tous les projets', included: true },
        { name: 'Messagerie avancée avec CRM', included: true },
        { name: 'Support dédié', included: true },
        { name: 'Accès aux projets premium', included: true },
        { name: 'Mise en avant des profils', included: true },
        { name: 'Support prioritaire 24/7', included: true },
      ],
      cta: 'Contacter les ventes',
      popular: false,
    },
  ];

  // Données pour les services
  const services = [
    {
      title: 'Modélisation 3D',
      description: 'Création de modèles 3D détaillés et optimisés pour divers usages, du jeu vidéo à l\'architecture.',
      image: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    },
    {
      title: 'Animation 3D',
      description: 'Animation de personnages, objets et environnements pour donner vie à vos projets avec fluidité et réalisme.',
      image: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    },
    {
      title: 'Rendu et Visualisation',
      description: 'Création d\'images et de vidéos photoréalistes pour mettre en valeur vos produits et concepts.',
      image: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    },
    {
      title: 'Texturing',
      description: 'Application de textures détaillées pour donner réalisme et caractère à vos modèles 3D.',
      image: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    },
    {
      title: 'Conception de personnages',
      description: 'Création de personnages uniques et expressifs pour vos jeux, films ou autres projets.',
      image: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    },
    {
      title: 'Environnements 3D',
      description: 'Conception d\'environnements immersifs et détaillés pour créer des mondes captivants.',
      image: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    },
  ];

  // Données pour les FAQ
  const faqs = [
    {
      question: 'Comment fonctionne le processus de recrutement d\'artistes ?',
      answer: 'Nous avons un processus de sélection rigoureux qui comprend l\'évaluation du portfolio, des entretiens et des tests techniques pour garantir que seuls les meilleurs artistes rejoignent notre plateforme.',
    },
    {
      question: 'Puis-je changer de forfait à tout moment ?',
      answer: 'Oui, vous pouvez passer à un forfait supérieur à tout moment. Si vous souhaitez passer à un forfait inférieur, le changement prendra effet à la fin de votre période de facturation en cours.',
    },
    {
      question: 'Comment sont protégés les droits de propriété intellectuelle ?',
      answer: 'Nous prenons très au sérieux la protection de la propriété intellectuelle. Tous les contrats entre artistes et clients incluent des clauses claires sur les droits d\'auteur et la propriété des œuvres créées.',
    },
    {
      question: 'Proposez-vous des réductions pour les étudiants ?',
      answer: 'Oui, nous offrons une réduction de 50% sur le forfait Pro pour les étudiants en arts visuels et en conception 3D. Une preuve de statut étudiant est requise.',
    },
    {
      question: 'Comment sont sélectionnés les projets mis en avant ?',
      answer: 'Les projets mis en avant sont sélectionnés par notre équipe éditoriale en fonction de leur qualité, de leur originalité et de leur impact. Nous veillons à mettre en lumière des projets variés et innovants.',
    },
    {
      question: 'Offrez-vous un support technique pour les artistes ?',
      answer: 'Oui, tous nos forfaits incluent un certain niveau de support technique. Les forfaits Pro et Entreprise bénéficient d\'un support prioritaire pour résoudre rapidement tout problème technique.',
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <Header />

      {/* Hero Section */}
      <div className="bg-primary-50 py-16 md:py-24">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              Nos Offres
            </h1>
            <p className="text-neutral-600 text-lg mb-0">
              Découvrez nos services et forfaits conçus pour répondre aux besoins des artistes 3D et des entreprises.
            </p>
          </div>
        </Container>
      </div>

      {/* Services Section */}
      <Section background="white" spacing="xl">
        <h2 className="text-3xl font-bold text-neutral-900 mb-12 text-center">Nos Services</h2>
        <Grid cols={1} mdCols={2} lgCols={3} gap={8}>
          {services.map((service, index) => (
            <div key={index} className="bg-white rounded-lg overflow-hidden shadow-sm border border-neutral-200 transition-shadow hover:shadow-md">
              <div className="h-48 overflow-hidden">
                <img
                  src={service.image}
                  alt={service.title}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-2">{service.title}</h3>
                <p className="text-neutral-600">{service.description}</p>
                <div className="mt-4">
                  <Button
                    variant="outline"
                    onClick={() => navigate('/lists-independants')}
                  >
                    Trouver un expert
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </Grid>
      </Section>

      {/* Pricing Section */}
      <Section background="light" spacing="xl">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-neutral-900 mb-6">Forfaits et Tarifs</h2>
          <p className="text-neutral-600 max-w-2xl mx-auto mb-8">
            Choisissez le forfait qui correspond le mieux à vos besoins et à votre budget.
          </p>

          {/* Toggle between monthly and yearly billing */}
          <div className="flex items-center justify-center mb-8">
            <span className={`mr-3 ${billingPeriod === 'monthly' ? 'font-semibold text-neutral-900' : 'text-neutral-500'}`}>
              Mensuel
            </span>
            <button
              type="button"
              className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none ${
                billingPeriod === 'yearly' ? 'bg-primary-600' : 'bg-neutral-200'
              }`}
              onClick={() => setBillingPeriod(billingPeriod === 'monthly' ? 'yearly' : 'monthly')}
            >
              <span className="sr-only">Changer la période de facturation</span>
              <span
                className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                  billingPeriod === 'yearly' ? 'translate-x-5' : 'translate-x-0'
                }`}
              />
            </button>
            <span className={`ml-3 ${billingPeriod === 'yearly' ? 'font-semibold text-neutral-900' : 'text-neutral-500'}`}>
              Annuel <span className="text-primary-600 font-medium">(-25%)</span>
            </span>
          </div>
        </div>

        <Grid cols={1} mdCols={3} gap={8}>
          {pricingPlans.map((plan, index) => (
            <div
              key={index}
              className={`bg-white rounded-lg overflow-hidden shadow-sm border ${
                plan.popular ? 'border-primary-500 ring-2 ring-primary-500 ring-opacity-50' : 'border-neutral-200'
              } transition-shadow hover:shadow-md relative`}
            >
              {plan.popular && (
                <div className="absolute top-0 inset-x-0 bg-primary-500 text-white text-center py-1 text-sm font-medium">
                  Populaire
                </div>
              )}
              <div className={`p-6 ${plan.popular ? 'pt-8' : ''}`}>
                <h3 className="text-xl font-semibold mb-1">{plan.name}</h3>
                <p className="text-neutral-600 mb-4">{plan.description}</p>
                <div className="mb-6">
                  <span className="text-4xl font-bold">{plan.price[billingPeriod]}€</span>
                  <span className="text-neutral-500">/{billingPeriod === 'monthly' ? 'mois' : 'mois (facturé annuellement)'}</span>
                </div>
                <Button
                  variant={plan.popular ? 'primary' : 'outline'}
                  fullWidth
                  onClick={() => {
                    setSelectedPlan({
                      name: plan.name,
                      price: plan.price[billingPeriod]
                    });
                    setIsPaymentModalOpen(true);
                  }}
                >
                  {plan.cta}
                </Button>
              </div>
              <div className="border-t border-neutral-200 p-6">
                <h4 className="font-medium mb-4">Fonctionnalités incluses :</h4>
                <ul className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      {feature.included ? (
                        <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                      ) : (
                        <X className="h-5 w-5 text-neutral-300 mr-2 flex-shrink-0" />
                      )}
                      <span className={feature.included ? 'text-neutral-700' : 'text-neutral-400'}>
                        {feature.name}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </Grid>
      </Section>

      {/* FAQ Section */}
      <Section background="white" spacing="xl">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold text-neutral-900 mb-12 text-center">Questions Fréquentes</h2>
          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white rounded-lg border border-neutral-200 overflow-hidden">
                <div className="p-6">
                  <div className="flex items-start">
                    <HelpCircle className="h-6 w-6 text-primary-600 mr-3 flex-shrink-0 mt-0.5" />
                    <div>
                      <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
                      <p className="text-neutral-600">{faq.answer}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-12 text-center">
            <p className="text-neutral-600 mb-4">Vous avez d'autres questions ?</p>
            <Button
              variant="outline"
              onClick={() => navigate('/contact')}
            >
              Contactez-nous
            </Button>
          </div>
        </div>
      </Section>

      {/* CTA Section - Implémentation directe pour garantir la visibilité */}
      <div style={{ backgroundColor: '#2980b9', padding: '4rem 0', position: 'relative', zIndex: 1 }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem' }}>
          <div style={{ textAlign: 'center', maxWidth: '768px', margin: '0 auto' }}>
            <h2 style={{ fontSize: '1.875rem', fontWeight: 'bold', color: 'white', marginBottom: '1.5rem' }}>
              Prêt à commencer ?
            </h2>
            <p style={{ color: 'white', marginBottom: '2rem' }}>
              Rejoignez notre communauté d'artistes 3D et d'entreprises dès aujourd'hui et découvrez tout ce que Hi 3D Artist peut vous offrir.
            </p>
            <div style={{ display: 'flex', flexDirection: 'row', gap: '1rem', justifyContent: 'center', alignItems: 'center', flexWrap: 'wrap' }}>
              <button
                type="button"
                onClick={() => navigate('/register')}
                style={{
                  backgroundColor: 'white',
                  color: '#2980b9',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '9999px',
                  fontWeight: '500',
                  fontSize: '1rem',
                  border: 'none',
                  cursor: 'pointer',
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minWidth: '180px'
                }}
              >
                Créer un compte
              </button>
              <button
                type="button"
                onClick={() => navigate('/lists-independants')}
                style={{
                  backgroundColor: 'transparent',
                  color: 'white',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '9999px',
                  fontWeight: '500',
                  fontSize: '1rem',
                  border: '1px solid white',
                  cursor: 'pointer',
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minWidth: '180px'
                }}
              >
                Explorer les talents
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Modal */}
      <Modal
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        title={`Paiement - Plan ${selectedPlan?.name}`}
      >
        <div className="mt-4">
          <p className="text-gray-600 mb-4">
            Montant à payer : {selectedPlan?.price}€ / {billingPeriod === 'monthly' ? 'mois' : 'an'}
          </p>
          <StripeContainer selectedPlan={selectedPlan} />
        </div>
      </Modal>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default OffersPage;
