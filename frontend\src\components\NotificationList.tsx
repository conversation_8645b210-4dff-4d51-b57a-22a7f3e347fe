import React from 'react';

const NotificationList: React.FC = () => {
  const notifications = [
    {
      id: 1,
      title: "Nouvelle offre reçue",
      message: "Vous avez reçu une nouvelle offre pour votre projet.",
      date: "il y a 10 minutes",
    },
    {
      id: 2,
      title: "Message de <PERSON>",
      message: "John Doe a répondu à votre message.",
      date: "il y a 1 heure",
    },
    {
      id: 3,
      title: "Votre profil a été consulté",
      message: "Un recruteur a consulté votre profil professionnel.",
      date: "il y a 2 heures",
    },
  ];

  return (
    <div className="absolute right-0 mt-2 w-80 bg-white border rounded-lg shadow-md py-2 z-50">
      {notifications.length === 0 ? (
        <p className="text-center text-gray-500 py-4">Aucune notification.</p>
      ) : (
        notifications.map((notification) => (
          <div
            key={notification.id}
            className="px-4 py-3 border-b last:border-0 cursor-pointer hover:bg-gray-100"
          >
            <h3 className="text-sm font-semibold">{notification.title}</h3>
            <p className="text-xs text-gray-600">{notification.message}</p>
            <span className="block text-xs text-gray-500 mt-1">{notification.date}</span>
          </div>
        ))
      )}
    </div>
  );
};

export default NotificationList;