<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateServiceOfferRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     // Vérifier si l'utilisateur est le propriétaire du service
    //     $serviceOffer = $this->route('service_offer');
    //     return $serviceOffer && $serviceOffer->user_id === auth()->id();
    // }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules()
    {
        return [
            // Nom du service
            'title' => 'sometimes|string|max:255',

            // Catégorie (sélecteur de catégories prédéfinies)
            'categories' => 'sometimes|array',
            'categories.*' => 'string|max:255',

            // Description avec possibilité de mise en forme
            'description' => 'sometimes|string',

            // Prix de base (USD)
            'price' => 'sometimes|numeric|min:0',

            // Unité du prix
            'price_unit' => 'sometimes|string|in:per_image,per_sqm,per_project',

            // Durée moyenne de réalisation
            'average_duration' => 'sometimes|string|in:less_than_week,1_to_2_weeks,1_month,2_months,3_months',

            // Image de couverture
            'cover_image' => 'sometimes|file|max:10240|mimes:jpeg,png,jpg,gif,svg,webp',

            // Catégorie d'image
            'image_category' => 'sometimes|string|max:255',

            // Projet associé
            'associated_project_id' => 'sometimes|nullable|exists:dashboard_projects,id',

            // Champs existants
            'execution_time' => 'sometimes|string',
            'concepts' => 'sometimes|string',
            'revisions' => 'sometimes|string',
            'is_private' => 'sometimes|boolean',
            'status' => 'sometimes|string|in:published,draft,pending',
            'files' => 'sometimes|array',
            'files.*' => 'file|max:10240|mimes:jpeg,png,jpg,gif,svg,webp,pdf,doc,docx,xls,xlsx,ppt,pptx,zip,rar',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages()
    {
        return [
            'title.string' => 'Le nom du service doit être une chaîne de caractères.',
            'title.max' => 'Le nom du service ne doit pas dépasser 255 caractères.',

            'categories.array' => 'Les catégories doivent être un tableau.',
            'categories.*.string' => 'Chaque catégorie doit être une chaîne de caractères.',
            'categories.*.max' => 'Une catégorie ne doit pas dépasser 255 caractères.',

            'description.string' => 'La description doit être une chaîne de caractères.',

            'price.numeric' => 'Le prix doit être un nombre.',
            'price.min' => 'Le prix doit être un nombre positif.',

            'price_unit.in' => 'L\'unité du prix doit être : par image, par m² ou par projet.',

            'average_duration.in' => 'La durée doit être : moins d\'une semaine, 1 à 2 semaines, 1 mois, 2 mois ou 3 mois.',

            'cover_image.file' => 'L\'image de couverture doit être un fichier.',
            'cover_image.max' => 'L\'image de couverture ne doit pas dépasser 10 Mo.',
            'cover_image.mimes' => 'L\'image de couverture doit être au format : jpeg, png, jpg, gif, svg ou webp.',

            'associated_project_id.exists' => 'Le projet associé sélectionné n\'existe pas.',

            'status.in' => 'Le statut doit être l\'un des suivants : published, draft, pending.',
        ];
    }
}
