import React, { useState, useEffect, ChangeEvent } from 'react';
import { X, Check, ChevronRight, ChevronLeft, Upload } from 'lucide-react';
import Button from '../ui/Button';
import { API_BASE_URL } from '../../config';
import { profileService } from '../../services/profileService';
import { getAvatarUrl, getInitials } from '../../utils/avatarUtils';
import './ClientProfileCompletionModal.css';
import LocationSelector from '../../components/ui/LocationSelector';

interface ClientProfileCompletionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
}

interface ProfileFormData {
  first_name: string;
  last_name: string;
  phone: string;
  city: string;
  country: string;
  bio: string;
  company_name: string;
  industry: string;
  position: string;
  avatar?: File | null;
  address?: string;
}

const ClientProfileCompletionModal: React.FC<ClientProfileCompletionModalProps> = ({
  isOpen,
  onClose,
  onComplete,
}) => {
  const [step, setStep] = useState<number>(1);
  const [formData, setFormData] = useState<ProfileFormData>({
    first_name: '',
    last_name: '',
    phone: '',
    city: '',
    country: '',
    bio: '',
    company_name: '',
    industry: '',
    position: '',
    avatar: null,
    address: '',
  });
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Fetch existing profile data
    const fetchProfile = async () => {
      try {
        // Utiliser le service de profil réel
        const data = await profileService.getProfile();
        const profile = data.profile;
        console.log('Profil récupéré pour la complétion:', profile);

        // Pre-fill form with existing data
        setFormData(prev => ({
          ...prev,
          first_name: profile.first_name || '',
          last_name: profile.last_name || '',
          phone: profile.phone || '',
          city: profile.city || '',
          country: profile.country || '',
          bio: profile.bio || '',
          company_name: profile.company_name || '',
          industry: profile.industry || '',
          position: profile.position || '',
          address: profile.address || '',
        }));

        if (profile.avatar) {
          // Utiliser getAvatarUrl pour obtenir l'URL correcte de l'avatar
          setAvatarPreview(getAvatarUrl(profile.avatar) || '');
        }
      } catch (err) {
        console.error('Error fetching profile:', err);
      }
    };

    if (isOpen) {
      fetchProfile();
    }
  }, [isOpen]);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleAvatarChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setFormData(prev => ({
        ...prev,
        avatar: file,
      }));

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError(null);

    try {
      // Validation des champs requis
      if (!formData.first_name || !formData.last_name) {
        setError('Veuillez remplir tous les champs obligatoires (prénom et nom).');
        setLoading(false);
        return;
      }

      // Create FormData object for file upload
      const formDataToSend = new FormData();

      // Append all text fields
      Object.entries(formData).forEach(([key, value]) => {
        if (key === 'avatar') {
          return; // Skip this for now
        }

        if (key === 'social_links' && value) {
          formDataToSend.append(key, JSON.stringify(value));
        } else if (value !== null && value !== undefined) {
          formDataToSend.append(key, value as string);
        }
      });

      // Append avatar if it exists
      if (formData.avatar) {
        formDataToSend.append('avatar', formData.avatar);
      }

      // Add a flag to indicate this is a profile completion
      formDataToSend.append('is_completion', 'true');

      console.log('Envoi des données de complétion de profil...');
      // Log des données envoyées
      Array.from(formDataToSend.entries()).forEach(([key, value]) => {
        console.log(`${key}: ${typeof value === 'string' ? value : '[File]'}`);
      });

      try {
        // Utiliser le service de profil réel
        const result = await profileService.completeProfile(formDataToSend);
        console.log('Profil complété avec succès:', result);

        // Call onComplete callback
        onComplete();
      } catch (apiError) {
        console.error('Erreur lors de la complétion du profil:', apiError);

        // Afficher l'erreur à l'utilisateur
        if (apiError instanceof Error) {
          try {
            const errorData = JSON.parse(apiError.message);
            setError(errorData.message || 'Une erreur est survenue lors de la complétion du profil.');
          } catch {
            setError(apiError.message || 'Une erreur est survenue lors de la complétion du profil.');
          }
        } else {
          setError('Une erreur est survenue lors de la complétion du profil. Veuillez réessayer.');
        }
      }
    } catch (err) {
      console.error('Error completing profile:', err);
      setError('Une erreur est survenue lors de la complétion du profil. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  const nextStep = () => {
    setStep(prev => Math.min(prev + 1, 3));
  };

  const prevStep = () => {
    setStep(prev => Math.max(prev - 1, 1));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-neutral-200 flex justify-between items-center">
          <h2 className="text-xl font-bold text-neutral-900">Complétez votre profil</h2>
          <button
            type="button"
            onClick={onClose}
            className="text-neutral-500 hover:text-neutral-700 focus:outline-none"
            title="Fermer"
            aria-label="Fermer"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Progress indicator */}
        <div className="px-6 py-3 bg-neutral-50 border-b border-neutral-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                step >= 1 ? 'bg-primary-600 text-white' : 'bg-neutral-200 text-neutral-500'
              }`}>
                {step > 1 ? <Check className="h-5 w-5" /> : 1}
              </div>
              <div className={`h-1 w-12 ${
                step > 1 ? 'bg-primary-600' : 'bg-neutral-200'
              }`}></div>
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                step >= 2 ? 'bg-primary-600 text-white' : 'bg-neutral-200 text-neutral-500'
              }`}>
                {step > 2 ? <Check className="h-5 w-5" /> : 2}
              </div>
              <div className={`h-1 w-12 ${
                step > 2 ? 'bg-primary-600' : 'bg-neutral-200'
              }`}></div>
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                step >= 3 ? 'bg-primary-600 text-white' : 'bg-neutral-200 text-neutral-500'
              }`}>
                3
              </div>
            </div>
            <div className="text-sm text-neutral-500">
              Étape {step} sur 3
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 modal-content">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-red-700">
              {error}
            </div>
          )}

          {step === 1 && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-neutral-900 mb-2">Informations personnelles</h3>
                <p className="text-neutral-600 mb-4">
                  Ces informations nous aideront à personnaliser votre expérience.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="first_name" className="block text-sm font-medium text-neutral-700 mb-1">
                    Prénom *
                  </label>
                  <input
                    type="text"
                    id="first_name"
                    name="first_name"
                    value={formData.first_name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div>
                  <label htmlFor="last_name" className="block text-sm font-medium text-neutral-700 mb-1">
                    Nom *
                  </label>
                  <input
                    type="text"
                    id="last_name"
                    name="last_name"
                    value={formData.last_name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-neutral-700 mb-1">
                     Téléphone
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div className="md:col-span-2">
                  <LocationSelector
                    country={formData.country}
                    city={formData.city}
                    address={formData.address || ''}
                    onCountryChange={(country: string) => setFormData({ ...formData, country })}
                    onCityChange={(city: string) => setFormData({ ...formData, city })}
                    onAddressChange={(address: string) => setFormData({ ...formData, address })}
                  />
                </div>
              </div>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-neutral-900 mb-2">Informations professionnelles</h3>
                <p className="text-neutral-600 mb-4">
                  Ces informations nous aideront à vous connecter avec les professionnels adaptés à vos besoins.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="company_name" className="block text-sm font-medium text-neutral-700 mb-1">
                    Nom de l'entreprise
                  </label>
                  <input
                    type="text"
                    id="company_name"
                    name="company_name"
                    value={formData.company_name}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div>
                  <label htmlFor="position" className="block text-sm font-medium text-neutral-700 mb-1">
                    Poste
                  </label>
                  <input
                    type="text"
                    id="position"
                    name="position"
                    value={formData.position}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div className="md:col-span-2">
                  <label htmlFor="industry" className="block text-sm font-medium text-neutral-700 mb-1">
                    Secteur d'activité
                  </label>
                  <select
                    id="industry"
                    name="industry"
                    value={formData.industry}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="">Sélectionnez un secteur</option>
                    <option value="Technology">Technologie</option>
                    <option value="Finance">Finance</option>
                    <option value="Healthcare">Santé</option>
                    <option value="Education">Éducation</option>
                    <option value="Retail">Commerce de détail</option>
                    <option value="Manufacturing">Fabrication</option>
                    <option value="Media">Médias</option>
                    <option value="Construction">Construction</option>
                    <option value="Transportation">Transport</option>
                    <option value="Energy">Énergie</option>
                    <option value="Other">Autre</option>
                  </select>
                </div>
              </div>

              <div>
                <label htmlFor="bio" className="block text-sm font-medium text-neutral-700 mb-1">
                  Biographie 
                </label>
                <textarea
                  id="bio"
                  name="bio"
                  value={formData.bio}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Parlez-nous de vous et de vos besoins en matière de projets..."
                ></textarea>
              </div>
            </div>
          )}

          {step === 3 && (
            <div className="space-y-6">
              {/* <div>
                <h3 className="text-lg font-medium text-neutral-900 mb-2">Photo de profil</h3>
                <p className="text-neutral-600 mb-4">
                  Ajoutez une photo de profil pour personnaliser votre compte.
                </p>
              </div> */}

              {/* <div className="flex flex-col items-center">
                <div className="relative mb-6">
                  <div className="w-32 h-32 rounded-full overflow-hidden bg-neutral-200 flex items-center justify-center">
                    {avatarPreview ? (
                      <img
                        src={avatarPreview}
                        alt="Avatar preview"
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          console.error('Erreur de chargement de l\'avatar:', e);
                          // En cas d'erreur, afficher les initiales
                          e.currentTarget.style.display = 'none';
                          // Forcer le rendu des initiales
                          setAvatarPreview(null);
                        }}
                      />
                    ) : (
                      <span className="text-4xl font-bold text-neutral-400">
                        {getInitials(formData.first_name, formData.last_name)}
                      </span>
                    )}
                  </div>
                </div>

                <label className="inline-flex items-center px-4 py-2 border border-neutral-300 rounded-md shadow-sm text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 cursor-pointer">
                  <Upload className="h-4 w-4 mr-2" />
                  Choisir une image
                  <input
                    type="file"
                    className="hidden"
                    accept="image/*"
                    onChange={handleAvatarChange}
                  />
                </label>

                <p className="text-xs text-neutral-500 mt-2">
                  Formats acceptés : JPG, PNG. Taille maximale : 2 Mo.
                </p>
              </div> */}

              <div className="mt-8 pt-6 border-t border-neutral-200">
                <h4 className="font-medium text-neutral-900 mb-2">Récapitulatif</h4>
                <div className="bg-neutral-50 rounded-lg p-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-neutral-700">Nom complet</p>
                      <p className="text-sm text-neutral-600">{formData.first_name} {formData.last_name}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-neutral-700">Téléphone</p>
                      <p className="text-sm text-neutral-600">{formData.phone || 'Non spécifié'}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-neutral-700">Localisation</p>
                      <p className="text-sm text-neutral-600">
                        {formData.city && formData.country
                          ? `${formData.city}, ${formData.country}`
                          : 'Non spécifiée'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-neutral-700">Entreprise</p>
                      <p className="text-sm text-neutral-600">{formData.company_name || 'Non spécifiée'}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-neutral-700">Poste</p>
                      <p className="text-sm text-neutral-600">{formData.position || 'Non spécifié'}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-neutral-700">Secteur</p>
                      <p className="text-sm text-neutral-600">{formData.industry || 'Non spécifié'}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-neutral-200 flex justify-between">
          <Button
            variant="outline"
            onClick={step === 1 ? onClose : prevStep}
            leftIcon={step > 1 ? <ChevronLeft className="h-4 w-4" /> : undefined}
          >
            {step === 1 ? 'Ignorer' : 'Précédent'}
          </Button>

          <Button
            variant="primary"
            onClick={step < 3 ? nextStep : handleSubmit}
            rightIcon={step < 3 ? <ChevronRight className="h-4 w-4" /> : undefined}
            disabled={loading}
            className="primary-button"
          >
            {step < 3 ? 'Suivant' : (loading ? 'Enregistrement...' : 'Terminer')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ClientProfileCompletionModal;
