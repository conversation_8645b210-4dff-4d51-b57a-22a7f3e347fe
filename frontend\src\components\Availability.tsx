// import React, { useState } from "react";
// import { useProfile } from "./ProfileContext";
// import { API_BASE_URL } from '../config';

// const Availability = () => {

//   const [storedProfile, setProfile] = useProfile();
//   // const storedProfile = JSON.parse(localStorage.getItem("userProfile") || "{}");
//   const initialAvailability = storedProfile?.profile_data?.availability || '';
//   const initialAvailabilityResponseTime = storedProfile?.profile_data?.estimated_response_time || '';
  
//   const [availability_status, setAvailabilityStatus] = useState<string>(initialAvailability);
//   const [estimated_response_time, setEstimatedResponseTime] = useState<string>(initialAvailabilityResponseTime);
  
//   const [loading, setLoading] = useState(false);
//   const [message, setMessage] = useState("");

//   const handleAvailabilityChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
//     setAvailabilityStatus(e.target.value);
//     if (e.target.value === "available") {
//       setEstimatedResponseTime("");
//     }
//   };

//   const handleEstimatedResponseTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     setEstimatedResponseTime(e.target.value);
//   };

//   const handleSubmit = async () => {
//     setLoading(true);
//     setMessage("");

//     const token = localStorage.getItem("token");

//     const requestBody: any = {
//       availability_status,
//     };

//     if (availability_status === "unavailable") {
//       const formattedDate = estimated_response_time 
//       ? new Date(estimated_response_time).toISOString().slice(0, 19).replace("T", " ")
//       : null;

//       requestBody.estimated_response_time = formattedDate;
//       // requestBody.estimated_response_time = estimated_response_time;
//     }

//     try {
//       const response = await fetch(
//         `${API_BASE_URL}/api/profile/completion/availability`,
//         {
//           method: "PUT",
//           headers: {
//             Authorization: `Bearer ${token}`,
//             "Content-Type": "application/json",
//           },
//           body: JSON.stringify(requestBody),
//         }
//       );
//       console.log('Donné envoyée :',JSON.stringify(requestBody));
      
//       const data = await response.json();
//       console.log('Donné Retour :',data);
//       if (response.ok) {
//         setMessage("✅ Disponibilité mise à jour avec succès !");
//         const updatedProfile = {
//           ...storedProfile,
//           profile_data: {
//             ...storedProfile.profile_data,
//             availability_status,
//             estimated_response_time: availability_status === "unavailable" ? estimated_response_time : "",
//           },
//         };
//         localStorage.setItem("userProfile", JSON.stringify(updatedProfile));
//         setProfile(updatedProfile);
//       } else {
//         setMessage("❌ Erreur : " + (data.message || "Impossible d'enregistrer la disponibilité."));
//       }
//     } catch (error) {
//       setMessage("❌ Erreur réseau, veuillez réessayer.");
//     } finally {
//       setLoading(false);
//     }
//   };

//   return (
//     <div className="min-h-screen bg-white px-4 sm:px-6 lg:px-8">
//       <h2 className="text-sm font-bold text-gray-700 mb-4">DISPONIBILITÉ</h2>

//       {message && (
//         <div className={`mb-4 p-2 text-white rounded ${message.startsWith("✅") ? "bg-green-500" : "bg-red-500"}`}>
//           {message}
//         </div>
//       )}

//       <div className="space-y-4">
//         <div className="grid grid-cols-1 gap-4">
//           <select
//             id="availability_status"
//             value={availability_status}
//             onChange={handleAvailabilityChange}
//             className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
//           >
//             <option value="available">Disponible</option>
//             <option value="unavailable">Non Disponible</option>
//           </select>
//         </div>

//         {availability_status === "unavailable" && (
//           <div>
//             <label htmlFor="estimated_response_time" className="block text-sm font-medium text-gray-700">
//               Temps de réponse estimé
//             </label>
//             <input
//               id="estimated_response_time"
//               type="datetime-local"
//               value={estimated_response_time}
//               onChange={handleEstimatedResponseTimeChange}
//               className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
//             />
//           </div>
//         )}

//         <div className="mt-4">
//           <button
//             type="button"
//             onClick={handleSubmit}
//             className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition"
//             disabled={loading}
//           >
//             {loading ? "Envoi en cours..." : "Enregistrer la disponibilité"}
//           </button>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default Availability;

import React, { useState } from "react";
import { useProfile } from "./ProfileContext"; // Importer le contexte
import { API_BASE_URL } from '../config';

const Availability = () => {
  const { profile, setProfile } = useProfile(); // 🔥 Utiliser les données globales
  const storedProfile = JSON.parse(localStorage.getItem("userProfile") || "{}");
  const initialAvailability = storedProfile?.profile_data?.availability_status || '';
  const initialAvailabilityResponseTime = storedProfile?.profile_data?.estimated_response_time || '';
  
  const [availability_status, setAvailabilityStatus] = useState<string>(initialAvailability);
  const [estimated_response_time, setEstimatedResponseTime] = useState<string>(initialAvailabilityResponseTime);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");

  const handleAvailabilityChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setAvailabilityStatus(e.target.value);
    if (e.target.value === "available") {
      setEstimatedResponseTime("");
    }
  };

  const handleEstimatedResponseTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEstimatedResponseTime(e.target.value);
  };

  const handleSubmit = async () => {
    setLoading(true);
    setMessage("");

    const token = localStorage.getItem("token");

    const requestBody: any = { availability_status };

    if (availability_status === "unavailable") {
      const formattedDate = estimated_response_time 
        ? new Date(estimated_response_time).toISOString().slice(0, 19).replace("T", " ")
        : null;

      requestBody.estimated_response_time = formattedDate;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/api/profile/completion/availability`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      console.log('Donné envoyée :', JSON.stringify(requestBody));

      const data = await response.json();
      console.log('Donné Retour :', data);

      if (response.ok) {
        setMessage("✅ Disponibilité mise à jour avec succès !");
        
        const updatedProfile = {
          ...storedProfile,
          profile_data: {
            ...storedProfile.profile_data,
            availability_status,
            estimated_response_time: availability_status === "unavailable" ? requestBody.estimated_response_time : "",
          },
        };

        const updateJiab = {
          ...storedProfile,
          profile_data: {
            ...storedProfile.profile_data,
            availability_status,
            estimated_response_time: availability_status === "unavailable" ? requestBody.estimated_response_time : "",
          },};

        localStorage.setItem("userProfile", JSON.stringify(updatedProfile));
        setProfile(updateJiab); // 🔥 Met à jour le contexte et force l'actualisation de la page
      } else {
        setMessage("❌ Erreur : " + (data.message || "Impossible d'enregistrer la disponibilité."));
      }
    } catch (error) {
      setMessage("❌ Erreur réseau, veuillez réessayer.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white px-4 sm:px-6 lg:px-8">
      <h2 className="text-sm font-bold text-gray-700 mb-4">DISPONIBILITÉ</h2>

      {message && (
        <div className={`mb-4 p-2 text-white rounded ${message.startsWith("✅") ? "bg-green-500" : "bg-red-500"}`}>
          {message}
        </div>
      )}

      <div className="space-y-4">
        <div className="grid grid-cols-1 gap-4">
          <select
            id="availability_status"
            value={availability_status}
            onChange={handleAvailabilityChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="available">Disponible</option>
            <option value="unavailable">Non Disponible</option>
          </select>
        </div>

        {availability_status === "unavailable" && (
          <div>
            <label htmlFor="estimated_response_time" className="block text-sm font-medium text-gray-700">
              Temps de réponse estimé
            </label>
            <input
              id="estimated_response_time"
              type="datetime-local"
              value={estimated_response_time}
              onChange={handleEstimatedResponseTimeChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        )}

        <div className="mt-4">
          <button
            type="button"
            onClick={handleSubmit}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition"
            disabled={loading}
          >
            {loading ? "Envoi en cours..." : "Enregistrer la disponibilité"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Availability;

