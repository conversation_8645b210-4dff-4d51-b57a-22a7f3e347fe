import React, { useState, useEffect, useRef } from 'react';
import { X, Menu, ChevronDown, ChevronRight } from 'lucide-react';
import { Link } from 'react-router-dom';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  links: {
    label: string;
    href: string;
    children?: { label: string; href: string }[];
  }[];
  authButtons?: React.ReactNode;
}

const MobileMenu: React.FC<MobileMenuProps> = ({
  isOpen,
  onClose,
  links,
  authButtons,
}) => {
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Prevent scrolling when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 lg:hidden">
      <div 
        ref={menuRef}
        className="fixed inset-y-0 right-0 max-w-xs w-full bg-white shadow-xl flex flex-col h-full"
      >
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="font-semibold text-lg">Menu</h2>
          <button 
            onClick={onClose}
            className="p-2 rounded-full hover:bg-neutral-100"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="flex-1 overflow-y-auto">
          <nav className="px-4 py-2">
            <ul className="space-y-1">
              {links.map((link) => (
                <li key={link.label}>
                  {link.children ? (
                    <div>
                      <button
                        className="flex items-center justify-between w-full py-3 px-2 rounded-md hover:bg-neutral-100"
                        onClick={() => setOpenSubmenu(openSubmenu === link.label ? null : link.label)}
                      >
                        <span>{link.label}</span>
                        {openSubmenu === link.label ? (
                          <ChevronDown className="h-5 w-5" />
                        ) : (
                          <ChevronRight className="h-5 w-5" />
                        )}
                      </button>
                      
                      {openSubmenu === link.label && (
                        <ul className="pl-4 py-2 space-y-1">
                          {link.children.map((child) => (
                            <li key={child.label}>
                              <Link
                                to={child.href}
                                className="block py-2 px-3 rounded-md hover:bg-neutral-100"
                                onClick={onClose}
                              >
                                {child.label}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  ) : (
                    <Link
                      to={link.href}
                      className="block py-3 px-2 rounded-md hover:bg-neutral-100"
                      onClick={onClose}
                    >
                      {link.label}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </nav>
        </div>

        {authButtons && (
          <div className="p-4 border-t">
            {authButtons}
          </div>
        )}
      </div>
    </div>
  );
};

export default MobileMenu;
