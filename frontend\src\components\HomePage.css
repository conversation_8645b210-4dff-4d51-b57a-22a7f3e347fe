/* Styles pour la section CTA */
.cta-section {
  position: relative;
  overflow: hidden;
}

.cta-background {
  position: absolute;
  inset: 0;
  background-size: cover;
  background-position: center;
  z-index: 0;
}

.cta-overlay {
  position: absolute;
  inset: 0;
  background-color: #2980b9;
  opacity: 0.9;
}

.cta-content {
  position: relative;
  z-index: 10;
  padding: 4rem 0;
}

.cta-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.cta-text-center {
  text-align: center;
}

.cta-title {
  font-size: 2rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
}

.cta-description {
  color: white;
  margin-bottom: 2rem;
  max-width: 42rem;
  margin: 0 auto 2rem;
}

.cta-buttons {
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.cta-button-primary {
  background-color: white;
  color: #2980b9;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  font-weight: 600;
  font-size: 1rem;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 180px;
  transition: background-color 0.3s, transform 0.2s;
  text-shadow: none;
}

.cta-button-primary:hover {
  background-color: #f8f9fa;
  transform: translateY(-2px);
}

.cta-button-secondary {
  background-color: transparent;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  font-weight: 500;
  font-size: 1rem;
  border: 1px solid white;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 180px;
  transition: background-color 0.3s, transform 0.2s;
}

.cta-button-secondary:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* Style spécifique pour le bouton "Commencer gratuitement" */
.homepage-cta-button {
  color: white !important;
  font-weight: 600 !important;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
}
