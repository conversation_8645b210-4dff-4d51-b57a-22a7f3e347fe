import React, { useState, useEffect, ChangeEvent } from 'react';
import { useNavigate } from 'react-router-dom';
import { Save, X, Upload, Trash2 } from 'lucide-react';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../ui/Button';
import { API_BASE_URL } from '../../config';
import { profileService } from '../../services/profileService';
import { getAvatarUrl, getInitials } from '../../utils/avatarUtils';
import { useToast } from '../../context/ToastContext';
import LocationSelector from '../ui/LocationSelector';

interface ClientProfileFormData {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  country: string;
  bio: string;
  birth_date: string;
  company_name: string;
  company_size: string;
  industry: string;
  position: string;
  website: string;
  social_links: {
    linkedin: string;
    twitter: string;
    facebook: string;
    instagram: string;
  };
  avatar?: File | null;
  current_avatar?: string;
}

function formatDateForInput(dateString: string | null | undefined): string {
  if (!dateString) return '';
  // Prend juste la partie YYYY-MM-DD
  return dateString.split('T')[0] || dateString.split(' ')[0];
}

const ClientProfileEditPage: React.FC = () => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const [formData, setFormData] = useState<ClientProfileFormData>({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    country: '',
    bio: '',
    birth_date: '',
    company_name: '',
    company_size: '',
    industry: '',
    position: '',
    website: '',
    social_links: {
      linkedin: '',
      twitter: '',
      facebook: '',
      instagram: '',
    },
    avatar: null,
    current_avatar: '',
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [saving, setSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);

  useEffect(() => {
    const fetchProfile = async () => {
      setLoading(true);
      try {
        // Utiliser le service profileService pour récupérer les données du profil
        const data = await profileService.getProfile();
        const profile = data.profile;

        // Remplir le formulaire avec les données du profil
        setFormData({
          first_name: profile.first_name || '',
          last_name: profile.last_name || '',
          email: profile.email || '',
          phone: profile.phone || '',
          address: profile.address || '',
          city: profile.city || '',
          country: profile.country || '',
          bio: profile.bio || '',
          birth_date: formatDateForInput(profile.birth_date),
          company_name: profile.company_name || '',
          company_size: profile.company_size || '',
          industry: profile.industry || '',
          position: profile.position || '',
          website: profile.website || '',
          social_links: {
            linkedin: profile.social_links?.linkedin || '',
            twitter: profile.social_links?.twitter || '',
            facebook: profile.social_links?.facebook || '',
            instagram: profile.social_links?.instagram || '',
          },
          avatar: null,
          current_avatar: profile.avatar || '',
        });

        if (profile.avatar) {
          // Utiliser getAvatarUrl pour obtenir l'URL correcte de l'avatar
          setAvatarPreview(getAvatarUrl(profile.avatar) || '');
        }

        setError(null);
        console.log('Profil récupéré avec succès:', profile);
      } catch (err) {
        console.error('Error fetching profile:', err);
        setError('Impossible de charger le profil. Veuillez réessayer plus tard.');
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => {
        // Vérifier que la propriété parent est bien un objet
        const parentObj = prev[parent as keyof ClientProfileFormData];
        if (typeof parentObj === 'object' && parentObj !== null) {
          return {
            ...prev,
            [parent]: {
              ...parentObj,
              [child]: value,
            },
          };
        }
        return prev; // Si ce n'est pas un objet, retourner l'état précédent sans modification
      });
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleAvatarChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setFormData(prev => ({
        ...prev,
        avatar: file,
      }));

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeAvatar = () => {
    setFormData(prev => ({
      ...prev,
      avatar: null,
      current_avatar: '',
    }));
    setAvatarPreview(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    try {
      console.log('Préparation des données à envoyer...');

      // Si nous avons un avatar, utiliser FormData
      if (formData.avatar) {
        // Create FormData object for file upload
        const formDataToSend = new FormData();

        // Préparer une copie des données sans l'avatar pour la convertir en JSON
        const dataWithoutAvatar = { ...formData };
        delete dataWithoutAvatar.avatar;
        delete dataWithoutAvatar.current_avatar;

        // Ajouter les données JSON comme un champ unique
        formDataToSend.append('profile_data', JSON.stringify(dataWithoutAvatar));
        console.log('Profile data JSON:', JSON.stringify(dataWithoutAvatar));

        // Ajouter l'avatar séparément
        formDataToSend.append('avatar', formData.avatar);
        console.log('Avatar added to form data');

        console.log('Envoi des données au serveur avec FormData (pour l\'avatar)...');

        try {
          const response = await profileService.updateProfileWithAvatar(formDataToSend);
          console.log('Profil mis à jour avec succès:', response);

          // Afficher une alerte de succès
          showToast('success', 'Profil mis à jour avec succès!');

          // Rediriger vers la page de profil
          navigate('/dashboard/client-profile');
        } catch (apiError) {
          console.error('Erreur API lors de la mise à jour du profil avec avatar:', apiError);
          const errorMessage = apiError instanceof Error
            ? apiError.message
            : 'Erreur inconnue';
          setError(`Erreur API: ${errorMessage}`);
        }
      } else {
        // Pas d'avatar, on peut envoyer directement les données JSON
        console.log('Envoi des données au serveur en JSON...');

        // Juste avant l'envoi
        const dataToSend = { ...formData };
        delete dataToSend.avatar; // On ne l'envoie pas si pas de nouvelle image

        // Si le backend a besoin de current_avatar pour garder l'ancienne image, gardez-le
        // Sinon, vous pouvez aussi le supprimer si ce n'est pas nécessaire
        // delete dataToSend.current_avatar;

        try {
          const response = await profileService.updateProfileJSON(dataToSend);
          console.log('Profil mis à jour avec succès:', response);

          // Afficher une alerte de succès
          showToast('success', 'Profil mis à jour avec succès!');

          // Rediriger vers la page de profil
          navigate('/dashboard/client-profile');
        } catch (apiError) {
          console.error('Erreur API lors de la mise à jour du profil JSON:', apiError);
          const errorMessage = apiError instanceof Error
            ? apiError.message
            : 'Erreur inconnue';
          setError(`Erreur API: ${errorMessage}`);
        }
      }
    } catch (err) {
      console.error('Erreur lors de la mise à jour du profil:', err);
      setError('Impossible de mettre à jour le profil. Veuillez réessayer plus tard.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Modifier mon profil"
      subtitle="Mettez à jour vos informations personnelles"
      actions={
        <div className="flex space-x-3">
          <Button
            variant="outline"
            leftIcon={<X className="h-5 w-5" />}
            onClick={() => navigate('/dashboard/client-profile')}
            style={{ color: 'black' }}
          >
            Annuler
          </Button>
          <Button
            variant="primary"
            leftIcon={<Save className="h-5 w-5" />}
            onClick={handleSubmit}
            disabled={saving}
            style={{ backgroundColor: '#2980b9', color: 'black' }}
          >
            {saving ? 'Enregistrement...' : 'Enregistrer'}
          </Button>
        </div>
      }
    >
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-red-700">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Avatar upload */}
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-neutral-200">
            <h2 className="text-lg font-semibold text-neutral-900">Photo de profil</h2>
          </div>
          <div className="p-6">
            <div className="flex flex-col sm:flex-row items-start sm:items-center">
              <div className="relative mb-4 sm:mb-0 sm:mr-6">
                <div className="w-24 h-24 rounded-full overflow-hidden bg-neutral-200 flex items-center justify-center">
                  {avatarPreview ? (
                    <img
                      src={avatarPreview}
                      alt="Avatar preview"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        console.error('Erreur de chargement de l\'avatar:', e);
                        // En cas d'erreur, afficher les initiales
                        e.currentTarget.style.display = 'none';
                        // Forcer le rendu des initiales
                        setAvatarPreview(null);
                      }}
                    />
                  ) : (
                    <span className="text-3xl font-bold text-neutral-400">
                      {getInitials(formData.first_name, formData.last_name)}
                    </span>
                  )}
                </div>
              </div>
              <div className="flex flex-col space-y-3">
                <label className="inline-flex items-center px-4 py-2 border border-neutral-300 rounded-md shadow-sm text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 cursor-pointer">
                  <Upload className="h-4 w-4 mr-2" />
                  Choisir une image
                  <input
                    type="file"
                    className="hidden"
                    accept="image/*"
                    onChange={handleAvatarChange}
                  />
                </label>
                {avatarPreview && (
                  <button
                    type="button"
                    onClick={removeAvatar}
                    className="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Supprimer
                  </button>
                )}
                <p className="text-xs text-neutral-500">
                  Formats acceptés : JPG, PNG. Taille maximale : 2 Mo.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Personal information */}
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-neutral-200">
            <h2 className="text-lg font-semibold text-neutral-900">Informations personnelles</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="first_name" className="block text-sm font-medium text-neutral-700 mb-1">
                  Prénom *
                </label>
                <input
                  type="text"
                  id="first_name"
                  name="first_name"
                  value={formData.first_name}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label htmlFor="last_name" className="block text-sm font-medium text-neutral-700 mb-1">
                  Nom *
                </label>
                <input
                  type="text"
                  id="last_name"
                  name="last_name"
                  value={formData.last_name}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-neutral-700 mb-1">
                  Email *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-neutral-700 mb-1">
                  Téléphone
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label htmlFor="birth_date" className="block text-sm font-medium text-neutral-700 mb-1">
                  Date de naissance
                </label>
                <input
                  type="date"
                  id="birth_date"
                  name="birth_date"
                  value={formData.birth_date}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>

            <div className="mt-6">
              <label htmlFor="bio" className="block text-sm font-medium text-neutral-700 mb-1">
                Biographie
              </label>
              <textarea
                id="bio"
                name="bio"
                value={formData.bio}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Parlez-nous de vous..."
              ></textarea>
            </div>
          </div>
        </div>

        {/* Address */}
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm">
          <div className="px-6 py-4 border-b border-neutral-200">
            <h2 className="text-lg font-semibold text-neutral-900">Adresse</h2>
          </div>
          <div className="p-6">
            <LocationSelector
              country={formData.country}
              city={formData.city}
              address={formData.address}
              onCountryChange={(country) => setFormData({ ...formData, country })}
              onCityChange={(city) => setFormData({ ...formData, city })}
              onAddressChange={(address) => setFormData({ ...formData, address })}
            />
          </div>
        </div>

        {/* Company information */}
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-neutral-200">
            <h2 className="text-lg font-semibold text-neutral-900">Informations professionnelles</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="company_name" className="block text-sm font-medium text-neutral-700 mb-1">
                  Nom de l'entreprise
                </label>
                <input
                  type="text"
                  id="company_name"
                  name="company_name"
                  value={formData.company_name}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label htmlFor="position" className="block text-sm font-medium text-neutral-700 mb-1">
                  Poste
                </label>
                <input
                  type="text"
                  id="position"
                  name="position"
                  value={formData.position}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label htmlFor="industry" className="block text-sm font-medium text-neutral-700 mb-1">
                  Secteur d'activité
                </label>
                <select
                  id="industry"
                  name="industry"
                  value={formData.industry}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Sélectionnez un secteur</option>
                  <option value="Technology">Technologie</option>
                  <option value="Finance">Finance</option>
                  <option value="Healthcare">Santé</option>
                  <option value="Education">Éducation</option>
                  <option value="Retail">Commerce de détail</option>
                  <option value="Manufacturing">Fabrication</option>
                  <option value="Media">Médias</option>
                  <option value="Construction">Construction</option>
                  <option value="Transportation">Transport</option>
                  <option value="Energy">Énergie</option>
                  <option value="Other">Autre</option>
                </select>
              </div>
              <div>
                <label htmlFor="company_size" className="block text-sm font-medium text-neutral-700 mb-1">
                  Taille de l'entreprise
                </label>
                <select
                  id="company_size"
                  name="company_size"
                  value={formData.company_size}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Sélectionnez une taille</option>
                  <option value="1-10">1-10 employés</option>
                  <option value="11-50">11-50 employés</option>
                  <option value="51-200">51-200 employés</option>
                  <option value="201-500">201-500 employés</option>
                  <option value="501-1000">501-1000 employés</option>
                  <option value="1001+">1001+ employés</option>
                </select>
              </div>
              <div className="md:col-span-2">
                <label htmlFor="website" className="block text-sm font-medium text-neutral-700 mb-1">
                  Site web
                </label>
                <input
                  type="url"
                  id="website"
                  name="website"
                  value={formData.website}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="https://..."
                />
              </div>
            </div>
          </div>
        </div>

        {/* Social links */}
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-neutral-200">
            <h2 className="text-lg font-semibold text-neutral-900">Réseaux sociaux</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="social_links.linkedin" className="block text-sm font-medium text-neutral-700 mb-1">
                  LinkedIn
                </label>
                <input
                  type="url"
                  id="social_links.linkedin"
                  name="social_links.linkedin"
                  value={formData.social_links.linkedin}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="https://linkedin.com/in/..."
                />
              </div>
              <div>
                <label htmlFor="social_links.twitter" className="block text-sm font-medium text-neutral-700 mb-1">
                  Twitter
                </label>
                <input
                  type="url"
                  id="social_links.twitter"
                  name="social_links.twitter"
                  value={formData.social_links.twitter}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="https://twitter.com/..."
                />
              </div>
              <div>
                <label htmlFor="social_links.facebook" className="block text-sm font-medium text-neutral-700 mb-1">
                  Facebook
                </label>
                <input
                  type="url"
                  id="social_links.facebook"
                  name="social_links.facebook"
                  value={formData.social_links.facebook}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="https://facebook.com/..."
                />
              </div>
              <div>
                <label htmlFor="social_links.instagram" className="block text-sm font-medium text-neutral-700 mb-1">
                  Instagram
                </label>
                <input
                  type="url"
                  id="social_links.instagram"
                  name="social_links.instagram"
                  value={formData.social_links.instagram}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="https://instagram.com/..."
                />
              </div>
            </div>
          </div>
        </div>

        {/* Submit buttons */}
        <div className="flex justify-end space-x-3">
          <Button
            variant="outline"
            type="button"
            onClick={() => navigate('/dashboard/client-profile')}
          >
            Annuler
          </Button>
          <Button
            variant="primary"
            type="button"
            onClick={handleSubmit}
            disabled={saving}
            style={{ backgroundColor: '#2980b9', color: 'white', fontWeight: 'bold' }}
          >
            {saving ? 'Enregistrement...' : 'Enregistrer les modifications'}
          </Button>
        </div>
      </form>
    </DashboardLayout>
  );
};

export default ClientProfileEditPage;
