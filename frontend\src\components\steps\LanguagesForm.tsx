// import React, { useState } from 'react';
// import { Plus, X } from 'lucide-react';
// import type { ProfileFormData } from '../types';

// interface LanguagesFormProps {
//   data: ProfileFormData;
//   onChange: (data: Partial<ProfileFormData>) => void;
// }

// // const LANGUAGE_LEVELS = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'] as const;

// // const isValidLevel = (value: string): value is typeof LANGUAGE_LEVELS[number] => {
// //   return LANGUAGE_LEVELS.includes(value as typeof LANGUAGE_LEVELS[number]);
// // };

// export default function LanguagesForm({ data, onChange }: LanguagesFormProps) {

//   const [newlanguage, setNewlanguage] = useState<string>(''); // État pour la compétence actuelle
  
//     // Fonction pour ajouter la compétence au tableau
//     const handleAddlanguage = () => {
//       if (newlanguage.trim() !== '') {
//         const updatedlanguages = [...data.languages, newlanguage.trim()];
//         onChange({ languages: updatedlanguages });
//         setNewlanguage(''); // Réinitialiser l'entrée après ajout
//       }
//     };
  
//     // Fonction pour gérer la suppression d'une compétence
//     const handleRemovelanguage = (languageToRemove: string) => {
//       const updatedlanguages = data.languages.filter(language => language !== languageToRemove);
//       onChange({ languages: updatedlanguages });
//     };
  
//     // Fonction pour gérer la saisie de texte
//     const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//       setNewlanguage(e.target.value);
//     };
  
//     // Fonction pour ajouter une compétence quand "Entrée" est pressé
//     const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
//       if (e.key === 'Enter') {
//         handleAddlanguage();
//       }
//     };

//   // const [newLanguage, setNewLanguage] = useState({
//   //   language: '',
//   //   level: 'B1' as typeof LANGUAGE_LEVELS[number],
//   // });

//   // const handleAddLanguage = () => {
//   //   if (newLanguage.language) {
//   //     onChange({
//   //       languages: [...data.languages, newLanguage],
//   //     });
//   //     setNewLanguage({
//   //       language: '',
//   //       level: 'B1',
//   //     });
//   //   }
//   // };

//   // const handleRemoveLanguage = (index: number) => {
//   //   onChange({
//   //     languages: data.languages.filter((_, i) => i !== index),
//   //   });
//   // };

//   return (
//     <div className="space-y-8">
//           <div>
//             <div className="space-y-4">
//               <div className="grid grid-cols-1 gap-4">
//                 <h3 className="text-lg font-semibold mb-4">Langues</h3>
//                 <input
//                   type="text"
//                   placeholder="Langue"
//                   value={newlanguage}
//                   onChange={handleInputChange}
//                   onKeyDown={handleKeyDown}
//                   className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
//                 />
//               </div>
    
//               {/* Affichage des compétences ajoutées sous forme de tags */}
//               <div className="flex gap-2 flex-wrap mt-4">
//                 {data.languages.map((language, index) => (
//                   <div key={index} className="flex items-center gap-2 bg-gray-200 rounded-lg px-3 py-1">
//                     <span>{language}</span>
//                     <button
//                       type="button"
//                       onClick={() => handleRemovelanguage(language)}
//                       className="text-red-500"
//                     >
//                       <X size={14} />
//                     </button>
//                   </div>
//                 ))}
//               </div>
    
//               {/* Bouton pour ajouter la compétence manuellement */}
//               <div className="mt-4">
//                 <button
//                   type="button"
//                   onClick={handleAddlanguage}
//                   className="px-4 py-2 bg-blue-500 text-white rounded-md"
//                 >
//                   Ajouter la langue
//                 </button>
//               </div>          
//             </div>
//           </div>
//         </div>
//     // <div className="space-y-8">
//     //   <div>
//     //     <h3 className="text-lg font-semibold mb-4">Langues</h3>
//     //     <div className="space-y-4">
//     //       <div className="flex gap-4">
//     //         <input
//     //           type="text"
//     //           placeholder="Langue"
//     //           value={newLanguage.language}
//     //           onChange={(e) => setNewLanguage({ ...newLanguage, language: e.target.value })}
//     //           className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
//     //         />
//     //         <select
//     //           value={newLanguage.level}
//     //           onChange={(e) => {
//     //             const selectedLevel = e.target.value;
//     //             if (isValidLevel(selectedLevel)) {
//     //               setNewLanguage({ ...newLanguage, level: selectedLevel });
//     //             }
//     //           }}
//     //           className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
//     //         >
//     //           {LANGUAGE_LEVELS.map((level) => (
//     //             <option key={level} value={level}>
//     //               {level}
//     //             </option>
//     //           ))}
//     //         </select>
//     //         <button
//     //           onClick={handleAddLanguage}
//     //           className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
//     //         >
//     //           <Plus className="w-4 h-4" />
//     //         </button>
//     //       </div>
//     //     </div>

//     //     <div className="mt-4 space-y-2">
//     //       {data.languages.map((lang, index) => (
//     //         <div
//     //           key={index}
//     //           className="flex items-center justify-between px-4 py-3 bg-gray-50 rounded-lg"
//     //         >
//     //           <span className="font-medium">{lang.language}</span>
//     //           <div className="flex items-center gap-4">
//     //             <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm font-medium">
//     //               {lang.level}
//     //             </span>
//     //             <button
//     //               onClick={() => handleRemoveLanguage(index)}
//     //               className="p-1 text-gray-500 hover:text-red-500"
//     //             >
//     //               <X className="w-4 h-4" />
//     //             </button>
//     //           </div>
//     //         </div>
//     //       ))}
//     //     </div>
//     //   </div>
//     // </div>
//   );
// }

import React, { useState } from 'react';
import { Plus, X } from 'lucide-react';
import type { ProfileFormData } from '../types';
import { LANGUAGES } from '../../config';

interface LanguagesFormProps {
  data: ProfileFormData;
  onChange: (data: Partial<ProfileFormData>) => void;
}

export default function LanguagesForm({ data, onChange }: LanguagesFormProps) {
  const [searchLang, setSearchLang] = useState('');

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedOptions = Array.from(e.target.selectedOptions, (option) => option.value);
    onChange({ languages: selectedOptions });
  };

  const handleRemoveLanguage = (languageToRemove: string) => {
    const updatedLanguages = data.languages.filter((language) => language !== languageToRemove);
    onChange({ languages: updatedLanguages });
  };

  // const filteredLanguages = LANGUAGES.filter((lang) =>
  //   lang.name_fr.toLowerCase().includes(searchLang.toLowerCase())
  // );

  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <div className="grid grid-cols-1 gap-4">
          <h3 className="text-lg font-semibold mb-4">Langues</h3>

          <input
            type="text"
            placeholder="Rechercher une langue..."
            value={searchLang}
            onChange={(e) => setSearchLang(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />

          <select
            multiple
            value={data.languages}
            onChange={handleSelectChange}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 h-48"
          >
            {/* {filteredLanguages.map((lang) => (
              <option key={lang.code} value={lang.code}>
                {lang.name_fr} ({lang.code})
              </option>
            ))} */}
          </select>
        </div>

        <div className="flex gap-2 flex-wrap mt-4">
          {data.languages.map((language, index) => {
            // const lang = LANGUAGES.find((l) => l.code === language);
            return (
              <div key={index} className="flex items-center gap-2 bg-gray-200 rounded-lg px-3 py-1">
                {/* <span>{lang ? lang.name_fr : language}</span> */}
                <button
                  type="button"
                  onClick={() => handleRemoveLanguage(language)}
                  className="text-red-500"
                >
                  <X size={14} />
                </button>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
