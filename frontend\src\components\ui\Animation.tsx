import React, { ReactNode, useEffect, useState } from 'react';

type AnimationType = 'fadeIn' | 'fadeOut' | 'slideInUp' | 'slideInDown' | 'slideInLeft' | 'slideInRight' | 'zoomIn' | 'bounce';

interface AnimationProps {
  children: ReactNode;
  type: AnimationType;
  duration?: number;
  delay?: number;
  className?: string;
  repeat?: boolean;
  infinite?: boolean;
  onAnimationEnd?: () => void;
}

/**
 * Composant Animation pour ajouter des animations aux éléments
 * @param children - Le contenu à animer
 * @param type - Le type d'animation
 * @param duration - La durée de l'animation en millisecondes
 * @param delay - Le délai avant le début de l'animation en millisecondes
 * @param className - Classes CSS supplémentaires
 * @param repeat - Si l'animation doit se répéter
 * @param infinite - Si l'animation doit se répéter indéfiniment
 * @param onAnimationEnd - Fonction appelée à la fin de l'animation
 */
const Animation: React.FC<AnimationProps> = ({
  children,
  type,
  duration = 500,
  delay = 0,
  className = '',
  repeat = false,
  infinite = false,
  onAnimationEnd,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Attendre le délai spécifié avant de rendre l'élément visible
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  // Définir les classes d'animation en fonction du type
  const getAnimationClass = () => {
    switch (type) {
      case 'fadeIn':
        return 'animate-fade-in';
      case 'fadeOut':
        return 'animate-fade-out';
      case 'slideInUp':
        return 'animate-slide-in-up';
      case 'slideInDown':
        return 'animate-slide-in-down';
      case 'slideInLeft':
        return 'animate-slide-in-left';
      case 'slideInRight':
        return 'animate-slide-in-right';
      case 'zoomIn':
        return 'animate-zoom-in';
      case 'bounce':
        return 'animate-bounce';
      default:
        return '';
    }
  };

  // Définir le style pour la durée et l'itération
  const animationStyle = {
    animationDuration: `${duration}ms`,
    animationDelay: `${delay}ms`,
    animationIterationCount: infinite ? 'infinite' : repeat ? '2' : '1',
  };

  // Gérer la fin de l'animation
  const handleAnimationEnd = () => {
    if (onAnimationEnd) {
      onAnimationEnd();
    }
  };

  if (!isVisible) {
    // Retourner un espace réservé invisible de même taille
    return <div style={{ visibility: 'hidden' }}>{children}</div>;
  }

  return (
    <div
      className={`${getAnimationClass()} ${className}`}
      style={animationStyle}
      onAnimationEnd={handleAnimationEnd}
    >
      {children}
    </div>
  );
};

// Optimiser le composant avec React.memo pour éviter les re-rendus inutiles
export default React.memo(Animation);
