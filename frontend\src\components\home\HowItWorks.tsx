import React from 'react';
import { UserPlus, Search, MessageSquare, CreditCard } from 'lucide-react';
import Section from '../layout/Section';
import Grid from '../layout/Grid';

const steps = [
  {
    id: 1,
    title: "Créez votre profil",
    description: "Inscrivez-vous et présentez vos compétences en 3D ou vos besoins en tant que client.",
    icon: <UserPlus className="h-6 w-6" />,
    color: "bg-primary-100 text-primary-700"
  },
  {
    id: 2,
    title: "Trouvez le talent idéal",
    description: "Parcourez les profils des professionnels ou publiez votre projet pour recevoir des offres.",
    icon: <Search className="h-6 w-6" />,
    color: "bg-secondary-100 text-secondary-700"
  },
  {
    id: 3,
    title: "Communiquez directement",
    description: "Échangez avec les professionnels pour définir les détails de votre collaboration.",
    icon: <MessageSquare className="h-6 w-6" />,
    color: "bg-blue-100 text-blue-700"
  },
  {
    id: 4,
    title: "Paiement sécurisé",
    description: "Effectuez des paiements sécurisés et recevez des factures pour vos transactions.",
    icon: <CreditCard className="h-6 w-6" />,
    color: "bg-green-100 text-green-700"
  }
];

const HowItWorks: React.FC = () => {
  return (
    <Section background="light" spacing="xl">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold text-neutral-900 mb-4">Comment ça marche ?</h2>
        <p className="text-neutral-600 max-w-2xl mx-auto">
          Découvrez comment Hi 3D Artist vous aide à trouver des talents en 3D ou à promouvoir vos propres compétences.
        </p>
      </div>

      <Grid cols={1} mdCols={2} lgCols={4} gap={8}>
        {steps.map((step) => (
          <div key={step.id} className="relative">
            <div className="flex flex-col items-center text-center">
              <div className={`w-16 h-16 rounded-full ${step.color} flex items-center justify-center mb-4`}>
                {step.icon}
              </div>
              
              <div className="absolute top-8 left-full w-full h-0.5 bg-neutral-200 -z-10 hidden md:block" 
                   style={{ display: step.id === steps.length ? 'none' : '' }}></div>
              
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 rounded-full bg-primary-600 text-white flex items-center justify-center font-bold">
                  {step.id}
                </div>
              </div>
              
              <h3 className="text-xl font-semibold mb-2">{step.title}</h3>
              <p className="text-neutral-600">{step.description}</p>
            </div>
          </div>
        ))}
      </Grid>
    </Section>
  );
};

export default HowItWorks;
