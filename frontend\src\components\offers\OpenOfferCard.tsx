import React from 'react';
import { Calendar, DollarSign, Clock, User, Building } from 'lucide-react';
import Badge from '../ui/Badge';

interface OpenOfferCardProps {
  offer: {
    id: number;
    title: string;
    description: string;
    budget: string;
    deadline: string;
    company?: string;
    status: string;
    created_at: string;
    applications_count?: number;
    client?: {
      name: string;
      avatar?: string;
    };
    categories?: string[];
  };
  onClick: () => void;
  isProfessional?: boolean;
}

const OpenOfferCard: React.FC<OpenOfferCardProps> = ({
  offer,
  onClick,
  isProfessional = false,
}) => {
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  // Calculate days remaining
  const getDaysRemaining = (dateString: string) => {
    const deadline = new Date(dateString);
    const now = new Date();
    const diffTime = deadline.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return 'Délai dépassé';
    } else if (diffDays === 0) {
      return 'Dernier jour';
    } else {
      return `${diffDays} jour${diffDays > 1 ? 's' : ''} restant${diffDays > 1 ? 's' : ''}`;
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'open':
        return 'success';
      case 'in_progress':
        return 'primary';
      case 'completed':
        return 'neutral';
      case 'closed':
        return 'danger';
      case 'invited':
        return 'info';
      default:
        return 'neutral';
    }
  };

  // Get status label
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'En attente';
      case 'open':
        return 'Ouvert';
      case 'in_progress':
        return 'En cours';
      case 'completed':
        return 'Terminé';
      case 'closed':
        return 'Fermé';
      case 'invited':
        return 'Invité';
      default:
        return 'Inconnu';
    }
  };

  return (
    <div
      className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
      onClick={onClick}
    >
      {/* Card header with status badge */}
      <div className="px-4 py-3 bg-neutral-50 border-b border-neutral-200 flex justify-between items-center">
        <Badge color={getStatusBadgeColor(offer.status)}>
          {getStatusLabel(offer.status)}
        </Badge>
        <span className="text-xs text-neutral-500">
          Créé le {formatDate(offer.created_at)}
        </span>
      </div>

      {/* Card content */}
      <div className="p-4">
        <h3 className="text-lg font-semibold text-neutral-900 mb-2 line-clamp-1">{offer.title}</h3>
        <p className="text-neutral-600 text-sm mb-4 line-clamp-2">{offer.description}</p>

        <div className="space-y-2 mb-4">
          {/* Budget */}
          <div className="flex items-center text-sm">
            <DollarSign className="h-4 w-4 text-neutral-500 mr-2" />
            <span className="text-neutral-700 font-medium">{offer.budget}</span>
          </div>

          {/* Deadline */}
          <div className="flex items-center text-sm">
            <Calendar className="h-4 w-4 text-neutral-500 mr-2" />
            <span className="text-neutral-700">{formatDate(offer.deadline)}</span>
          </div>

          {/* Days remaining */}
          <div className="flex items-center text-sm">
            <Clock className="h-4 w-4 text-neutral-500 mr-2" />
            <span className="text-neutral-700">{getDaysRemaining(offer.deadline)}</span>
          </div>

          {/* Company or client */}
          <div className="flex items-center text-sm">
            {isProfessional ? (
              <>
                <User className="h-4 w-4 text-neutral-500 mr-2" />
                <span className="text-neutral-700">{offer.client?.name || 'Client anonyme'}</span>
              </>
            ) : (
              <>
                <Building className="h-4 w-4 text-neutral-500 mr-2" />
                <span className="text-neutral-700">{offer.company || 'Votre entreprise'}</span>
              </>
            )}
          </div>
        </div>

        {/* Categories */}
        {offer.categories && offer.categories.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-3">
            {offer.categories.slice(0, 3).map((category, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-neutral-100 text-neutral-800"
              >
                {category}
              </span>
            ))}
            {offer.categories.length > 3 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-neutral-100 text-neutral-800">
                +{offer.categories.length - 3}
              </span>
            )}
          </div>
        )}

        {/* Applications count (for clients only) */}
        {!isProfessional && offer.applications_count !== undefined && (
          <div className="mt-2 text-sm text-neutral-500">
            {offer.applications_count} candidature{offer.applications_count !== 1 ? 's' : ''}
          </div>
        )}
      </div>
    </div>
  );
};

export default OpenOfferCard;
