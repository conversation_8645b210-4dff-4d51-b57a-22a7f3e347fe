{"service_offer_response_example": {"id": 1, "user_id": 123, "user": {"id": 123, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "avatar": "path/to/avatar.jpg", "is_professional": true, "professional_details": {"title": "Architecte 3D", "skills": ["3D Modeling", "Rendering"]}}, "title": "Modélisation architecturale résidentielle", "description": "Je propose des services de modélisation 3D pour vos projets résidentiels...", "price": 150.0, "price_unit": "per_project", "price_unit_label": "par projet", "average_duration": "1_to_2_weeks", "average_duration_label": "1 à 2 semaines", "cover_image": "service_cover_images/cover_123.jpg", "cover_image_url": "http://localhost:8000/storage/service_cover_images/cover_123.jpg", "image_category": "architectural", "associated_project_id": 456, "associated_project": {"id": 456, "title": "Villa moderne", "description": "Projet de villa contemporaine", "category": "Architecture"}, "execution_time": "2 semaines", "concepts": "3 concepts initiaux", "revisions": "2 révisions incluses", "is_private": false, "categories": ["architectural", "residential"], "files": [{"path": "service_offer_files/portfolio_1.jpg", "original_name": "portfolio_example.jpg", "mime_type": "image/jpeg", "size": 1024000}], "status": "published", "likes": 15, "views": 234, "created_at": "2025-07-01T10:30:00.000000Z", "updated_at": "2025-07-01T10:30:00.000000Z"}, "configuration_options_example": {"price_units": {"per_image": {"value": "per_image", "label": "par image", "description": "Prix calculé par image produite"}, "per_sqm": {"value": "per_sqm", "label": "par m²", "description": "Prix calculé par mètre carré"}, "per_project": {"value": "per_project", "label": "par projet", "description": "Prix forfaitaire pour l'ensemble du projet"}}, "average_durations": {"less_than_week": {"value": "less_than_week", "label": "moins d'une semaine", "description": "Livraison en moins de 7 jours"}, "1_to_2_weeks": {"value": "1_to_2_weeks", "label": "1 à 2 semaines", "description": "Livraison entre 1 et 2 semaines"}, "1_month": {"value": "1_month", "label": "1 mois", "description": "Livraison en 1 mois"}, "2_months": {"value": "2_months", "label": "2 mois", "description": "Livraison en 2 mois"}, "3_months": {"value": "3_months", "label": "3 mois", "description": "Livraison en 3 mois"}}, "image_categories": {"architectural": "Architecture 3D", "product": "Produit 3D", "character": "Personnage 3D", "environment": "Environnement 3D", "modeling": "Modélisation 3D", "animation": "Animation", "rendering": "Rendu 3D", "visualization": "Visualisation", "other": "<PERSON><PERSON>"}}, "available_projects_example": [{"id": 1, "title": "Villa moderne", "description": "Projet de villa contemporaine avec piscine", "category": "Architecture"}, {"id": 2, "title": "Appartement design", "description": "Rénovation d'un appartement parisien", "category": "Intérieur"}], "create_service_offer_request_example": {"title": "Modélisation architecturale résidentielle", "description": "Je propose des services de **modélisation 3D** pour vos projets résidentiels avec :\n\n- Modélisation détaillée\n- Rendu photoréaliste\n- Plans techniques", "price": 150.0, "price_unit": "per_project", "average_duration": "1_to_2_weeks", "categories": ["architectural", "residential"], "cover_image": "file_upload", "image_category": "architectural", "associated_project_id": 1, "is_private": false, "status": "published"}, "validation_errors_example": {"message": "The given data was invalid.", "errors": {"title": ["Le nom du service est obligatoire."], "description": ["La description est obligatoire."], "price": ["Le prix de base est obligatoire."], "price_unit": ["L'unité du prix est obligatoire."], "average_duration": ["La durée moyenne de réalisation est obligatoire."], "categories": ["La catégorie est obligatoire."], "cover_image": ["L'image de couverture doit être au format : jpeg, png, jpg, gif, svg ou webp."], "associated_project_id": ["Le projet associé sélectionné n'existe pas."]}}}