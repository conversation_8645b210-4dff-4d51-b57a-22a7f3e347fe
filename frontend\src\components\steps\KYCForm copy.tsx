import React, { useRef, useState } from 'react';
import { Upload, X } from 'lucide-react';
import type { ProfileFormData } from '../types';

interface KYCFormProps {
  data: ProfileFormData;
  onChange: (data: Partial<ProfileFormData>) => void;
}

export default function KYCForm({ data, onChange }: KYCFormProps) {
  const [certificationInput, setCertificationInput] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const selfieInputRef = useRef<HTMLInputElement>(null);

  // const handleAddCertification = () => {
  //   if (certificationInput.trim()) {
  //     onChange({
  //       certifications: [...data.certifications, certificationInput.trim()]
  //     });
  //     setCertificationInput('');
  //   }
  // };

  // const handleRemoveCertification = (index: number) => {
  //   onChange({
  //     certifications: data.certifications.filter((_, i) => i !== index)
  //   });
  // };

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 gap-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Pièce d'identité</h3>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*,.pdf"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  onChange({ idDocument: file });
                }
              }}
            />
            <button
              onClick={() => fileInputRef.current?.click()}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
            >
              <Upload className="w-4 h-4" />
              Télécharger un document
            </button>
            {data.idDocument && (
              <p className="mt-2 text-sm text-gray-600">
                {data.idDocument.name}
              </p>
            )}
          </div>
        </div>

        {/* <div>
          <h3 className="text-lg font-semibold mb-4">Selfie</h3>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <input
              type="file"
              ref={selfieInputRef}
              className="hidden"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  onChange({ selfie: file });
                }
              }}
            />
            <button
              onClick={() => selfieInputRef.current?.click()}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
            >
              <Upload className="w-4 h-4" />
              Télécharger une photo
            </button>
            {data.selfie && (
              <p className="mt-2 text-sm text-gray-600">
                {data.selfie.name}
              </p>
            )}
          </div>
        </div> */}
      </div>
      <div>
        <label htmlFor="identity_document_number" className="block text-sm font-medium text-gray-700 mb-1">
            ID du Document
          </label> 
          <input
            type="text"
            id="identity_document_number"
            value={data.identity_document_number}
            onChange={(e) => onChange({ identity_document_number: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            required
          />
      </div>

      {/* <div>
        <h3 className="text-lg font-semibold mb-4">Certifications</h3>
        <div className="space-y-4">
          <div className="flex gap-2">
            <input
              type="text"
              value={certificationInput}
              onChange={(e) => setCertificationInput(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Ajouter une certification"
            />
            <button
              onClick={handleAddCertification}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              Ajouter
            </button>
          </div>

          <div className="space-y-2">
            {data.certifications.map((cert, index) => (
              <div
                key={index}
                className="flex items-center justify-between px-4 py-2 bg-gray-50 rounded-lg"
              >
                <span>{cert}</span>
                <button
                  onClick={() => handleRemoveCertification(index)}
                  className="p-1 text-gray-500 hover:text-red-500"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      </div> */}
    </div>
  );
}