<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('service_offers', function (Blueprint $table) {
            // Unité du prix (par image, par m², par projet)
            $table->enum('price_unit', ['per_image', 'per_sqm', 'per_project'])->default('per_project')->after('price');

            // Durée moyenne de réalisation
            $table->enum('average_duration', [
                'less_than_week',
                '1_to_2_weeks',
                '1_month',
                '2_months',
                '3_months'
            ])->nullable()->after('price_unit');

            // Image de couverture
            $table->string('cover_image')->nullable()->after('average_duration');

            // Catégorie d'image
            $table->string('image_category')->nullable()->after('cover_image');

            // Projet associé
            $table->foreignId('associated_project_id')->nullable()->constrained('dashboard_projects')->onDelete('set null')->after('image_category');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('service_offers', function (Blueprint $table) {
            $table->dropForeign(['associated_project_id']);
            $table->dropColumn([
                'price_unit',
                'average_duration',
                'cover_image',
                'image_category',
                'associated_project_id'
            ]);
        });
    }
};
