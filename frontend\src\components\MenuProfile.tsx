import React, { useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, LogOut } from 'lucide-react';

interface MenuProfileProps {
  user: {
    id: number;
    first_name: string;
    last_name: string;
    is_professional: boolean;
  };
  setIsModalOpen: (isOpen: boolean) => void;
  handleLogout: () => void;
  setIsMenuOpen: (isOpen: boolean) => void;
}

const MenuProfile: React.FC<MenuProfileProps> = ({ user, setIsModalOpen, handleLogout, setIsMenuOpen }) => {
  const navigate = useNavigate();
  const menuRef = useRef<HTMLDivElement>(null);

  const handleProfil = () => {
    if (user.is_professional) {
      navigate(`/profile/${user.id}`);
    } else {
      navigate(`/edit-profile-client/${user.id}`);
    }
    setIsModalOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [setIsMenuOpen]);

  return (
    <div className="absolute mt-3 bg-white border rounded-lg shadow-lg py-2 px-4 w-48 z-50" ref={menuRef}>
      <button
        className="w-full text-left text-gray-700 hover:bg-gray-100 py-2 flex items-center"
        onClick={handleProfil}
      >
        <span className="font-semibold">{user.first_name} {user.last_name}</span>
      </button>

      <div className="border-t my-2"></div>

      <button className="w-full text-left text-gray-700 hover:bg-gray-100 py-2 flex items-center" onClick={() => navigate('/settings')}>
        <Settings className="w-4 h-4 mr-2" />
        Paramètres
      </button>

      <button className="w-full text-left text-gray-700 hover:bg-gray-100 py-2 flex items-center" onClick={() => navigate('/stats')}>
        <BarChart className="w-4 h-4 mr-2" />
        Statistiques
      </button>

      <button className="w-full text-left text-red-500 hover:bg-red-50 py-2 flex items-center" onClick={handleLogout}>
        <LogOut className="w-4 h-4 mr-2" />
        Se déconnecter
      </button>
    </div>
  );
};

export default MenuProfile;