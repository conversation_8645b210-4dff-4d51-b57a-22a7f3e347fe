import React from 'react';
import { Phone, Video, MoreVertical, ArrowLeft } from 'lucide-react';
import Avatar from '../ui/Avatar';

interface ChatHeaderProps {
  title: string;
  subtitle?: string;
  avatar?: string;
  status?: 'online' | 'offline' | 'away';
  onBack?: () => void;
  onVideoCall?: () => void;
  onVoiceCall?: () => void;
  onMenuClick?: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  title,
  subtitle,
  avatar,
  status,
  onBack,
  onVideoCall,
  onVoiceCall,
  onMenuClick,
}) => {
  return (
    <div className="flex items-center justify-between p-4 border-b border-neutral-200 bg-white">
      <div className="flex items-center">
        {onBack && (
          <button
            onClick={onBack}
            className="mr-2 p-1 rounded-full hover:bg-neutral-100 lg:hidden"
          >
            <ArrowLeft className="h-5 w-5 text-neutral-600" />
          </button>
        )}
        
        <Avatar
          size="md"
          src={avatar}
          fallback={title.charAt(0)}
          status={status}
        />
        
        <div className="ml-3">
          <h2 className="font-semibold text-neutral-900">{title}</h2>
          {subtitle && <p className="text-sm text-neutral-500">{subtitle}</p>}
        </div>
      </div>
      
      <div className="flex items-center space-x-2">
        {onVoiceCall && (
          <button
            onClick={onVoiceCall}
            className="p-2 rounded-full hover:bg-neutral-100"
          >
            <Phone className="h-5 w-5 text-neutral-600" />
          </button>
        )}
        
        {onVideoCall && (
          <button
            onClick={onVideoCall}
            className="p-2 rounded-full hover:bg-neutral-100"
          >
            <Video className="h-5 w-5 text-neutral-600" />
          </button>
        )}
        
        {onMenuClick && (
          <button
            onClick={onMenuClick}
            className="p-2 rounded-full hover:bg-neutral-100"
          >
            <MoreVertical className="h-5 w-5 text-neutral-600" />
          </button>
        )}
      </div>
    </div>
  );
};

export default ChatHeader;
