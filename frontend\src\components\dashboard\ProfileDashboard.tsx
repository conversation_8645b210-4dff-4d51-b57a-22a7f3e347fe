import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Mail,
  Phone,
  MapPin,
  Briefcase,
  Award,
  Edit,
  Star,
  Download,
  CheckCircle,
  FileText
} from 'lucide-react';
import DashboardLayout from './DashboardLayout';
import Button from '../ui/Button';
import Avatar from '../ui/Avatar';
import Badge from '../ui/Badge';
import Tabs from '../ui/Tabs';
import { profileService } from '../../services/profileService';
import { API_BASE_URL } from '../../config';
import { getAvatarUrl, getInitials } from '../../utils/avatarUtils';
import './ProfileDashboard.css';

interface ProfileDataLocal {
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  bio?: string;
  skills?: string[];
  avatar?: string;
  portfolio?: Array<{
    path: string;
    name: string;
    type: string;
  }>;
  title?: string;
  rating?: number;
  hourly_rate?: number;
  availability_status?: string;
  experience?: number;
  completion_percentage: number;
  languages?: string[];
  services_offered?: string[];
}

const ProfileDashboard: React.FC = () => {
  const [profileData, setProfileData] = useState<ProfileDataLocal | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  // Supprimé car nous utilisons maintenant le composant Tabs
  const navigate = useNavigate();

  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          navigate('/login');
          return;
        }

        const { profile } = await profileService.getProfile();
        console.log('Profile data from API:', profile);
        setProfileData(profile as unknown as ProfileDataLocal);
        setError(null);
      } catch (err) {
        console.error('Error fetching profile data:', err);
        setError('Failed to fetch profile data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, [navigate]);

  const handleEditProfile = () => {
    navigate('/dashboard/profile/edit');
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <div className="text-red-600 mb-4">{error}</div>
          <Button
            variant="primary"
            onClick={() => window.location.reload()}
          >
            Réessayer
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  if (!profileData) {
    return (
      <DashboardLayout>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
          <div className="text-yellow-700 mb-4">Aucune donnée de profil disponible. Veuillez compléter votre profil.</div>
          <Button
            variant="primary"
            onClick={handleEditProfile}
          >
            Compléter le profil
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  // Utiliser la fonction utilitaire pour obtenir l'URL de l'avatar
  const getProfileAvatarUrl = () => {
    return getAvatarUrl(profileData?.avatar) || 'https://via.placeholder.com/150';
  };

  const renderStars = (rating: number) => {
    return Array(5).fill(0).map((_, index) => (
      <Star
        key={index}
        className={`h-5 w-5 ${index < Math.floor(rating) ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
      />
    ));
  };

  const getAvailabilityColor = (status?: string) => {
    switch (status) {
      case 'available':
        return 'success';
      case 'busy':
        return 'warning';
      case 'unavailable':
        return 'error';
      default:
        return 'neutral';
    }
  };

  const getAvailabilityText = (status?: string) => {
    switch (status) {
      case 'available':
        return 'Available';
      case 'busy':
        return 'Busy';
      case 'unavailable':
        return 'Unavailable';
      default:
        return 'Not specified';
    }
  };

  return (
    <DashboardLayout
      title="Mon profil"
      subtitle="Voir et gérer votre profil professionnel"
      actions={
        <button
          type="button"
          onClick={handleEditProfile}
          className="edit-profile-button"
        >
          <Edit />
          Modifier le profil
        </button>
      }
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left column - Profile summary */}
        <div className="lg:col-span-1 space-y-6">
          {/* Profile card */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="p-6 text-center">
              <div className="relative mx-auto mb-4">
                <Avatar
                  size="xl"
                  src={getProfileAvatarUrl()}
                  fallback={getInitials(profileData.first_name, profileData.last_name)}
                  className="mx-auto"
                />
                <Badge
                  variant={getAvailabilityColor(profileData.availability_status) as 'success' | 'warning' | 'error' | 'neutral'}
                  className="absolute bottom-0 right-0 border-2 border-white"
                >
                  {getAvailabilityText(profileData.availability_status)}
                </Badge>
              </div>

              <h3 className="text-xl font-semibold text-neutral-900">
                {profileData.first_name} {profileData.last_name}
              </h3>
              <p className="text-neutral-600 mt-1">
                {profileData.title || 'Professionnel'}
              </p>

              {profileData.rating !== undefined && profileData.rating !== null && (
                <div className="flex justify-center mt-2">
                  {renderStars(typeof profileData.rating === 'number' ? profileData.rating : 0)}
                  <span className="ml-2 text-sm text-neutral-600">
                    {typeof profileData.rating === 'number' ? profileData.rating.toFixed(1) : '0.0'}
                  </span>
                </div>
              )}

              <div className="mt-4 pt-4 border-t border-neutral-200">
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-neutral-600">Complétion du profil</span>
                  <span className="font-medium text-neutral-900">{profileData.completion_percentage}%</span>
                </div>
                <div className="w-full bg-neutral-200 rounded-full h-2">
                  <div
                    className="completion-progress-bar"
                    style={{ width: `${profileData.completion_percentage}%` }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="bg-neutral-50 px-6 py-4 border-t border-neutral-200">
              <div className="space-y-3">
                <div className="flex items-center text-sm">
                  <Mail className="h-4 w-4 text-neutral-500 mr-2" />
                  <span className="text-neutral-700">{profileData.email}</span>
                </div>
                <div className="flex items-center text-sm">
                  <Phone className="h-4 w-4 text-neutral-500 mr-2" />
                  <span className="text-neutral-700">{profileData.phone || 'Not provided'}</span>
                </div>
                <div className="flex items-center text-sm">
                  <MapPin className="h-4 w-4 text-neutral-500 mr-2" />
                  <span className="text-neutral-700">
                    {profileData.city && profileData.country
                      ? `${profileData.city}, ${profileData.country}`
                      : 'Location not provided'}
                  </span>
                </div>
                {profileData.hourly_rate && (
                  <div className="flex items-center text-sm">
                    <Award className="h-4 w-4 text-neutral-500 mr-2" />
                    <span className="text-neutral-700">${profileData.hourly_rate}/hr</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Skills card */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h3 className="text-lg font-semibold text-neutral-900">Compétences</h3>
            </div>
            <div className="p-6">
              {profileData.skills && Array.isArray(profileData.skills) && profileData.skills.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {profileData.skills.map((skill, index) => (
                    <Badge key={index} variant="primary">
                      {skill}
                    </Badge>
                  ))}
                </div>
              ) : (
                <p className="text-neutral-500 text-sm">Aucune compétence ajoutée pour le moment.</p>
              )}
            </div>
          </div>

          {/* Languages card */}
          {profileData.languages && Array.isArray(profileData.languages) && profileData.languages.length > 0 && (
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-neutral-200">
                <h3 className="text-lg font-semibold text-neutral-900">Langues</h3>
              </div>
              <div className="p-6">
                <div className="flex flex-wrap gap-2">
                  {profileData.languages.map((language, index) => (
                    <Badge key={index} variant="secondary">
                      {language}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Right column - Detailed information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Tabs avec accessibilité améliorée */}
          <Tabs
            tabs={[
              {
                id: 'overview',
                label: 'Aperçu',
                content: (
                  <div className="space-y-6">
                    {/* Bio section */}
                    <div>
                      <h3 className="text-lg font-semibold text-neutral-900 mb-3">À propos de moi</h3>
                      <p className="text-neutral-700 whitespace-pre-line">
                        {profileData.bio || 'No bio information available. Add a bio to tell clients about yourself and your expertise.'}
                      </p>
                    </div>

                    {/* Experience section */}
                    {profileData.experience && (
                      <div>
                        <h3 className="text-lg font-semibold text-neutral-900 mb-3">Expérience</h3>
                        <div className="flex items-center">
                          <Briefcase className="h-5 w-5 text-primary-600 mr-2" />
                          <span className="text-neutral-700">{profileData.experience} années d'expérience</span>
                        </div>
                      </div>
                    )}

                    {/* Contact information */}
                    <div>
                      <h3 className="text-lg font-semibold text-neutral-900 mb-3">Coordonnées</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-neutral-50 p-4 rounded-lg">
                          <div className="flex items-center mb-2">
                            <Mail className="h-5 w-5 text-primary-600 mr-2" />
                            <span className="text-neutral-900 font-medium">E-mail</span>
                          </div>
                          <p className="text-neutral-700">{profileData.email}</p>
                        </div>
                        <div className="bg-neutral-50 p-4 rounded-lg">
                          <div className="flex items-center mb-2">
                            <Phone className="h-5 w-5 text-primary-600 mr-2" />
                            <span className="text-neutral-900 font-medium">Téléphone</span>
                          </div>
                          <p className="text-neutral-700">{profileData.phone || 'Not provided'}</p>
                        </div>
                        <div className="bg-neutral-50 p-4 rounded-lg">
                          <div className="flex items-center mb-2">
                            <MapPin className="h-5 w-5 text-primary-600 mr-2" />
                            <span className="text-neutral-900 font-medium">Adresse</span>
                          </div>
                          <p className="text-neutral-700">{profileData.address || 'Not provided'}</p>
                          <p className="text-neutral-700">
                            {profileData.city && profileData.country
                              ? `${profileData.city}, ${profileData.country}`
                              : ''}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ),
              },
              {
                id: 'portfolio',
                label: 'Portfolio',
                content: (
                  <div>
  <h3 className="text-lg font-semibold text-neutral-900 mb-4">Éléments du portfolio</h3>
  {profileData.portfolio && Array.isArray(profileData.portfolio) && profileData.portfolio.length > 0 ? (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
      {profileData.portfolio.map((item, index) => (
        <div key={index} className="group relative rounded-lg overflow-hidden border border-neutral-200 bg-white">
          <div className="aspect-square">
            {item.type && item.type.startsWith('image/') ? (
              <>
                <img
                  src={`${API_BASE_URL}${item.path}`}
                  alt={item.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Fallback si l'image ne peut pas être chargée
                    (e.target as HTMLImageElement).src = 'https://via.placeholder.com/300';
                  }}
                />
                {/* Overlay avec bouton de téléchargement */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                  <a
                    href={`${API_BASE_URL}${item.path}`}
                    download={item.name}
                    className="bg-white text-primary-600 p-2 rounded-full shadow-lg hover:bg-neutral-100 transition-colors"
                    title={`Télécharger ${item.name}`}
                  >
                    <Download className="h-5 w-5" />
                  </a>
                </div>
              </>
            ) : (
              <div className="w-full h-full flex flex-col items-center justify-center p-4 bg-neutral-50">
                <FileText className="h-12 w-12 text-neutral-400 mb-2" />
                <span className="text-sm text-neutral-600 text-center truncate w-full">
                  {item.name}
                </span>
                {/* Bouton de téléchargement pour les fichiers non-image */}
                <a
                  href={`${API_BASE_URL}${item.path}`}
                  download={item.name}
                  className="mt-2 bg-white text-primary-600 p-2 rounded-full shadow-lg hover:bg-neutral-100 transition-colors"
                  title={`Télécharger ${item.name}`}
                >
                  <Download className="h-5 w-5" />
                </a>
              </div>
            )}
          </div>
          <div className="p-3">
            <p className="text-sm font-medium text-neutral-900 truncate">{item.name}</p>
            <p className="text-xs text-neutral-500">{item.type}</p>
          </div>
        </div>
      ))}
    </div>
  ) : (
    <div className="text-center py-8 bg-neutral-50 rounded-lg">
      <FileText className="h-12 w-12 text-neutral-400 mx-auto mb-3" />
      <h4 className="text-lg font-medium text-neutral-700 mb-2">Aucun élément de portfolio pour le moment.</h4>
      <p className="text-neutral-500 mb-4">Ajoutez des éléments à votre portfolio pour présenter votre travail aux clients potentiels.</p>
      <button
        type="button"
        onClick={handleEditProfile}
        className="portfolio-action-button"
      >
        Ajouter des éléments au portfolio
      </button>
    </div>
  )}
</div>
                ),
              },
            
            ]}
            defaultTab="overview"
          />
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ProfileDashboard;
