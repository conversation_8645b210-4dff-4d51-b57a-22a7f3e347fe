import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Search,
  Filter,
  SlidersHorizontal,
  Star,
  MapPin,
  Clock,
  AlertTriangle,
  Grid as GridIcon,
  List,
  ChevronLeft,
  ChevronRight,
  Check
} from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../ui/Button';
import Avatar from '../ui/Avatar';
import Badge from '../ui/Badge';

interface Professional {
  id: number;
  firstName: string;
  lastName: string;
  title: string;
  skills: string[];
  rating: number;
  reviewCount: number;
  imageUrl: string;
  location: string;
  availability: 'available' | 'busy' | 'unavailable';
  hourlyRate: string;
  description?: string;
  completedProjects?: number;
}

const AllProfessionalsPage: React.FC = () => {
  const navigate = useNavigate();
  const [professionals, setProfessionals] = useState<Professional[]>([]);
  const [filteredProfessionals, setFilteredProfessionals] = useState<Professional[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [skillFilter, setSkillFilter] = useState<string>('');
  const [availabilityFilter, setAvailabilityFilter] = useState<string>('all');
  const [ratingFilter, setRatingFilter] = useState<number | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(viewMode === 'grid' ? 12 : 8);
  const [sortBy, setSortBy] = useState<string>('rating');

  const token = localStorage.getItem('token');

  useEffect(() => {
    const fetchProfessionals = async () => {
      setLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/freelance-profiles`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des professionnels');
        }

        const data = await response.json();

        // Transformer les données en format Professional
        const formattedProfessionals = data.profiles.map((profile: any) => ({
          id: profile.id,
          firstName: profile.user?.first_name || 'Prénom',
          lastName: profile.user?.last_name || 'Nom',
          title: profile.title || 'Artiste 3D',
          skills: profile.skills || [],
          rating: profile.rating || 0,
          reviewCount: profile.review_count || 0,
          imageUrl: profile.portfolio_image || 'https://via.placeholder.com/300',
          location: profile.location || 'Paris, France',
          availability: profile.availability || 'available',
          hourlyRate: `${profile.hourly_rate || '40'} €/h`,
          description: profile.bio || '',
          completedProjects: profile.completed_projects_count || 0,
        }));

        setProfessionals(formattedProfessionals);
        setFilteredProfessionals(formattedProfessionals);
        setError(null);
      } catch (err) {
        console.error('Error fetching professionals:', err);
        setError('Impossible de récupérer les professionnels. Veuillez réessayer plus tard.');
        // Utiliser des données statiques en cas d'erreur
        const mockProfessionals = getMockProfessionals();
        setProfessionals(mockProfessionals);
        setFilteredProfessionals(mockProfessionals);
      } finally {
        setLoading(false);
      }
    };

    fetchProfessionals();
  }, [token]);

  // Filtrer les professionnels lorsque les filtres changent
  useEffect(() => {
    let result = [...professionals];

    // Filtre par recherche
    if (searchTerm) {
      result = result.filter(professional =>
        `${professional.firstName} ${professional.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        professional.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        professional.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        professional.location.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtre par compétence
    if (skillFilter) {
      result = result.filter(professional =>
        professional.skills.some(skill =>
          skill.toLowerCase().includes(skillFilter.toLowerCase())
        )
      );
    }

    // Filtre par disponibilité
    if (availabilityFilter !== 'all') {
      result = result.filter(professional => professional.availability === availabilityFilter);
    }

    // Filtre par note
    if (ratingFilter !== null) {
      result = result.filter(professional => professional.rating >= ratingFilter);
    }

    // Tri
    result.sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'hourlyRate':
          return parseInt(a.hourlyRate) - parseInt(b.hourlyRate);
        case 'completedProjects':
          return (b.completedProjects || 0) - (a.completedProjects || 0);
        default:
          return 0;
      }
    });

    setFilteredProfessionals(result);
    setCurrentPage(1); // Réinitialiser à la première page après filtrage
  }, [searchTerm, skillFilter, availabilityFilter, ratingFilter, sortBy, professionals]);

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredProfessionals.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredProfessionals.length / itemsPerPage);

  const paginate = (pageNumber: number) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  const handleViewProfessional = (professionalId: number) => {
    navigate(`/artist/${professionalId}`);
  };

  // Données statiques pour le fallback
  const getMockProfessionals = (): Professional[] => {
    return [
      {
        id: 1,
        firstName: 'Thomas',
        lastName: 'Martin',
        title: 'Artiste 3D & Animateur',
        skills: ['Modélisation 3D', 'Animation', 'Texturing', 'Blender', 'Maya'],
        rating: 4.8,
        reviewCount: 24,
        imageUrl: 'https://images.unsplash.com/photo-1596079890744-c1a0462d0975?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        location: 'Paris, France',
        availability: 'available',
        hourlyRate: '45 €/h',
        description: 'Artiste 3D spécialisé dans la création de personnages et l\'animation pour jeux vidéo et films.',
        completedProjects: 18,
      },
      {
        id: 2,
        firstName: 'Sophie',
        lastName: 'Dubois',
        title: 'Modeleur 3D & Texturing',
        skills: ['Modélisation 3D', 'Texturing', 'Substance Painter', 'ZBrush', 'Blender'],
        rating: 4.7,
        reviewCount: 19,
        imageUrl: 'https://images.unsplash.com/photo-1599420186946-7b6fb4e297f0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        location: 'Lyon, France',
        availability: 'busy',
        hourlyRate: '40 €/h',
        description: 'Spécialiste en modélisation 3D et texturing avec 5 ans d\'expérience dans l\'industrie du jeu vidéo.',
        completedProjects: 15,
      },
      {
        id: 3,
        firstName: 'Lucas',
        lastName: 'Bernard',
        title: 'Artiste 3D & Rendu',
        skills: ['Modélisation 3D', 'Rendu', 'Lighting', 'Cinema 4D', 'V-Ray'],
        rating: 4.9,
        reviewCount: 31,
        imageUrl: 'https://images.unsplash.com/photo-1620064916958-605375619af8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        location: 'Bordeaux, France',
        availability: 'available',
        hourlyRate: '50 €/h',
        description: 'Artiste 3D spécialisé dans le rendu photoréaliste et l\'éclairage pour l\'architecture et la publicité.',
        completedProjects: 22,
      },
      {
        id: 4,
        firstName: 'Emma',
        lastName: 'Petit',
        title: 'Animatrice 3D',
        skills: ['Animation 3D', 'Rigging', 'Motion Capture', 'Maya', 'MotionBuilder'],
        rating: 4.6,
        reviewCount: 15,
        imageUrl: 'https://images.unsplash.com/photo-1618172193763-c511deb635ca?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        location: 'Toulouse, France',
        availability: 'unavailable',
        hourlyRate: '42 €/h',
        description: 'Animatrice 3D spécialisée dans l\'animation de personnages et le rigging pour films et jeux vidéo.',
        completedProjects: 12,
      },
      {
        id: 5,
        firstName: 'Nicolas',
        lastName: 'Roux',
        title: 'Artiste VFX & 3D',
        skills: ['VFX', 'Simulation', 'Houdini', 'Nuke', 'After Effects'],
        rating: 4.8,
        reviewCount: 22,
        imageUrl: 'https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        location: 'Marseille, France',
        availability: 'available',
        hourlyRate: '55 €/h',
        description: 'Artiste VFX spécialisé dans les simulations et effets visuels pour le cinéma et la télévision.',
        completedProjects: 20,
      },
      {
        id: 6,
        firstName: 'Julie',
        lastName: 'Moreau',
        title: 'Artiste 3D Généraliste',
        skills: ['Modélisation 3D', 'Animation', 'Texturing', 'Blender', 'Unity'],
        rating: 4.5,
        reviewCount: 17,
        imageUrl: 'https://images.unsplash.com/photo-1620138546344-7b2c38516edf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        location: 'Nantes, France',
        availability: 'available',
        hourlyRate: '38 €/h',
        description: 'Artiste 3D généraliste avec une expérience dans les jeux mobiles et la réalité virtuelle.',
        completedProjects: 14,
      },
    ];
  };

  const getAvailabilityBadge = (availability: string) => {
    switch (availability) {
      case 'available':
        return <Badge color="green">Disponible</Badge>;
      case 'busy':
        return <Badge color="amber">Occupé</Badge>;
      case 'unavailable':
        return <Badge color="red">Indisponible</Badge>;
      default:
        return <Badge color="gray">Inconnu</Badge>;
    }
  };

  return (
    <DashboardLayout
      title="Professionnels recommandés"
      subtitle="Découvrez les meilleurs talents en création 3D"
      actions={
        <Button
          variant="outline"
          onClick={() => navigate('/dashboard')}
        >
          Retour au tableau de bord
        </Button>
      }
    >
      {/* Barre de recherche et filtres */}
      <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Rechercher un professionnel..."
              className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Compétence..."
              className="px-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={skillFilter}
              onChange={(e) => setSkillFilter(e.target.value)}
            />
            <select
              className="px-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={availabilityFilter}
              onChange={(e) => setAvailabilityFilter(e.target.value)}
              aria-label="Filtrer par disponibilité"
            >
              <option value="all">Toutes disponibilités</option>
              <option value="available">Disponible</option>
              <option value="busy">Occupé</option>
              <option value="unavailable">Indisponible</option>
            </select>
            <Button
              variant="outline"
              leftIcon={<SlidersHorizontal className="h-5 w-5" />}
              onClick={() => setShowFilters(!showFilters)}
            >
              Plus
            </Button>
          </div>
        </div>

        {/* Filtres avancés */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-neutral-200 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">Note minimale</label>
              <div className="flex items-center">
                {[1, 2, 3, 4, 5].map((rating) => (
                  <button
                    key={rating}
                    type="button"
                    className={`p-1 ${ratingFilter === rating ? 'text-yellow-500' : 'text-neutral-300'}`}
                    onClick={() => setRatingFilter(rating === ratingFilter ? null : rating)}
                    aria-label={`Filtrer par note minimale de ${rating} étoiles`}
                  >
                    <Star className="h-6 w-6 fill-current" />
                  </button>
                ))}
                {ratingFilter && (
                  <button
                    type="button"
                    className="ml-2 text-neutral-500 hover:text-neutral-700"
                    onClick={() => setRatingFilter(null)}
                  >
                    Réinitialiser
                  </button>
                )}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">Trier par</label>
              <select
                className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                aria-label="Trier les résultats"
              >
                <option value="rating">Meilleure note</option>
                <option value="hourlyRate">Tarif horaire (croissant)</option>
                <option value="completedProjects">Projets terminés</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">Affichage</label>
              <div className="flex border border-neutral-300 rounded-lg overflow-hidden">
                <button
                  type="button"
                  className={`flex-1 py-2 px-4 flex justify-center items-center ${
                    viewMode === 'grid' ? 'bg-primary-600 text-white' : 'bg-white text-neutral-700'
                  }`}
                  onClick={() => setViewMode('grid')}
                >
                  <GridIcon className="h-5 w-5 mr-2" />
                  Grille
                </button>
                <button
                  type="button"
                  className={`flex-1 py-2 px-4 flex justify-center items-center ${
                    viewMode === 'list' ? 'bg-primary-600 text-white' : 'bg-white text-neutral-700'
                  }`}
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-5 w-5 mr-2" />
                  Liste
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Liste des professionnels */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-lg font-semibold text-red-700 mb-2">Erreur</h2>
          <p className="text-red-600">{error}</p>
          <Button
            variant="primary"
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Réessayer
          </Button>
        </div>
      ) : filteredProfessionals.length === 0 ? (
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-8 text-center">
          <Search className="h-16 w-16 text-neutral-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-neutral-800 mb-2">Aucun professionnel trouvé</h2>
          <p className="text-neutral-600 mb-6">
            Aucun professionnel ne correspond à vos critères de recherche. Essayez de modifier vos filtres.
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4">
            <div className="flex justify-between items-center">
              <p className="text-neutral-600">
                {filteredProfessionals.length} professionnel{filteredProfessionals.length > 1 ? 's' : ''} trouvé{filteredProfessionals.length > 1 ? 's' : ''}
              </p>
              <div className="flex items-center text-sm text-neutral-500">
                <span>Affichage de {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, filteredProfessionals.length)} sur {filteredProfessionals.length}</span>
              </div>
            </div>
          </div>

          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {currentItems.map((professional) => (
                <div
                  key={professional.id}
                  className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer"
                  onClick={() => handleViewProfessional(professional.id)}
                >
                  <div className="h-48 overflow-hidden">
                    <img
                      src={professional.imageUrl}
                      alt={`${professional.firstName} ${professional.lastName}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center">
                        <Avatar
                          size="md"
                          fallback={`${professional.firstName[0]}${professional.lastName[0]}`}
                          className="mr-3 border-2 border-white shadow-sm"
                        />
                        <div>
                          <h3 className="font-semibold text-neutral-900">{professional.firstName} {professional.lastName}</h3>
                          <p className="text-sm text-neutral-600">{professional.title}</p>
                        </div>
                      </div>
                      {getAvailabilityBadge(professional.availability)}
                    </div>
                    <div className="flex items-center text-sm text-neutral-600 mb-3">
                      <MapPin className="h-4 w-4 mr-1" />
                      <span>{professional.location}</span>
                    </div>
                    <div className="flex flex-wrap gap-1 mb-3">
                      {professional.skills.slice(0, 3).map((skill, index) => (
                        <Badge key={index} color="blue" className="text-xs">{skill}</Badge>
                      ))}
                      {professional.skills.length > 3 && (
                        <Badge color="gray" className="text-xs">+{professional.skills.length - 3}</Badge>
                      )}
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <Star className="h-4 w-4 text-yellow-500 fill-current mr-1" />
                        <span className="font-medium">{professional.rating}</span>
                        <span className="text-neutral-500 text-sm ml-1">({professional.reviewCount})</span>
                      </div>
                      <div className="font-medium text-neutral-900">{professional.hourlyRate}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
              <div className="divide-y divide-neutral-200">
                {currentItems.map((professional) => (
                  <div
                    key={professional.id}
                    className="p-4 hover:bg-neutral-50 transition-colors duration-200 cursor-pointer"
                    onClick={() => handleViewProfessional(professional.id)}
                  >
                    <div className="flex items-start">
                      <div className="flex-shrink-0 mr-4">
                        <Avatar
                          size="lg"
                          src={professional.imageUrl}
                          fallback={`${professional.firstName[0]}${professional.lastName[0]}`}
                        />
                      </div>
                      <div className="flex-grow">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-semibold text-neutral-900">{professional.firstName} {professional.lastName}</h3>
                            <p className="text-neutral-600">{professional.title}</p>
                          </div>
                          <div className="flex items-center">
                            {getAvailabilityBadge(professional.availability)}
                            <div className="ml-3 font-medium text-neutral-900">{professional.hourlyRate}</div>
                          </div>
                        </div>
                        <div className="flex items-center text-sm text-neutral-600 mt-1 mb-2">
                          <MapPin className="h-4 w-4 mr-1" />
                          <span>{professional.location}</span>
                          <div className="mx-2 h-1 w-1 rounded-full bg-neutral-300"></div>
                          <div className="flex items-center">
                            <Star className="h-4 w-4 text-yellow-500 fill-current mr-1" />
                            <span className="font-medium">{professional.rating}</span>
                            <span className="text-neutral-500 text-sm ml-1">({professional.reviewCount})</span>
                          </div>
                          <div className="mx-2 h-1 w-1 rounded-full bg-neutral-300"></div>
                          <div className="flex items-center">
                            <Check className="h-4 w-4 text-green-500 mr-1" />
                            <span>{professional.completedProjects} projets terminés</span>
                          </div>
                        </div>
                        <p className="text-sm text-neutral-600 line-clamp-2 mb-2">
                          {professional.description}
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {professional.skills.map((skill, index) => (
                            <Badge key={index} color="blue" className="text-xs">{skill}</Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 flex justify-between items-center">
              <Button
                variant="ghost"
                size="sm"
                leftIcon={<ChevronLeft className="h-4 w-4" />}
                onClick={() => paginate(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Précédent
              </Button>
              <div className="flex items-center space-x-2">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((number) => (
                  <button
                    key={number}
                    type="button"
                    onClick={() => paginate(number)}
                    className={`px-3 py-1 rounded-md ${
                      currentPage === number
                        ? 'bg-primary-600 text-white'
                        : 'text-neutral-600 hover:bg-neutral-100'
                    }`}
                  >
                    {number}
                  </button>
                ))}
              </div>
              <Button
                variant="ghost"
                size="sm"
                rightIcon={<ChevronRight className="h-4 w-4" />}
                onClick={() => paginate(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Suivant
              </Button>
            </div>
          )}
        </div>
      )}
    </DashboardLayout>
  );
};

export default AllProfessionalsPage;
