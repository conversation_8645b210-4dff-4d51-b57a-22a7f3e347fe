import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Briefcase,
  Calendar,
  Clock,
  DollarSign,
  FileText,
  Upload,
  X,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from './DashboardLayout';
import Button from '../ui/Button';

interface Category {
  id: number;
  name: string;
}

const CreateProject: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    budget: '',
    deadline: '',
    skills: [] as string[],
    attachments: [] as File[]
  });

  // New skill input
  const [newSkill, setNewSkill] = useState('');

  // Categories (would typically come from an API)
  const [categories] = useState<Category[]>([
    { id: 1, name: 'Modélisation 3D' },
    { id: 2, name: 'Animation 3D' },
    { id: 3, name: 'Rendu 3D' },
    { id: 4, name: 'Texturing' },
    { id: 5, name: 'Rigging' },
    { id: 6, name: 'Conception de personnages' },
    { id: 7, name: 'Environnements virtuels' },
    { id: 8, name: 'Effets spéciaux' },
    { id: 9, name: 'Réalité virtuelle' },
    { id: 10, name: 'Réalité augmentée' }
  ]);

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle file uploads
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setFormData(prev => ({
        ...prev,
        attachments: [...prev.attachments, ...newFiles]
      }));
    }
  };

  // Remove an attachment
  const handleRemoveAttachment = (index: number) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
  };

  // Add a skill
  const handleAddSkill = () => {
    if (newSkill.trim() && !formData.skills.includes(newSkill.trim())) {
      setFormData(prev => ({
        ...prev,
        skills: [...prev.skills, newSkill.trim()]
      }));
      setNewSkill('');
    }
  };

  // Remove a skill
  const handleRemoveSkill = (skillToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.filter(skill => skill !== skillToRemove)
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.title.trim()) {
      setError('Le titre du projet est requis');
      return;
    }

    if (!formData.description.trim()) {
      setError('La description du projet est requise');
      return;
    }

    if (!formData.category) {
      setError('La catégorie du projet est requise');
      return;
    }

    if (!formData.budget.trim()) {
      setError('Le budget du projet est requis');
      return;
    }

    if (!formData.deadline) {
      setError('La date limite du projet est requise');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      // Create FormData object for file uploads
      const projectFormData = new FormData();
      projectFormData.append('title', formData.title);
      projectFormData.append('description', formData.description);
      projectFormData.append('category', formData.category);
      projectFormData.append('budget', formData.budget);
      projectFormData.append('deadline', formData.deadline);

      // Add skills
      formData.skills.forEach((skill, index) => {
        projectFormData.append(`skills[${index}]`, skill);
      });

      // Add attachments
      formData.attachments.forEach((file, index) => {
        projectFormData.append(`attachments[${index}]`, file);
      });

      // Send request to API
      console.log('Sending project data to API:', `${API_BASE_URL}/api/dashboard/projects`);
      console.log('Project data:', {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        budget: formData.budget,
        deadline: formData.deadline,
        skills: formData.skills,
        attachments: formData.attachments.map(f => f.name)
      });

      const response = await fetch(`${API_BASE_URL}/api/dashboard/projects`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: projectFormData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de la création du projet');
      }

      const data = await response.json();

      setSuccess('Projet créé avec succès!');

      // Redirect to project page after a short delay
      setTimeout(() => {
        navigate(`/dashboard/projects/${data.project.id}`);
      }, 1500);

    } catch (err) {
      console.error('Error creating project:', err);
      setError(err instanceof Error ? err.message : 'Une erreur est survenue lors de la création du projet');
    } finally {
      setLoading(false);
    }
  };

  return (
    <DashboardLayout
      title="Créer un nouveau projet"
      subtitle="Décrivez votre projet pour trouver les meilleurs professionnels"
      actions={
        <Button
          variant="outline"
          onClick={() => navigate('/dashboard')}
        >
          Annuler
        </Button>
      }
    >
      {/* Alert messages */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-start">
          <AlertTriangle className="h-5 w-5 text-red-500 mr-3 mt-0.5" />
          <div>
            <p className="text-red-800 font-medium">Erreur</p>
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 flex items-start">
          <CheckCircle className="h-5 w-5 text-green-500 mr-3 mt-0.5" />
          <div>
            <p className="text-green-800 font-medium">Succès</p>
            <p className="text-green-700 text-sm">{success}</p>
          </div>
        </div>
      )}

      <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden mb-6">
        <div className="p-6">
          <form onSubmit={handleSubmit}>
            <div className="space-y-6">
              {/* Project title */}
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-neutral-700 mb-1">
                  Titre du projet *
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Ex: Création d'un personnage 3D pour jeu vidéo"
                  required
                />
              </div>

              {/* Project description */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-neutral-700 mb-1">
                  Description du projet *
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={5}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Décrivez votre projet en détail. Incluez les objectifs, les exigences et toute information pertinente."
                  required
                />
              </div>

              {/* Project category */}
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-neutral-700 mb-1">
                  Catégorie *
                </label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  required
                >
                  <option value="">Sélectionnez une catégorie</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Project budget */}
              <div>
                <label htmlFor="budget" className="block text-sm font-medium text-neutral-700 mb-1">
                  Budget (€) *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <DollarSign className="h-5 w-5 text-neutral-400" />
                  </div>
                  <input
                    type="text"
                    id="budget"
                    name="budget"
                    value={formData.budget}
                    onChange={handleChange}
                    className="w-full pl-10 px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Ex: 1000"
                    required
                  />
                </div>
              </div>

              {/* Project deadline */}
              <div>
                <label htmlFor="deadline" className="block text-sm font-medium text-neutral-700 mb-1">
                  Date limite *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Calendar className="h-5 w-5 text-neutral-400" />
                  </div>
                  <input
                    type="date"
                    id="deadline"
                    name="deadline"
                    value={formData.deadline}
                    onChange={handleChange}
                    className="w-full pl-10 px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    required
                  />
                </div>
              </div>

              {/* Project skills */}
              <div>
                <label htmlFor="skills" className="block text-sm font-medium text-neutral-700 mb-1">
                  Compétences requises
                </label>
                <div className="flex">
                  <input
                    type="text"
                    id="skills"
                    value={newSkill}
                    onChange={(e) => setNewSkill(e.target.value)}
                    className="flex-1 px-4 py-2 border border-neutral-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Ex: Modélisation 3D"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddSkill();
                      }
                    }}
                  />
                  <button
                    type="button"
                    onClick={handleAddSkill}
                    className="px-4 py-2 bg-primary-600 text-white rounded-r-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                  >
                    Ajouter
                  </button>
                </div>

                {formData.skills.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-3">
                    {formData.skills.map((skill, index) => (
                      <div
                        key={index}
                        className="flex items-center bg-primary-50 text-primary-700 px-3 py-1 rounded-full text-sm"
                      >
                        {skill}
                        <button
                          type="button"
                          onClick={() => handleRemoveSkill(skill)}
                          className="ml-2 text-primary-500 hover:text-primary-700 focus:outline-none"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Project attachments */}
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">
                  Pièces jointes
                </label>
                <div className="border-2 border-dashed border-neutral-300 rounded-md p-6 text-center">
                  <Upload className="h-8 w-8 text-neutral-400 mx-auto mb-2" />
                  <p className="text-sm text-neutral-600 mb-1">
                    Glissez-déposez des fichiers ici ou
                  </p>
                  <div>
                    <label
                      htmlFor="file-upload"
                      className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 cursor-pointer"
                    >
                      Parcourir
                      <input
                        id="file-upload"
                        type="file"
                        multiple
                        onChange={handleFileChange}
                        className="hidden"
                      />
                    </label>
                  </div>
                  <p className="text-xs text-neutral-500 mt-2">
                    PNG, JPG, PDF, DOC jusqu'à 10MB
                  </p>
                </div>

                {formData.attachments.length > 0 && (
                  <div className="mt-4 space-y-2">
                    {formData.attachments.map((file, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between bg-neutral-50 border border-neutral-200 rounded-md p-3"
                      >
                        <div className="flex items-center">
                          <FileText className="h-5 w-5 text-neutral-500 mr-3" />
                          <div>
                            <p className="text-sm font-medium text-neutral-700">{file.name}</p>
                            <p className="text-xs text-neutral-500">{(file.size / 1024).toFixed(2)} KB</p>
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={() => handleRemoveAttachment(index)}
                          className="text-neutral-400 hover:text-red-500 focus:outline-none"
                        >
                          <X className="h-5 w-5" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Submit button */}
              <div className="flex justify-end mt-6">
                <Button
                  type="submit"
                  variant="primary"
                  disabled={loading}
                  className="w-full md:w-auto py-3 px-6 text-lg font-semibold"
                  style={{ backgroundColor: '#2980b9', color: 'white', borderRadius: '0.5rem' }}
                >
                  {loading ? 'Création en cours...' : 'Créer le projet'}
                </Button>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Tips section */}
      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-500 mr-3 mt-0.5" />
          <div>
            <p className="text-blue-800 font-medium">Conseils pour un projet réussi</p>
            <ul className="text-blue-700 text-sm mt-2 space-y-1 list-disc list-inside">
              <li>Soyez précis dans votre description pour attirer les bons professionnels</li>
              <li>Définissez un budget réaliste pour la qualité que vous recherchez</li>
              <li>Ajoutez des références visuelles ou des exemples pour clarifier vos attentes</li>
              <li>Précisez les délais et les étapes importantes du projet</li>
            </ul>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default CreateProject;
