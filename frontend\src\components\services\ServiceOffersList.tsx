import React, { useState } from 'react';
import { Search, Filter, Plus, Briefcase } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Button from '../ui/Button';
import ServiceOfferCard from './ServiceOfferCard';
import type { ServiceOffer } from './types';

interface ServiceOffersListProps {
  services: ServiceOffer[];
  isLoading?: boolean;
  error?: string;
  onServiceClick: (serviceId: number) => void;
  onCreateService?: () => void;
  onFilterChange?: (filters: any) => void;
  onSearchChange?: (query: string) => void;
  onSelectService:(service:ServiceOffer) => void;
  emptyMessage?: string;
}

const ServiceOffersList: React.FC<ServiceOffersListProps> = ({
  services,
  isLoading = false,
  error,
  onServiceClick,
  onCreateService,
  onFilterChange,
  onSelectService,
  onSearchChange,
  emptyMessage = 'Aucun service disponible',
}) => {
  const navigate = useNavigate();

  // Fonction pour créer un service directement
  const handleCreateServiceDirect = () => {
    console.log("Navigating directly to create service page");
    // Utiliser window.location pour forcer un rechargement complet et éviter les problèmes de routage
    window.location.href = '/dashboard/services/create';
  };
  // State for search and filters
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [showFilters, setShowFilters] = useState(false);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    if (onSearchChange) {
      onSearchChange(query);
    }
  };

  // Handle status filter change
  const handleStatusFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value);
  };

  // Handle category filter change
  const handleCategoryFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setCategoryFilter(e.target.value);
  };

  // Apply filters to services
  const filteredServices = services.filter(service => {
    // Apply search filter
    const matchesSearch =
      service.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      service.description.toLowerCase().includes(searchQuery.toLowerCase());

    // Apply status filter
    const matchesStatus = statusFilter === 'all' || service.status === statusFilter;

    // Apply category filter
    const matchesCategory = categoryFilter === 'all' ||
      (service.categories && service.categories.includes(categoryFilter));

    return matchesSearch && matchesStatus && matchesCategory;
  });

  // Get unique categories from services
  const categoriesArray = services.flatMap(service => service.categories || []);
  const categories = Array.from(new Set(categoriesArray));

  return (
    <div className="space-y-6">
      {/* Search and filters */}
      <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
        <div className="p-4">
          <div className="flex flex-col md:flex-row md:items-center space-y-3 md:space-y-0 md:space-x-4">
            {/* Search input */}
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-neutral-400" />
              </div>
              <input
                type="text"
                placeholder="Rechercher un service..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="block w-full pl-10 pr-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* Filter button */}
            <Button
              variant="outline"
              leftIcon={<Filter className="h-5 w-5" />}
              onClick={() => setShowFilters(!showFilters)}
              className="md:w-auto"
            >
              Filtres
              {showFilters && <span className="ml-1 text-xs">▲</span>}
              {!showFilters && <span className="ml-1 text-xs">▼</span>}
            </Button>

            {/* Create service button - only for professionals */}
            {JSON.parse(localStorage.getItem('user') || '{}').role === 'professional' && (
              <Button
                variant="primary"
                leftIcon={<Plus className="h-5 w-5" />}
                onClick={handleCreateServiceDirect}
                style={{ backgroundColor: '#2980b9', color: 'black' }}
              >
                Créer un service
              </Button>
            )}
          </div>

          {/* Expanded filters */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-neutral-200 grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Status filter */}
              <div>
                <label htmlFor="statusFilter" className="block text-sm font-medium text-neutral-700 mb-1">
                  Statut
                </label>
                <select
                  id="statusFilter"
                  value={statusFilter}
                  onChange={handleStatusFilterChange}
                  className="block w-full pl-3 pr-10 py-2 border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="all">Tous les statuts</option>
                  <option value="draft">Brouillon</option>
                  <option value="published">Publié</option>
                  <option value="archived">Archivé</option>
                </select>
              </div>

              {/* Category filter */}
              <div>
                <label htmlFor="categoryFilter" className="block text-sm font-medium text-neutral-700 mb-1">
                  Catégorie
                </label>
                <select
                  id="categoryFilter"
                  value={categoryFilter}
                  onChange={handleCategoryFilterChange}
                  className="block w-full pl-3 pr-10 py-2 border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="all">Toutes les catégories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      )}

      {/* Error state */}
      {error && !isLoading && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-700">
          <p>{error}</p>
        </div>
      )}

      {/* Empty state */}
      {!isLoading && !error && filteredServices.length === 0 && (
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-12 text-center">
          <div className="mx-auto w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mb-4">
            <Briefcase className="h-8 w-8 text-neutral-400" />
          </div>
          <h3 className="text-lg font-medium text-neutral-700 mb-2">{emptyMessage}</h3>
          {JSON.parse(localStorage.getItem('user') || '{}').role === 'professional' && (
            <div className="mt-4">
              <Button
                variant="primary"
                onClick={handleCreateServiceDirect}
                style={{ backgroundColor: '#2980b9', color: 'black' }}
              >
                Créer un service
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Services grid */}
      {!isLoading && !error && filteredServices.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredServices.map(service => (
            <ServiceOfferCard
              key={service.id}
              service={service}
              onClick={()=>onSelectService(service)}
              // onClick={() => onServiceClick(service.id)}
            />
          ))}

          {/* Create service card - only for professionals */}
          {JSON.parse(localStorage.getItem('user') || '{}').role === 'professional' && (
            <div
              className="bg-white rounded-lg border-2 border-dashed border-neutral-300 shadow-sm p-8 flex flex-col items-center justify-center cursor-pointer hover:border-primary-500 transition-colors"
              onClick={handleCreateServiceDirect}
            >
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mb-4">
                <Plus className="h-6 w-6 text-primary-600" />
              </div>
              <h3 className="text-lg font-medium text-neutral-700 mb-1">Créer un service</h3>
              <p className="text-sm text-neutral-500 text-center">
                Proposez vos compétences et services aux clients
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ServiceOffersList;
