import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { API_BASE_URL } from '../../config';
import Header from '../Header';
import Footer from '../Footer';
import ChatList, { ChatPreview } from './ChatList';
import ChatWindow from './ChatWindow';
import { MessageProps } from './ChatMessage';
import Button from '../ui/Button';
import { MessageSquare, Plus } from 'lucide-react';

const MessagingPage: React.FC = () => {
  const { id } = useParams<{ id?: string }>();
  const navigate = useNavigate();
  const [activeChat, setActiveChat] = useState<number | null>(id ? parseInt(id) : null);
  const [chats, setChats] = useState<ChatPreview[]>([]);
  const [messages, setMessages] = useState<MessageProps[]>([]);
  const [loading, setLoading] = useState(true);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentChatInfo, setCurrentChatInfo] = useState<{
    title: string;
    subtitle?: string;
    avatar?: string;
  } | null>(null);

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');
  const isClient = user?.is_professional === false;

  // Fetch conversations list
  useEffect(() => {
    const fetchConversations = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/messages/conversation/${user.id}`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des conversations');
        }

        const data = await response.json();

        // Transform API data to ChatPreview format
        const chatPreviews: ChatPreview[] = data.conversations.map((conv: any) => {
          const otherUser = isClient ? conv.professional : conv.client;

          return {
            id: conv.offer_id,
            title: `${otherUser.first_name} ${otherUser.last_name}`,
            avatar: otherUser.avatar || undefined,
            lastMessage: conv.last_message?.message_text || 'Aucun message',
            timestamp: conv.last_message?.created_at,
            unreadCount: conv.unread_count || 0,
            status: otherUser.is_online ? 'online' : 'offline',
          };
        });

        setChats(chatPreviews);

        // If no active chat is set but we have conversations, set the first one as active
        if (!activeChat && chatPreviews.length > 0) {
          setActiveChat(chatPreviews[0].id);
        }
      } catch (err) {
        console.error('Error fetching conversations:', err);
        setError('Impossible de charger les conversations');
      }
    };

    fetchConversations();
  }, [token, isClient, activeChat]);

  // Fetch messages for active chat
  useEffect(() => {
    if (!activeChat) return;

    const fetchMessages = async () => {
      setLoading(true);
      try {
        const queryParam = !isClient ? `?professional_id=${user.id}` : '';
        const response = await fetch(`${API_BASE_URL}/api/open-offers/${activeChat}/messages${queryParam}`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des messages');
        }

        const data = await response.json();

        // Find current chat info
        const currentChat = chats.find(chat => chat.id === activeChat);
        if (currentChat) {
          setCurrentChatInfo({
            title: currentChat.title,
            avatar: currentChat.avatar,
          });
        }

        // Transform API data to MessageProps format
        const messageList: MessageProps[] = data.messages.map((msg: any) => ({
          id: msg.id,
          text: msg.message_text,
          timestamp: msg.created_at,
          sender: {
            id: msg.sender_id,
            name: `${msg.sender.first_name} ${msg.sender.last_name}`,
            avatar: msg.sender.avatar || undefined,
          },
          isCurrentUser: msg.sender_id === user.id,
          status: 'read', // Assuming all messages are read for now
        }));

        setMessages(messageList);
        setError(null);
      } catch (err) {
        console.error('Error fetching messages:', err);
        setError('Impossible de charger les messages');
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();
  }, [activeChat, token, isClient, user.id, chats]);

  // Handle sending a message
  const handleSendMessage = async (message: string) => {
    if (!activeChat || !message.trim()) return;

    setSendingMessage(true);
    try {
      const bodyData: any = {
        message_text: message,
      };

      if (isClient) {
        // Get the professional ID from the current chat
        const currentChat = chats.find(chat => chat.id === activeChat);
        if (currentChat) {
          const receiverId = localStorage.getItem('receiver_id');
          if (!receiverId) {
            throw new Error("ID du professionnel à contacter manquant");
          }
          bodyData.receiver_id = parseInt(receiverId);
        }
      }

      const response = await fetch(`${API_BASE_URL}/api/open-offers/${activeChat}/messages`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bodyData),
      });

      if (!response.ok) {
        throw new Error("Erreur lors de l'envoi du message");
      }

      // Add the new message to the list
      const data = await response.json();
      const newMessage: MessageProps = {
        id: data.message.id,
        text: data.message.message_text,
        timestamp: data.message.created_at,
        sender: {
          id: user.id,
          name: `${user.first_name} ${user.last_name}`,
          avatar: user.avatar || undefined,
        },
        isCurrentUser: true,
        status: 'sent',
      };

      setMessages(prev => [...prev, newMessage]);

      // Update the chat preview with the new message
      setChats(prev =>
        prev.map(chat =>
          chat.id === activeChat
            ? {
                ...chat,
                lastMessage: message,
                timestamp: new Date().toISOString()
              }
            : chat
        )
      );
    } catch (err) {
      console.error('Error sending message:', err);
      setError("Échec de l'envoi du message");
    } finally {
      setSendingMessage(false);
    }
  };

  // Handle chat selection
  const handleChatSelect = (chatId: number) => {
    setActiveChat(chatId);
    navigate(`/discussions/${chatId}`);
  };

  // Handle creating a new conversation
  const handleNewConversation = () => {
    navigate('/lists-independants');
  };

  return (
    <div className="flex flex-col min-h-screen bg-neutral-50">
      <Header />

      <div className="flex-1 container mx-auto py-6 px-4">
        <div className="bg-white rounded-lg shadow-sm overflow-hidden border border-neutral-200 h-[calc(100vh-200px)]">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 h-full">
            {/* Chat List - Hidden on mobile when a chat is active */}
            <div className={`md:col-span-1 ${activeChat ? 'hidden md:block' : ''}`}>
              <div className="h-full flex flex-col">
                <ChatList
                  chats={chats}
                  onChatSelect={handleChatSelect}
                  activeChat={activeChat || undefined}
                  onSearch={(query) => console.log('Search:', query)}
                />

                <div className="p-4 border-t border-neutral-200">
                  <Button
                    variant="primary"
                    fullWidth
                    leftIcon={<Plus className="h-5 w-5" />}
                    onClick={handleNewConversation}
                    style={{ backgroundColor: '#2980b9', color: 'white', padding: '0.75rem 1.5rem', fontWeight: 'bold', borderRadius: '0.5rem' }}
                  >
                    Nouvelle conversation
                  </Button>
                </div>
              </div>
            </div>

            {/* Chat Window or Empty State */}
            <div className={`md:col-span-2 lg:col-span-3 h-full ${!activeChat ? 'hidden md:flex' : ''}`}>
              {activeChat && currentChatInfo ? (
                <ChatWindow
                  chatId={activeChat}
                  title={currentChatInfo.title}
                  subtitle="En ligne"
                  avatar={currentChatInfo.avatar}
                  status="online"
                  messages={messages}
                  isLoading={loading}
                  onSendMessage={handleSendMessage}
                  onBack={() => {
                    setActiveChat(null);
                    navigate('/discussions');
                  }}
                />
              ) : (
                <div className="flex flex-col items-center justify-center h-full p-6 text-center">
                  <div className="bg-neutral-100 p-4 rounded-full mb-4">
                    <MessageSquare className="h-10 w-10 text-neutral-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-neutral-800 mb-2">
                    Aucune conversation sélectionnée
                  </h3>
                  <p className="text-neutral-600 mb-6 max-w-md">
                    Sélectionnez une conversation existante ou commencez-en une nouvelle avec un professionnel.
                  </p>
                  <Button
                    variant="primary"
                    leftIcon={<Plus className="h-5 w-5" />}
                    onClick={handleNewConversation}
                    style={{ backgroundColor: '#2980b9', color: 'white', padding: '0.75rem 1.5rem', fontWeight: 'bold', borderRadius: '0.5rem' }}
                  >
                    Nouvelle conversation
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default MessagingPage;
