import React from 'react';
import Header from '../Header';
import Footer from '../Footer';
import Section from '../layout/Section';
import Container from '../layout/Container';
import Grid from '../layout/Grid';
import { Users, Target, Award, Heart } from 'lucide-react';

const AboutPage: React.FC = () => {
  // Données pour l'équipe
  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'Fondateur & CEO',
      bio: 'Passionné par l\'art 3D depuis plus de 15 ans, Alexandre a fondé Hi 3D Artist pour créer un pont entre les artistes talentueux et les entreprises à la recherche d\'excellence.',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=300&h=300&q=80',
    },
    {
      name: '<PERSON>',
      role: 'Directrice Artistique',
      bio: 'Avec son expérience dans les plus grands studios d\'animation, Marie apporte son expertise pour évaluer et mettre en valeur les talents de notre plateforme.',
      image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=300&h=300&q=80',
    },
    {
      name: 'Thomas Moreau',
      role: 'Responsable Technique',
      bio: 'Expert en développement web et en technologies 3D, Thomas veille à ce que notre plateforme offre la meilleure expérience possible à nos utilisateurs.',
      image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=300&h=300&q=80',
    },
    {
      name: 'Sophie Petit',
      role: 'Responsable Relations Clients',
      bio: 'Sophie s\'assure que chaque client et artiste trouve exactement ce qu\'il recherche sur notre plateforme, avec un accompagnement personnalisé.',
      image: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=300&h=300&q=80',
    },
  ];

  // Données pour les valeurs
  const values = [
    {
      title: 'Excellence',
      description: 'Nous nous engageons à promouvoir l\'excellence dans tous les aspects de la création 3D, en mettant en avant les meilleurs talents et projets.',
      icon: <Award className="h-8 w-8 text-primary-600" />,
    },
    {
      title: 'Communauté',
      description: 'Nous croyons en la force de la communauté et encourageons le partage de connaissances et la collaboration entre les artistes.',
      icon: <Users className="h-8 w-8 text-primary-600" />,
    },
    {
      title: 'Innovation',
      description: 'Nous sommes constamment à la recherche de nouvelles technologies et approches pour faire évoluer l\'industrie de la 3D.',
      icon: <Target className="h-8 w-8 text-primary-600" />,
    },
    {
      title: 'Passion',
      description: 'La passion pour l\'art 3D est au cœur de notre entreprise et guide toutes nos décisions et actions.',
      icon: <Heart className="h-8 w-8 text-primary-600" />,
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <Header />

      {/* Hero Section */}
      <div className="bg-primary-50 py-16 md:py-24">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              Qui sommes-nous ?
            </h1>
            <p className="text-neutral-600 text-lg mb-0">
              Découvrez l'histoire et la mission de Hi 3D Artist, la plateforme qui révolutionne la mise en relation entre artistes 3D et entreprises.
            </p>
          </div>
        </Container>
      </div>

      {/* Notre Histoire Section */}
      <Section background="white" spacing="xl">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold text-neutral-900 mb-6 text-center">Notre Histoire</h2>
          <div className="prose prose-lg mx-auto">
            <p>
              Hi 3D Artist est né en 2020 d'une vision simple mais ambitieuse : créer un écosystème où les artistes 3D talentueux peuvent s'épanouir et où les entreprises peuvent trouver facilement les compétences dont elles ont besoin pour leurs projets.
            </p>
            <p>
              Fondée par Alexandre Dupont, un passionné d'art 3D avec plus de 15 ans d'expérience dans l'industrie, notre plateforme a rapidement grandi pour devenir une référence dans le domaine de la création 3D.
            </p>
            <p>
              Aujourd'hui, Hi 3D Artist rassemble plus de 500 artistes talentueux et a permis la réalisation de plus de 1 200 projets pour des clients du monde entier, des startups aux grandes entreprises.
            </p>
          </div>
        </div>
      </Section>

      {/* Notre Mission Section */}
      <Section background="light" spacing="xl">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-neutral-900 mb-6">Notre Mission</h2>
          <p className="text-xl text-neutral-700 mb-8">
            Connecter les meilleurs talents de la 3D avec des opportunités exceptionnelles, tout en favorisant l'innovation et la créativité dans l'industrie.
          </p>
          <div className="bg-white p-8 rounded-lg shadow-sm border border-neutral-200">
            <p className="italic text-neutral-600">
              "Nous croyons que chaque artiste 3D mérite de trouver des projets qui correspondent à ses compétences et à ses passions, et que chaque entreprise mérite d'accéder aux meilleurs talents pour concrétiser sa vision."
            </p>
            <p className="font-semibold mt-4">— Alexandre Dupont, Fondateur</p>
          </div>
        </div>
      </Section>

      {/* Nos Valeurs Section */}
      <Section background="white" spacing="xl">
        <h2 className="text-3xl font-bold text-neutral-900 mb-12 text-center">Nos Valeurs</h2>
        <Grid cols={1} mdCols={2} gap={8}>
          {values.map((value, index) => (
            <div key={index} className="flex items-start p-6 bg-white rounded-lg border border-neutral-200 shadow-sm">
              <div className="mr-4 bg-primary-50 p-3 rounded-full">
                {value.icon}
              </div>
              <div>
                <h3 className="text-xl font-semibold mb-2">{value.title}</h3>
                <p className="text-neutral-600">{value.description}</p>
              </div>
            </div>
          ))}
        </Grid>
      </Section>

      {/* Notre Équipe Section */}
      <Section background="light" spacing="xl">
        <h2 className="text-3xl font-bold text-neutral-900 mb-12 text-center">Notre Équipe</h2>
        <Grid cols={1} mdCols={2} lgCols={4} gap={8}>
          {teamMembers.map((member, index) => (
            <div key={index} className="bg-white rounded-lg overflow-hidden shadow-sm border border-neutral-200 transition-shadow hover:shadow-md">
              <div className="aspect-square overflow-hidden">
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-1">{member.name}</h3>
                <p className="text-primary-600 font-medium mb-3">{member.role}</p>
                <p className="text-neutral-600 text-sm">{member.bio}</p>
              </div>
            </div>
          ))}
        </Grid>
      </Section>

      {/* Rejoignez-nous Section */}
      <div className="py-12 md:py-16" style={{ backgroundColor: '#3399FF' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold mb-6" style={{ color: '#FFFFFF' }}>Rejoignez l'aventure</h2>
            <p className="mb-8" style={{ color: '#FFFFFF' }}>
              Que vous soyez un artiste 3D talentueux ou une entreprise à la recherche d'excellence, Hi 3D Artist vous ouvre ses portes pour une collaboration fructueuse.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <a
                href="/register"
                className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md hover:bg-neutral-100"
                style={{ backgroundColor: '#FFFFFF', color: '#3399FF' }}
              >
                Créer un compte
              </a>
              <a
                href="/contact"
                className="inline-flex items-center justify-center px-6 py-3 border text-base font-medium rounded-md hover:bg-white hover:bg-opacity-10"
                style={{ color: '#FFFFFF', borderColor: '#FFFFFF' }}
              >
                Nous contacter
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default AboutPage;
