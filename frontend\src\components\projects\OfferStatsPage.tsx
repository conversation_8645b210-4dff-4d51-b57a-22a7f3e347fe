import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Bar<PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  TooltipProps
} from 'recharts';
import { Calendar, Filter, Download } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import OfferStatsCard from '../dashboard/OfferStatsCard';
import Button from '../ui/Button';
import Alert from '../ui/Alert';

interface OfferStats {
  total: number;
  interested: number;
  notAvailable: number;
  pending: number;
  selected: number;
}

interface MonthlyStats {
  month: string;
  total: number;
  interested: number;
  notAvailable: number;
  selected: number;
}

const OfferStatsPage: React.FC = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<OfferStats>({
    total: 0,
    interested: 0,
    notAvailable: 0,
    pending: 0,
    selected: 0,
  });
  const [monthlyStats, setMonthlyStats] = useState<MonthlyStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [period, setPeriod] = useState<'month' | 'quarter' | 'year'>('month');

  const token = localStorage.getItem('token');
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  const isClient = currentUser.role === 'client';

  useEffect(() => {
    const fetchStats = async () => {
      if (!token) return;

      setLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/stats/offers?period=${period}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des statistiques');
        }

        const data = await response.json();

        // Mettre à jour les statistiques globales
        setStats(data.stats || {
          total: 0,
          interested: 0,
          notAvailable: 0,
          pending: 0,
          selected: 0,
        });

        // Mettre à jour les statistiques mensuelles
        setMonthlyStats(data.monthly_stats || []);
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger les statistiques');

        // Utiliser des données de secours
        loadStaticData();
      } finally {
        setLoading(false);
      }
    };

    // Fonction pour charger des données statiques en cas d'erreur
    const loadStaticData = () => {
      // Statistiques globales de secours
      setStats({
        total: 24,
        interested: 15,
        notAvailable: 5,
        pending: 4,
        selected: 8,
      });

      // Statistiques mensuelles de secours
      const months = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin'];
      const mockMonthlyStats = months.map((month, index) => ({
        month,
        total: 4 + Math.floor(Math.random() * 3),
        interested: 2 + Math.floor(Math.random() * 3),
        notAvailable: 1 + Math.floor(Math.random() * 2),
        selected: 1 + Math.floor(Math.random() * 2),
      }));

      setMonthlyStats(mockMonthlyStats);
    };

    fetchStats();
  }, [token, period]);

  // Gérer le changement de période
  const handlePeriodChange = (newPeriod: 'month' | 'quarter' | 'year') => {
    setPeriod(newPeriod);
  };

  // Gérer l'exportation des statistiques
  const handleExportStats = () => {
    // Créer un objet contenant toutes les statistiques
    const exportData = {
      global: stats,
      monthly: monthlyStats,
      exportDate: new Date().toISOString(),
      user: {
        id: currentUser.id,
        name: currentUser.name,
        role: currentUser.role,
      },
    };

    // Convertir en JSON
    const jsonData = JSON.stringify(exportData, null, 2);

    // Créer un blob et un lien de téléchargement
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `statistiques-appels-offre-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <DashboardLayout
      title="Statistiques des appels d'offre"
      subtitle={isClient ? "Analysez les performances de vos appels d'offre" : "Suivez les appels d'offre que vous avez reçus"}
      actions={
        <Button
          variant="outline"
          leftIcon={<Download className="h-5 w-5" />}
          onClick={handleExportStats}
        >
          Exporter les statistiques
        </Button>
      }
    >
      {/* Error Alert */}
      {error && (
        <Alert
          type="error"
          title="Erreur"
          onClose={() => setError(null)}
          className="mb-6"
        >
          {error}
        </Alert>
      )}

      {/* Filtres de période */}
      <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden mb-6">
        <div className="p-4">
          <div className="flex items-center">
            <Calendar className="h-5 w-5 text-neutral-500 mr-2" />
            <span className="text-neutral-700 font-medium mr-4">Période:</span>

            <div className="flex space-x-2">
              <Button
                variant={period === 'month' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => handlePeriodChange('month')}
                style={period === 'month' ? { backgroundColor: '#2980b9', color: 'black' } : {}}
              >
                Mois
              </Button>

              <Button
                variant={period === 'quarter' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => handlePeriodChange('quarter')}
                style={period === 'quarter' ? { backgroundColor: '#2980b9', color: 'black' } : {}}
              >
                Trimestre
              </Button>

              <Button
                variant={period === 'year' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => handlePeriodChange('year')}
                style={period === 'year' ? { backgroundColor: '#2980b9', color: 'black' } : {}}
              >
                Année
              </Button>
            </div>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      ) : (
        <>
          {/* Carte de statistiques */}
          <OfferStatsCard
            title="Statistiques globales"
            stats={stats}
            className="mb-6"
          />

          {/* Graphique d'évolution mensuelle */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden mb-6">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h3 className="text-lg font-semibold text-neutral-900">Évolution mensuelle</h3>
            </div>

            <div className="p-6">
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={monthlyStats}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <RechartsTooltip />
                    <Legend />
                    <Line type="monotone" dataKey="total" name="Total" stroke="#6b7280" strokeWidth={2} />
                    <Line type="monotone" dataKey="interested" name="Intéressés" stroke="#4ade80" strokeWidth={2} />
                    <Line type="monotone" dataKey="notAvailable" name="Non disponibles" stroke="#f87171" strokeWidth={2} />
                    <Line type="monotone" dataKey="selected" name="Sélectionnés" stroke="#60a5fa" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* Graphique de répartition par statut */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h3 className="text-lg font-semibold text-neutral-900">Répartition par statut</h3>
            </div>

            <div className="p-6">
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={monthlyStats}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <RechartsTooltip />
                    <Legend />
                    <Bar dataKey="interested" name="Intéressés" fill="#4ade80" />
                    <Bar dataKey="notAvailable" name="Non disponibles" fill="#f87171" />
                    <Bar dataKey="pending" name="En attente" fill="#fbbf24" />
                    <Bar dataKey="selected" name="Sélectionnés" fill="#60a5fa" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        </>
      )}
    </DashboardLayout>
  );
};

export default OfferStatsPage;
