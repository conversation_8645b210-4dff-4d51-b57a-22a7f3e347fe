import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Plus, AlertCircle } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import OpenOffersList from './OpenOffersList';
import OpenOfferForm, { OpenOfferFormData } from './OpenOfferForm';
import OpenOfferDetails from './OpenOfferDetails';
import Button from '../ui/Button';
import Alert from '../ui/Alert';

const OpenOffersManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [offers, setOffers] = useState<any[]>([]);
  const [selectedOffer, setSelectedOffer] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);

  // Vérifier les paramètres dans l'URL
  const searchParams = new URLSearchParams(window.location.search);
  const shouldShowForm = searchParams.get('edit') === 'true' || searchParams.get('create') === 'true';
  const inviteProfessionalId = searchParams.get('invite');

  // Récupérer le token d'authentification
  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isProfessional = user.role === 'professional';
  const isEditMode = !!selectedOffer;

  // Fonction pour récupérer une offre par son ID
  const fetchOfferById = useCallback(async (offerId: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${offerId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération de l\'offre');
      }

      const data = await response.json();
      console.log("Réponse API pour l'offre spécifique:", data);

      if (data.open_offer) {
        // Formater l'offre pour correspondre à notre structure
        const offer = data.open_offer;
        const formattedOffer = {
          id: offer.id,
          title: offer.title,
          description: offer.description,
          categories: typeof offer.categories === 'string' ? JSON.parse(offer.categories) : (offer.categories || []),
          budget: offer.budget,
          deadline: offer.deadline,
          company: offer.company,
          website: offer.website,
          recruitment_type: offer.recruitment_type || 'company',
          open_to_applications: offer.open_to_applications !== false,
          auto_invite: offer.auto_invite || false,
          status: offer.status,
          created_at: offer.created_at,
          updated_at: offer.updated_at,
          views_count: offer.views_count || 0,
          applications_count: offer.applications_count || 0,
          user_id: offer.user_id, // Ajouter l'ID de l'utilisateur propriétaire
          client: offer.user ? {
            id: offer.user.id,
            name: `${offer.user.first_name} ${offer.user.last_name}`,
            avatar: offer.user.profile_picture_path,
          } : null,
          files: offer.files || [],
          filters: offer.filters || {
            languages: [],
            skills: [],
            location: '',
            experience_years: 0,
            availability_status: 'available',
          },
        };

        setSelectedOffer(formattedOffer);

        // Si le paramètre edit=true est présent, afficher le formulaire d'édition
        if (shouldShowForm) {
          setShowForm(true);
        }
      } else {
        setError('Offre non trouvée');
      }
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de charger les détails de l\'offre');
    } finally {
      setIsLoading(false);
    }
  }, [token, shouldShowForm]);

  // Charger les offres au chargement du composant
  useEffect(() => {
    const fetchOffers = async () => {
      setIsLoading(true);
      try {
        const endpoint = isProfessional
          ? `${API_BASE_URL}/api/open-offers`
          : `${API_BASE_URL}/api/client/open-offers`;

        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des offres');
        }

        const data = await response.json();
        console.log("Réponse API pour les offres:", data);
        const apiOffers = data.client_open_offers || data.open_offers || [];
        console.log("API Offers:", apiOffers);

        // Transformer les données de l'API
        const formattedOffers = apiOffers.map((offer: any) => ({
          id: offer.id,
          title: offer.title,
          description: offer.description,
          categories: typeof offer.categories === 'string' ? JSON.parse(offer.categories) : (offer.categories || []),
          budget: offer.budget,
          deadline: offer.deadline,
          company: offer.company,
          website: offer.website,
          recruitment_type: offer.recruitment_type || 'company',
          open_to_applications: offer.open_to_applications !== false,
          auto_invite: offer.auto_invite || false,
          status: offer.status,
          created_at: offer.created_at,
          updated_at: offer.updated_at,
          views_count: offer.views_count || 0,
          applications_count: offer.applications_count || 0,
          user_id: offer.user_id, // Ajouter l'ID de l'utilisateur propriétaire
          client: offer.user ? {
            id: offer.user.id,
            name: `${offer.user.first_name} ${offer.user.last_name}`,
            avatar: offer.user.profile_picture_path,
          } : null,
          files: offer.files || [],
          filters: offer.filters || {
            languages: [],
            skills: [],
            location: '',
            experience_years: 0,
            availability_status: 'available',
          },
        }));

        setOffers(formattedOffers);

        // Si un ID est fourni dans l'URL, sélectionner cette offre
        if (id) {
          console.log("ID recherché:", id);
          console.log("Offres formatées:", formattedOffers);
          console.log("Types des IDs:", formattedOffers.map((offer: any) => typeof offer.id));

          const selectedOffer = formattedOffers.find((offer: any) => {
            console.log(`Comparaison: ${offer.id} (${typeof offer.id}) === ${id} (${typeof id})`);
            return offer.id.toString() === id;
          });

          console.log("Offre sélectionnée:", selectedOffer);

          if (selectedOffer) {
            setSelectedOffer(selectedOffer);
            // Si le paramètre edit=true est présent, afficher le formulaire d'édition
            console.log("shouldShowForm:", shouldShowForm);
            if (shouldShowForm) {
              setShowForm(true);
            }
          } else {
            // Si l'offre n'est pas trouvée dans la liste, essayer de la récupérer directement par son ID
            console.log("Offre non trouvée dans la liste, tentative de récupération directe");
            fetchOfferById(id);
          }
        }
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger les offres');
      } finally {
        setIsLoading(false);
      }
    };

    fetchOffers();
  }, [token, id, isProfessional, fetchOfferById, shouldShowForm]);

  // Gérer la création d'une nouvelle offre
  const handleCreateOffer = () => {
    setSelectedOffer(null);
    setShowForm(true);
  };

  // Vérifier si on doit afficher le formulaire avec invitation automatique
  useEffect(() => {
    // Si le paramètre create=true est présent dans l'URL, afficher le formulaire
    if (searchParams.get('create') === 'true' && !showForm) {
      console.log("Affichage du formulaire de création d'offre");
      setSelectedOffer(null);
      setShowForm(true);

      // Si le paramètre invite est également présent, activer l'option d'invitation automatique
      if (inviteProfessionalId) {
        console.log("Création d'une offre avec invitation automatique pour le professionnel ID:", inviteProfessionalId);

        // Activer l'option d'invitation automatique
        setFormData(prev => ({
          ...prev,
          autoInvite: true
        }));
      }
    }
    // Si seulement le paramètre invite est présent, activer l'option d'invitation automatique
    else if (inviteProfessionalId && !showForm && !selectedOffer) {
      console.log("Création d'une offre avec invitation automatique pour le professionnel ID:", inviteProfessionalId);
      setSelectedOffer(null);
      setShowForm(true);

      // Activer l'option d'invitation automatique
      setFormData(prev => ({
        ...prev,
        autoInvite: true
      }));
    }
  }, [inviteProfessionalId, showForm, selectedOffer, searchParams]);

  // État du formulaire pour l'invitation automatique
  const [formData, setFormData] = useState({
    autoInvite: false
  });

  // Gérer la modification d'une offre existante
  const handleEditOffer = (offer: any) => {
    // Vérifier si l'utilisateur est le propriétaire de l'offre
    if (offer.user_id && offer.user_id !== user.id) {
      setFormError("Vous n'êtes pas autorisé à modifier cette offre. Seul le propriétaire peut la modifier.");
      return;
    }

    setSelectedOffer(offer);
    setShowForm(true);
  };

  // Gérer la suppression d'une offre
  const handleDeleteOffer = async (offerId: number) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer cette offre ?')) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${offerId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la suppression de l\'offre');
      }

      // Mettre à jour la liste des offres
      setOffers(offers.filter(offer => offer.id !== offerId));

      // Si l'offre supprimée était sélectionnée, désélectionner
      if (selectedOffer && selectedOffer.id === offerId) {
        setSelectedOffer(null);
        navigate('/dashboard/offers');
      }

      setFormSuccess('Offre supprimée avec succès');
    } catch (err) {
      console.error('Erreur:', err);
      setFormError('Impossible de supprimer l\'offre');
    }
  };

  // Gérer la soumission du formulaire (création ou modification)
  const handleFormSubmit = async (formData: OpenOfferFormData) => {
    setFormLoading(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Vérifier si l'utilisateur est le propriétaire de l'offre en mode édition
      if (isEditMode && selectedOffer.user_id && selectedOffer.user_id !== user.id) {
        setFormError("Vous n'êtes pas autorisé à modifier cette offre. Seul le propriétaire peut la modifier.");
        setFormLoading(false);
        return;
      }

      const method = isEditMode ? 'PUT' : 'POST';
      const endpoint = isEditMode
        ? `${API_BASE_URL}/api/open-offers/${selectedOffer.id}`
        : `${API_BASE_URL}/api/open-offers`;

      // Format deadline
      const formattedDeadline = new Date(formData.deadline).toISOString();

      // Prepare payload
      // Si nous avons un ID de professionnel à inviter, forcer auto_invite à true
      const payload = {
        title: formData.title,
        categories: formData.categories,
        budget: formData.budget,
        deadline: formattedDeadline,
        company: formData.company,
        website: formData.website,
        description: formData.description,
        recruitment_type: formData.recruitmentType,
        open_to_applications: formData.openToApplications,
        // Forcer auto_invite à true si nous avons un ID de professionnel à inviter
        auto_invite: inviteProfessionalId ? true : formData.autoInvite,
        status: formData.status || "open",
        filters: formData.filters,
      };

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("Erreur API détaillée:", errorData);
        console.error("Status code:", response.status);
        console.error("User ID:", user.id);
        console.error("Offer user_id:", selectedOffer?.user_id);

        if (response.status === 403) {
          throw new Error(`Vous n'êtes pas autorisé à ${isEditMode ? 'modifier' : 'créer'} cette offre. Seul le propriétaire peut la modifier.`);
        } else {
          throw new Error(`Erreur lors de ${isEditMode ? 'la modification' : 'la création'} de l'offre (${response.status})`);
        }
      }

      const data = await response.json();
      console.log("Réponse API complète:", data);

      // Mettre à jour la liste des offres
      if (isEditMode) {
        setOffers(offers.map(offer =>
          offer.id === selectedOffer.id ? { ...offer, ...payload, id: selectedOffer.id } : offer
        ));
        setFormSuccess('Offre mise à jour avec succès');
      } else {
        // Récupérer l'ID de l'offre créée
        const offerId = data.open_offer?.id || data.id;

        const newOffer = {
          ...payload,
          id: offerId || Math.random(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          views_count: 0,
          applications_count: 0,
          client: {
            id: user.id,
            name: user.name,
            avatar: user.avatar,
          },
        };
        setOffers([...offers, newOffer]);
        setFormSuccess('Offre créée avec succès');
      }

      // Fermer le formulaire et afficher les détails de l'offre
      setShowForm(false);
      if (!isEditMode) {
        // Vérifier si l'ID est dans data.open_offer.id ou data.id
        const offerId = data.open_offer?.id || data.id;
        console.log("ID de l'offre créée:", offerId);

        if (offerId) {
          // Si un ID de professionnel à inviter est présent, envoyer une invitation automatique
          if (inviteProfessionalId) {
            try {
              console.log("Envoi d'une invitation automatique au professionnel ID:", inviteProfessionalId);
              const inviteResponse = await fetch(`${API_BASE_URL}/api/open-offers/${offerId}/invite`, {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ professional_id: inviteProfessionalId }),
              });

              if (inviteResponse.ok) {
                console.log("Invitation envoyée avec succès");
                setFormSuccess('Offre créée et invitation envoyée avec succès');
              } else {
                console.error("Erreur lors de l'envoi de l'invitation");
              }
            } catch (inviteErr) {
              console.error("Erreur lors de l'invitation:", inviteErr);
            }
          }

          navigate(`/dashboard/offers/${offerId}`);
        } else {
          console.error("Impossible de récupérer l'ID de l'offre créée:", data);
          // Rediriger vers la liste des offres
          navigate('/dashboard/offers');
        }
      }
    } catch (err) {
      console.error('Erreur:', err);
      setFormError(`Impossible de ${isEditMode ? 'modifier' : 'créer'} l'offre`);
    } finally {
      setFormLoading(false);
    }
  };

  // Gérer l'annulation du formulaire
  const handleFormCancel = () => {
    setShowForm(false);
    if (!isEditMode) {
      setSelectedOffer(null);
    }
  };

  // Gérer le clic sur une offre
  const handleOfferClick = (offerId: number) => {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const isClient = user.role === 'client' || !user.is_professional;

    if (isClient) {
      navigate(`/dashboard/client/offers/${offerId}`);
    } else {
      // Stay on the same page but show the selected offer details
      navigate(`/dashboard/open-offers/${offerId}`);
    }
  };

  // Gérer la candidature à une offre (pour les professionnels)
  const handleApplyToOffer = (offerId: number) => {
    navigate(`/dashboard/open-offers/${offerId}/apply`);
  };

  // Gérer le contact avec un client (pour les professionnels)
  const handleContactClient = (clientId: number) => {
    navigate(`/discussions/client/${clientId}`);
  };

  // Gérer le partage d'une offre
  const handleShareOffer = (offerId: number) => {
    const url = `${window.location.origin}/offre/${offerId}`;
    navigator.clipboard.writeText(url);
    alert('Lien copié dans le presse-papier');
  };

  return (
    <DashboardLayout
      title={isProfessional ? "Offres disponibles" : "Mes offres ouvertes"}
      subtitle={isProfessional
        ? "Explorez les offres disponibles et trouvez votre prochaine opportunité"
        : "Gérez vos offres ouvertes et trouvez des professionnels qualifiés"
      }
      actions={
        !isProfessional && !showForm && !selectedOffer ? (
          <Button
            variant="primary"
            leftIcon={<Plus className="h-5 w-5" />}
            onClick={handleCreateOffer}
            style={{
              backgroundColor: '#2980b9',
              color: 'white',
              padding: '0.75rem 1.5rem',
              fontWeight: 'bold',
              borderRadius: '0.5rem',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
            }}
          >
            Créer une offre
          </Button>
        ) : null
      }
    >
      {/* Afficher les erreurs */}
      {(error || formError) && (
        <Alert
          type="error"
          title="Erreur"
          onClose={() => {
            setError(null);
            setFormError(null);
          }}
          className="mb-6"
        >
          {error || formError}
        </Alert>
      )}

      {/* Afficher les succès */}
      {formSuccess && (
        <Alert
          type="success"
          title="Succès"
          onClose={() => setFormSuccess(null)}
          className="mb-6"
        >
          {formSuccess}
        </Alert>
      )}

      {/* Afficher le formulaire, les détails ou la liste */}
      {showForm ? (
        <OpenOfferForm
          initialData={isEditMode ? selectedOffer : undefined}
          onSubmit={handleFormSubmit}
          onCancel={handleFormCancel}
          isLoading={formLoading}
        />
      ) : selectedOffer ? (
        <OpenOfferDetails
          {...selectedOffer}
          onEdit={!isProfessional ? handleEditOffer : undefined}
          onDelete={!isProfessional ? handleDeleteOffer : undefined}
          onApply={isProfessional ? handleApplyToOffer : undefined}
          onContact={isProfessional ? handleContactClient : undefined}
          onShare={handleShareOffer}
          isOwner={!isProfessional}
          isProfessional={isProfessional}
        />
      ) : (
        <OpenOffersList
          offers={offers}
          isLoading={isLoading}
          error={error || undefined}
          onOfferClick={handleOfferClick}
          onCreateOffer={!isProfessional ? handleCreateOffer : undefined}
          isProfessional={isProfessional}
          emptyMessage={isProfessional
            ? 'Aucune offre disponible pour le moment'
            : 'Vous n\'avez pas encore créé d\'offre'
          }
        />
      )}
    </DashboardLayout>
  );
};

export default OpenOffersManagementPage;
