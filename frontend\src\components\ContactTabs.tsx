import React, { useState } from "react";
import Tabs from "./Tabs"; // Importez le composant Tabs
import DiscussionPage from "./DiscussionPage";
import EquipeMembers from "./EquipeMembers";

const ContactTabs: React.FC = () => {
  const [activeTab, setActiveTab] = useState("discussion");

  const tabs = [
    { id: "discussion", label: "Discussion", component: <DiscussionPage /> },
    { id: "membre", label: "Membres de l'equipe", component: <EquipeMembers /> }, 
  ];

  return (
    <div className="min-h-screen bg-white px-4 sm:px-6 lg:px-8">
      {/* Barre d'onglets */}
      <Tabs
        tabs={tabs.map((tab) => ({ id: tab.id, label: tab.label }))} // Passer la liste des onglets
        activeTab={activeTab}
        setActiveTab={setActiveTab}
      />

      {/* Contenu de l'onglet actif */}
      <div className="max-w-7xl mx-auto py-12">
        {tabs.find((tab) => tab.id === activeTab)?.component}
      </div>
    </div>
  );
};

export default ContactTabs;