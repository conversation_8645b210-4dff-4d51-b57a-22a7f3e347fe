import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../Header';
import Footer from '../Footer';
import Section from '../layout/Section';
import Container from '../layout/Container';
import Grid from '../layout/Grid';
import Button from '../ui/Button';
import { Search, Filter, Star, MapPin } from 'lucide-react';
import { exploreService, Professional, Project, Category, FilterOptions } from '../../services/exploreService';
import { API_BASE_URL } from '../../config';

// Interfaces définies dans exploreService.ts
interface ProfessionalProfile {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  avatar: string;
  portfolio_items: any | null;
  phone: string;
  address: string;
  city: string;
  country: string;
  bio: string;
  title: string | null;
  expertise: string | null;
  completion_percentage: number;
  profession: string;
  years_of_experience: number;
  hourly_rate: string;
  description: string | null;
  availability_status: string;
  estimated_response_time: string | null;
  rating: string;
  skills: string[];
  languages: string[];
  services_offered: string[];
  portfolio: Array<{
    id: string;
    path: string;
    name: string;
    type: string;
    created_at: string;
  }>;
  social_links: any[];
  created_at: string;
  updated_at: string;
}

interface AchievementFile {
  path: string;
  original_name: string;
  mime_type: string;
  size: number;
}

interface Achievement {
  id: number;
  title: string;
  description: string;
  file_path: string;
  files: AchievementFile[];
  organization: string;
  achievement_url: string;
  date_obtained: string; // Nouvelle rubrique
  created_at: string;
  updated_at: string;
  professional_profile_id: number;
  professional_profile: ProfessionalProfile; // Nouvelle rubrique ajoutée
}

interface AchievementResponse {
  success: boolean;
  achievements: Achievement[];
}


const ExplorePage: React.FC = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [professionals, setProfessionals] = useState<Professional[]>([]);
  const [filteredProfessionals, setFilteredProfessionals] = useState<Professional[]>([]);
  const [featuredProjects, setFeaturedProjects] = useState<Project[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [filteredService, setFiltereService] = useState<Project[]>([]);
  const [filterType, setFiltersType] = useState<string | null>('pro');

  // Filtres pour les professionnels
  const [availabilityFilter, setAvailabilityFilter] = useState<string>('all');
  const [skillsFilter, setSkillsFilter] = useState<string[]>([]);
  const [ratingFilter, setRatingFilter] = useState<number | null>(null);
  const [locationFilter, setLocationFilter] = useState<string>('');

  // Filtres pour les projets/réalisations
  const [projectCategoryFilter, setProjectCategoryFilter] = useState<string>('all');

  // Filtres pour les services
  const [priceRangeFilter, setPriceRangeFilter] = useState<[number, number]>([0, 5000]);
  const [executionTimeFilter, setExecutionTimeFilter] = useState<string>('all');

  // Tri des résultats
  const [sortBy, setSortBy] = useState<string>('newest');

  // État pour les catégories
  const [categories, setCategories] = useState<Category[]>([]);

  // Charger les catégories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const categoriesResponse = await exploreService.getCategories();
        console.log('Catégories récupérées:', categoriesResponse);

        // Adapter les données au format attendu par le composant
        const adaptedCategories = categoriesResponse.categories.map(cat => ({
          ...cat
          // Utiliser directement image_url au lieu de créer imageUrl
        }));

        setCategories(adaptedCategories);
      } catch (error) {
        console.error('Erreur lors du chargement des catégories:', error);
        // En cas d'erreur, utiliser des catégories de secours
        const backupCategories: Category[] = [
          {
            id: 1,
            name: 'Modélisation 3D',
            slug: 'modelisation-3d',
            description: 'Création de modèles 3D détaillés et optimisés',
            image_url: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            count: 120,
          },
          {
            id: 2,
            name: 'Animation',
            slug: 'animation',
            description: 'Animation de personnages et d\'objets',
            image_url: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            count: 85,
          },
          {
            id: 3,
            name: 'Rendu',
            slug: 'rendu',
            description: 'Création d\'images photoréalistes',
            image_url: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            count: 95,
          },
          {
            id: 4,
            name: 'Texturing',
            slug: 'texturing',
            description: 'Application de textures détaillées',
            image_url: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            count: 70,
          },
          {
            id: 5,
            name: 'Conception de personnages',
            slug: 'conception-personnages',
            description: 'Création de personnages uniques',
            image_url: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            count: 65,
          },
          {
            id: 6,
            name: 'Environnements 3D',
            slug: 'environnements-3d',
            description: 'Conception d\'environnements immersifs',
            image_url: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            count: 55,
          },
          {
            id: 7,
            name: 'Effets spéciaux',
            slug: 'effets-speciaux',
            description: 'Création d\'effets visuels impressionnants',
            image_url: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            count: 40,
          },
          {
            id: 8,
            name: 'Réalité virtuelle',
            slug: 'realite-virtuelle',
            description: 'Création d\'expériences VR immersives',
            image_url: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            count: 30,
          },
        ];
        setCategories(backupCategories);
      }
    };

    fetchCategories();
  }, []);

  
  useEffect(() => {
  const fetchProfessionals = async () => {
    const response = await exploreService.getProfessionals();
    return response.professionals.map(pro => ({
      id: pro.id,
      id_user: pro.user_id,
      first_name: pro.first_name,
      last_name: pro.last_name,
      title: pro.title || 'Artiste 3D',
      skills: pro.skills || ['Modélisation', 'Blender', 'Maya'],
      rating: pro.rating || 4.5,
      review_count: pro.review_count || 10,
      profile_picture_path: pro.profile_picture_path || 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      city: pro.city || 'Paris',
      country: pro.country || 'France',
      availability_status: (pro.availability_status || 'available') as 'available' | 'busy' | 'unavailable',
      service_offer: (pro.service_offer || []).map((proj: any) => ({
        avatar: pro.profile_picture_path? getUrlProlfil(String(pro.profile_picture_path)) :'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        id: proj.id,
        title: proj.title,
        description: proj.description || 'Projet réalisé avec passion et expertise technique.',
        // image_url: proj.files?.[0]?.path
        //   ? `${API_BASE_URL}/storage/${proj.files[0].path}`
        //   : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        
        image_url : Array.isArray(proj.files) && proj.files.length > 0
        ? `${API_BASE_URL}/storage/${proj.files[0].path}`
        : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',     
        file_urls: Array.isArray(proj.files)
        ? proj.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
        : [],
        category: proj.categories ? proj.categories.join(" - ") : "",
        client_name: proj.execution_time,
        date_create: proj.created_at,
        price: proj.price,
        user_id: pro.user_id,
        professional_name: `${pro?.first_name || ''} ${pro?.last_name || ''}`.trim()
      })),
      achievements: (pro.achievements || []).map((ach: any) => ({
        avatar: pro.profile_picture_path? getUrlProlfil(String(pro.profile_picture_path)) :'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        id: ach.id,
        title: ach.title,
        description: ach.description || 'Réalisé avec expertise.',
        image_url : Array.isArray(ach.files) && ach.files.length > 0
        ? `${API_BASE_URL}/storage/${ach.files[0].path}`
        : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',

        // image_url: ach.file_path
        //   ? `${API_BASE_URL}/storage/${ach.file_path}`
        //   : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        
          file_urls: Array.isArray(ach.files)
        ? ach.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
        : [],
        category: ach.organization,
        client_name: ach.organization,
        date_create: ach.date_obtained,
        price: '',
        user_id: pro.user_id,
        professional_name: `${pro?.first_name || ''} ${pro?.last_name || ''}`.trim()
      })),
    }));
  };

  const fetchAchievements = async () => {
    const response = await fetch(`${API_BASE_URL}/api/explorer/achievements`);
    const data: AchievementResponse = await response.json();
    return data.achievements.map((proj) => ({
      id: proj.id,
      title: proj.title,
      description: proj.description || 'Projet réalisé avec passion et expertise technique.',
      image_url : Array.isArray(proj.files) && proj.files.length > 0
        ? `${API_BASE_URL}/storage/${proj.files[0].path}`
        : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',

      // image_url: proj.file_path
      //   ? `${API_BASE_URL}/storage/${proj.file_path}`
      //   : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      
        file_urls: Array.isArray(proj.files)
        ? proj.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
        : [],
      category: proj.organization || 'Projet',
      client_name: proj.organization || 'Organisation',
      professional_name: proj.professional_profile.first_name + ' ' + proj.professional_profile.last_name,
      professional_id: proj.professional_profile_id || 1,
      date_create: proj.date_obtained,
      user_id: proj.professional_profile.user_id,
      avatar: proj.professional_profile.avatar? getUrlProlfil(String(proj.professional_profile.avatar)) :'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    }));
  };

  const fetchServices = async () => {
    const response = await fetch(`${API_BASE_URL}/api/explorer/services`);
    const data = await response.json();
    return data.services.map((proj: any) => ({
      id: proj.id,
      title: proj.title,
      description: proj.description || 'Projet réalisé avec passion et expertise technique.',
      // image_url: proj.files?.[0]?.path
      //   ? `${API_BASE_URL}/storage/${proj.files[0].path}`
      //   : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      image_url : Array.isArray(proj.files) && proj.files.length > 0
        ? `${API_BASE_URL}/storage/${proj.files[0].path}`
        : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',     
      file_urls: Array.isArray(proj.files)
        ? proj.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
        : [],
      category: proj.categories ? proj.categories.join(" - ") : "",
      client_name: proj.execution_time,
      professional_name: proj.professional.first_name + ' ' + proj.professional.last_name,
      professional_id: proj.professional.id || 1,
      date_create: proj.created_at,
      price: proj.price,
      user_id: proj.professional.user_id,
      avatar: proj.professional.avatar? getUrlProlfil(String(proj.professional.avatar)) :'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    }));
  };

  const fetchData = async () => {
    setLoading(true);

    try {
      const professionals = await fetchProfessionals();

      if (professionals.length > 0) {
        setProfessionals(professionals);
        setFilteredProfessionals(professionals);
      }

      const [achievements, services] = await Promise.all([
        fetchAchievements(),
        fetchServices(),
      ]);

      if (achievements.length > 0) {
        setFeaturedProjects(achievements);
        setFilteredProjects(achievements);
      }

      setFiltereService(services);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      // Ici, tu peux insérer une logique de fallback si tu veux afficher des données fictives
    } finally {
      setLoading(false);
    }
  };

  fetchData();
}, []);


  // Fonction pour appliquer les filtres
  const applyFilters = async () => {
    // setLoading(true);

    try {
      // Construire les options de filtrage
      const filterOptions: FilterOptions = {
        search: searchQuery,
        category: selectedCategory || undefined,
        sort_by: sortBy
      };

      // Ajouter les filtres spécifiques selon le type de contenu
      if (filterType === 'pro') {
        filterOptions.availability = availabilityFilter;
        filterOptions.rating = ratingFilter;
        filterOptions.location = locationFilter;
        filterOptions.skills = skillsFilter;

        // Appeler l'API de filtrage des professionnels
        const response = await exploreService.filterProfessionals(filterOptions);

        const formatedData =  response.professionals.map(pro => ({
      id: pro.id,
      id_user: pro.user_id,
      first_name: pro.first_name,
      last_name: pro.last_name,
      title: pro.title || 'Artiste 3D',
      skills: pro.skills || ['Modélisation', 'Blender', 'Maya'],
      rating: pro.rating || 4.5,
      review_count: pro.review_count || 10,
      profile_picture_path: pro.profile_picture_path || 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      city: pro.city || 'Paris',
      country: pro.country || 'France',
      availability_status: (pro.availability_status || 'available') as 'available' | 'busy' | 'unavailable',
      service_offer: (pro.service_offer || []).map((proj: any) => ({
        avatar: pro.profile_picture_path || 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        id: proj.id,
        title: proj.title,
        description: proj.description || 'Projet réalisé avec passion et expertise technique.',
        image_url : Array.isArray(proj.files) && proj.files.length > 0
        ? `${API_BASE_URL}/storage/${proj.files[0].path}`
        : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',     
        file_urls: Array.isArray(proj.files)
        ? proj.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
        : [],
        category: proj.categories ? proj.categories.join(" - ") : "",
        client_name: proj.execution_time,
        date_create: proj.created_at,
        price: proj.price,
        user_id: pro.user_id,
        professional_name: `${pro?.first_name || ''} ${pro?.last_name || ''}`.trim()

      })),
      achievements: (pro.achievements || []).map((ach: any) => ({
        avatar: pro.profile_picture_path || 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        id: ach.id,
        title: ach.title,
        description: ach.description || 'Réalisé avec expertise.',
        image_url : Array.isArray(ach.files) && ach.files.length > 0
        ? `${API_BASE_URL}/storage/${ach.files[0].path}`
        : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',

        // image_url: ach.file_path
        //   ? `${API_BASE_URL}/storage/${ach.file_path}`
        //   : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        
          file_urls: Array.isArray(ach.files)
        ? ach.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
        : [],
        category: ach.organization,
        client_name: ach.organization,
        date_create: ach.date_obtained,
        price: '',
        user_id: pro.user_id,
        professional_name: `${pro?.first_name || ''} ${pro?.last_name || ''}`.trim()
      })),
    }));
        setFilteredProfessionals(formatedData || []);
      }
      else if (filterType === 'realization') {
        filterOptions.category = projectCategoryFilter !== 'all' ? projectCategoryFilter : selectedCategory || undefined;

        // Appeler l'API de filtrage des projets
        const response = await exploreService.filterProjects(filterOptions);
        setFilteredProjects(response.projects || []);
      }
      else if (filterType === 'service') {
        filterOptions.price_min = priceRangeFilter[0];
        filterOptions.price_max = priceRangeFilter[1];
        filterOptions.execution_time = executionTimeFilter;

        // Appeler l'API de filtrage des services
        const response = await exploreService.filterServices(filterOptions);
        setFilteredProjects(response.data || []);
      }
    } catch (error) {
      console.error('Erreur lors de l\'application des filtres:', error);

      // En cas d'erreur, filtrer localement
      if (filterType === 'pro' && professionals.length > 0) {
        let filtered = [...professionals];

        // Filtre par recherche
        if (searchQuery) {
          filtered = filtered.filter(pro =>
            `${pro.first_name} ${pro.last_name}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (pro.title && pro.title.toLowerCase().includes(searchQuery.toLowerCase())) ||
            (pro.skills && pro.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase())))
          );
        }

        // Filtre par disponibilité
        if (availabilityFilter !== 'all') {
          filtered = filtered.filter(pro => pro.availability_status === availabilityFilter);
        }

        // Filtre par note
        if (ratingFilter !== null) {
          filtered = filtered.filter(pro => (pro.rating || 0) >= ratingFilter);
        }

        // Filtre par catégorie
        if (selectedCategory) {
          filtered = filtered.filter(pro =>
            pro.skills && pro.skills.some(skill =>
              skill.toLowerCase().includes(selectedCategory.toLowerCase())
            )
          );
        }

        setFilteredProfessionals(filtered);
      }
      else if ((filterType === 'realization' || filterType === 'service') && featuredProjects.length > 0) {
        let filtered = [...featuredProjects];

        // Filtre par recherche
        if (searchQuery) {
          filtered = filtered.filter(project =>
            project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (project.description && project.description.toLowerCase().includes(searchQuery.toLowerCase()))
          );
        }

        // Filtre par catégorie
        if (projectCategoryFilter !== 'all' || selectedCategory) {
          const categoryFilter = projectCategoryFilter !== 'all' ? projectCategoryFilter : selectedCategory;
          if (categoryFilter) {
            filtered = filtered.filter(project =>
              project.category && project.category.toLowerCase().includes(categoryFilter.toLowerCase())
            );
          }
        }

        setFilteredProjects(filtered);
      }
    } finally {
      setLoading(false);
    }
  };

  // Effet pour appliquer les filtres lorsque les critères changent
  useEffect(() => {
    // Appliquer les filtres avec un délai pour éviter trop d'appels API
    const timer = setTimeout(() => {
      applyFilters();
    }, 500);

    return () => clearTimeout(timer);
  }, [
    filterType,
    searchQuery,
    availabilityFilter,
    skillsFilter,
    ratingFilter,
    locationFilter,
    selectedCategory,
    projectCategoryFilter,
    priceRangeFilter,
    executionTimeFilter,
    // filteredProfessionals
    // eslint-disable-next-line react-hooks/exhaustive-deps
    // Nous excluons applyFilters de la liste des dépendances pour éviter des appels infinis
  ]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Appliquer les filtres immédiatement
    applyFilters();
  };

  // Fonction pour afficher les étoiles de notation
  const renderRatingStars = (rating: number| string | null | undefined) => {
    const numericRating = Number(rating);
    const fullStars = Math.floor(numericRating);
    const hasHalfStar = numericRating % 1 >= 0.5;

    return (
      <div className="flex items-center">
        {Array.from({ length: 5 }).map((_, i) => (
          <Star
            key={i}
            className={`h-4 w-4 ${
              i < fullStars
                ? 'text-yellow-400 fill-yellow-400'
                : i === fullStars && hasHalfStar
                ? 'text-yellow-400 fill-yellow-400 half-star'
                : 'text-neutral-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-neutral-600">{numericRating.toFixed(1)}</span>
      </div>
    );
  };

  // Fonction pour afficher le badge de disponibilité
  const renderAvailabilityBadge = (availability: 'available' | 'busy' | 'unavailable' | undefined) => {
    if (!availability) {
      availability = 'unavailable';
    }

    const colors = {
      available: 'bg-green-100 text-green-800',
      busy: 'bg-yellow-100 text-yellow-800',
      unavailable: 'bg-neutral-100 text-neutral-800',
    };

    const labels = {
      available: 'Disponible',
      busy: 'Occupé',
      unavailable: 'Indisponible',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[availability]}`}>
        <span className={`w-2 h-2 rounded-full mr-1 ${availability === 'available' ? 'bg-green-500' : availability === 'busy' ? 'bg-yellow-500' : 'bg-neutral-500'}`}></span>
        {labels[availability]}
      </span>
    );
  };

  const getPlaceholder = () => {
    switch (filterType) {
      case 'pro':
        return 'Rechercher un professionnel, une compétence…';
      case 'realization':
        return 'Rechercher une réalisation…';
      case 'service':
        return 'Rechercher un service…';
      default:
        return 'Rechercher…';
    }
  };

  const getUrlProlfil = (path : string)  => {
      return path? `${API_BASE_URL}${path}`:'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="flex justify-center items-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
        <Footer />
      </div>
    );
  }

  console.log("Filtered professionals affichés:", filteredProfessionals);


  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <Header />

      {/* Hero Section with Search */}
      <div className="bg-gradient-to-r from-primary-50 to-primary-100 py-16 md:py-24">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-4">
              Explorer
            </h1>
            <p className="text-neutral-600 text-lg mb-12 max-w-2xl mx-auto">
              Découvrez les meilleurs artistes 3D et leurs projets exceptionnels dans différentes catégories.
            </p>

            {/* Search Form */}
            <form onSubmit={handleSearch} className="relative max-w-2xl mx-auto">
              <div className="flex gap-3 flex-col md:flex-row">
                <div className="flex-1 relative">
                  <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-neutral-400" />
                  </div>
                  <input
                    type="text"
                    placeholder={getPlaceholder()}
                    className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-full focus:ring-2 focus:ring-primary-500 focus:border-primary-500 shadow-sm"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                <div className="flex gap-2">
                  <button
                    type="button"
                    onClick={() => setShowFilters(!showFilters)}
                    className="px-4 py-3 bg-white border border-neutral-300 rounded-full flex items-center gap-2 hover:bg-neutral-50 shadow-sm text-neutral-800 font-semibold"
                  >
                    <Filter className="h-5 w-5 text-neutral-500" />
                    <span className="hidden md:inline">Filtres</span>
                  </button>

                  <Button
                    type="submit"
                    variant="primary"
                    size="lg"
                    className="px-6"
                  >
                    Rechercher
                  </Button>
                </div>
              </div>

              {/* Filters Panel (hidden by default) */}
              {showFilters && (
                <div className="mt-4 p-4 bg-white rounded-lg shadow-lg border border-neutral-200 text-left">
                  {/* Type de contenu */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => {
                        setFiltersType('pro');
                        // Réinitialiser les filtres spécifiques aux autres types
                        setProjectCategoryFilter('all');
                        setPriceRangeFilter([0, 5000]);
                        setExecutionTimeFilter('all');
                      }}
                      type="button"
                      className={filterType === 'pro' ? 'font-bold text-blue-600' : ''}
                    >
                      Professionnel
                    </Button>

                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => {
                        setFiltersType('realization');
                        // Réinitialiser les filtres spécifiques aux autres types
                        setAvailabilityFilter('all');
                        setRatingFilter(null);
                        setPriceRangeFilter([0, 5000]);
                        setExecutionTimeFilter('all');
                      }}
                      type="button"
                      className={filterType === 'realization' ? 'font-bold text-blue-600' : ''}
                    >
                      Réalisations
                    </Button>

                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => {
                        setFiltersType('service');
                        // Réinitialiser les filtres spécifiques aux autres types
                        setAvailabilityFilter('all');
                        setRatingFilter(null);
                        setProjectCategoryFilter('all');
                      }}
                      type="button"
                      className={filterType === 'service' ? 'font-bold text-blue-600' : ''}
                    >
                      Services
                    </Button>
                  </div>

                  {/* Filtres spécifiques selon le type */}
                  <div className="mt-4 border-t pt-4">
                    {/* Filtres pour les professionnels */}
                    {filterType === 'pro' && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Filtre par disponibilité */}
                        <div>
                          <label className="block text-sm font-medium text-neutral-700 mb-1">
                            Disponibilité
                          </label>
                          <select
                            className="w-full p-2 border border-neutral-300 rounded-md"
                            value={availabilityFilter}
                            onChange={(e) => setAvailabilityFilter(e.target.value)}
                            aria-label="Filtrer par disponibilité"
                          >
                            <option value="all">Toutes disponibilités</option>
                            <option value="available">Disponible</option>
                            <option value="busy">Occupé</option>
                            <option value="unavailable">Indisponible</option>
                          </select>
                        </div>

                        {/* Filtre par compétences */}
                        <div>
                          <label className="block text-sm font-medium text-neutral-700 mb-1">
                            Compétences
                          </label>
                          <div className="relative">
                            <input
                              type="text"
                              className="w-full p-2 border border-neutral-300 rounded-md"
                              value={skillsFilter.join(', ')}
                              onChange={(e) => {
                                const skills = e.target.value.split(',').map(skill => skill.trim()).filter(Boolean);
                                setSkillsFilter(skills);
                              }}
                              placeholder="Ex: Modélisation, Animation, Rendu"
                              aria-label="Filtrer par compétences"
                            />
                            <div className="text-xs text-neutral-500 mt-1">
                              Séparez les compétences par des virgules
                            </div>
                          </div>
                        </div>

                        {/* Filtre par note minimale */}
                        <div>
                          <label className="block text-sm font-medium text-neutral-700 mb-1">
                            Note minimale
                          </label>
                          <select
                            className="w-full p-2 border border-neutral-300 rounded-md"
                            value={ratingFilter === null ? 'all' : ratingFilter.toString()}
                            onChange={(e) => setRatingFilter(e.target.value === 'all' ? null : parseFloat(e.target.value))}
                            aria-label="Filtrer par note minimale"
                          >
                            <option value="all">Toutes notes</option>
                            <option value="4.5">4.5 et plus</option>
                            <option value="4">4 et plus</option>
                            <option value="3.5">3.5 et plus</option>
                            <option value="3">3 et plus</option>
                          </select>
                        </div>

                        {/* Filtre par localisation */}
                        <div>
                          <label className="block text-sm font-medium text-neutral-700 mb-1">
                            Localisation
                          </label>
                          <input
                            type="text"
                            className="w-full p-2 border border-neutral-300 rounded-md"
                            value={locationFilter}
                            onChange={(e) => setLocationFilter(e.target.value)}
                            placeholder="Ville ou pays"
                            aria-label="Filtrer par localisation"
                          />
                        </div>

                        {/* Filtre par catégorie */}
                        <div>
                          <label className="block text-sm font-medium text-neutral-700 mb-1">
                            Catégorie
                          </label>
                          <select
                            className="w-full p-2 border border-neutral-300 rounded-md"
                            value={selectedCategory || 'all'}
                            onChange={(e) => setSelectedCategory(e.target.value === 'all' ? null : e.target.value)}
                            aria-label="Filtrer par catégorie"
                          >
                            <option value="all">Toutes catégories</option>
                            {categories.map((category) => (
                              <option key={category.id} value={category.slug}>
                                {category.name}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                    )}

                    {/* Filtres pour les réalisations */}
                    {filterType === 'realization' && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Filtre par catégorie */}
                        <div>
                          <label className="block text-sm font-medium text-neutral-700 mb-1">
                            Catégorie
                          </label>
                          <select
                            className="w-full p-2 border border-neutral-300 rounded-md"
                            value={projectCategoryFilter}
                            onChange={(e) => setProjectCategoryFilter(e.target.value)}
                            aria-label="Filtrer les projets par catégorie"
                          >
                            <option value="all">Toutes catégories</option>
                            {categories.map((category) => (
                              <option key={category.id} value={category.slug}>
                                {category.name}
                              </option>
                            ))}
                          </select>
                        </div>

                        {/* Filtre par compétences */}
                        <div>
                          <label className="block text-sm font-medium text-neutral-700 mb-1">
                            Compétences requises
                          </label>
                          <div className="relative">
                            <input
                              type="text"
                              className="w-full p-2 border border-neutral-300 rounded-md"
                              value={skillsFilter.join(', ')}
                              onChange={(e) => {
                                const skills = e.target.value.split(',').map(skill => skill.trim()).filter(Boolean);
                                setSkillsFilter(skills);
                              }}
                              placeholder="Ex: Modélisation, Animation, Rendu"
                              aria-label="Filtrer par compétences requises"
                            />
                            <div className="text-xs text-neutral-500 mt-1">
                              Séparez les compétences par des virgules
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Filtres pour les services */}
                    {filterType === 'service' && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Filtre par fourchette de prix */}
                        <div>
                          <label className="block text-sm font-medium text-neutral-700 mb-1">
                            Fourchette de prix
                          </label>
                          <select
                            className="w-full p-2 border border-neutral-300 rounded-md"
                            value={`${priceRangeFilter[0]}-${priceRangeFilter[1]}`}
                            onChange={(e) => {
                              const [min, max] = e.target.value.split('-').map(Number);
                              setPriceRangeFilter([min, max]);
                            }}
                            aria-label="Filtrer par fourchette de prix"
                          >
                            <option value="0-5000">Tous les prix</option>
                            <option value="0-100">Moins de 100€</option>
                            <option value="100-500">100€ - 500€</option>
                            <option value="500-1000">500€ - 1000€</option>
                            <option value="1000-5000">Plus de 1000€</option>
                          </select>
                        </div>

                        {/* Filtre par temps d'exécution */}
                        <div>
                          <label className="block text-sm font-medium text-neutral-700 mb-1">
                            Temps d'exécution
                          </label>
                          <select
                            className="w-full p-2 border border-neutral-300 rounded-md"
                            value={executionTimeFilter}
                            onChange={(e) => setExecutionTimeFilter(e.target.value)}
                            aria-label="Filtrer par temps d'exécution"
                          >
                            <option value="all">Tous les délais</option>
                            <option value="express">Express (moins de 3 jours)</option>
                            <option value="standard">Standard (1-2 semaines)</option>
                            <option value="extended">Étendu (plus de 2 semaines)</option>
                          </select>
                        </div>

                        {/* Filtre par catégorie */}
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-neutral-700 mb-1">
                            Catégorie
                          </label>
                          <select
                            className="w-full p-2 border border-neutral-300 rounded-md"
                            value={selectedCategory || 'all'}
                            onChange={(e) => setSelectedCategory(e.target.value === 'all' ? null : e.target.value)}
                            aria-label="Filtrer les services par catégorie"
                          >
                            <option value="all">Toutes catégories</option>
                            {categories.map((category) => (
                              <option key={category.id} value={category.slug}>
                                {category.name}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Options de tri */}
                  <div className="mt-6 mb-4">
                    <label className="block text-sm font-medium text-neutral-700 mb-1">
                      Trier par
                    </label>
                    <select
                      className="w-full p-2 border border-neutral-300 rounded-md"
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      aria-label="Trier les résultats"
                    >
                      <option value="newest">Plus récents</option>
                      <option value="rating">Meilleures notes</option>
                      {filterType === 'service' && (
                        <>
                          <option value="price_asc">Prix croissant</option>
                          <option value="price_desc">Prix décroissant</option>
                        </>
                      )}
                    </select>
                  </div>

                  {/* Boutons d'action pour les filtres */}
                  <div className="mt-6 flex justify-end space-x-4">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => {
                        // Réinitialiser tous les filtres
                        setSearchQuery('');
                        setSelectedCategory(null);
                        setAvailabilityFilter('all');
                        setSkillsFilter([]);
                        setRatingFilter(null);
                        setLocationFilter('');
                        setProjectCategoryFilter('all');
                        setPriceRangeFilter([0, 5000]);
                        setExecutionTimeFilter('all');
                        setSortBy('newest');
                      }}
                      type="button"
                    >
                      Réinitialiser
                    </Button>

                    <Button
                      variant="primary"
                      size="sm"
                      onClick={applyFilters}
                      type="button"
                    >
                      Appliquer les filtres
                    </Button>
                  </div>
                </div>
              )}
            </form>
          </div>
        </Container>
      </div>

      {/* Indicateur de chargement */}
      {loading && (
        <Section background="light" spacing="xl">
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
          </div>
        </Section>
      )}

      {/* Featured Professionals Section lgCols={4}*/}
      {!loading && filterType === 'pro' && (
      <Section background="light" spacing="xl">
        <div className="flex justify-between items-center mb-12">
          <h2 className="text-3xl font-bold text-neutral-900">Professionnels</h2>
          
        </div>

        <Grid cols={1} mdCols={1} gap={6}>
          {filteredProfessionals.length > 0 ? (
            filteredProfessionals.map((professional) => (
              <div
                key={professional.id}
                // className="bg-white rounded-xl border border-neutral-200 p-4 shadow-sm hover:shadow-md transition cursor-pointer border border-neutral-200"
                 className="bg-white rounded-xl p-4 shadow-sm transition-all duration-300 transform hover:shadow-lg hover:-translate-y-1 hover:border-neutral-300 cursor-pointer"
                // onClick={() => navigate(`/professionals/${professional.id}`)}
              >
                {/* Top Info */}
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-3">
                    <img
                      src={getUrlProlfil(String(professional.profile_picture_path))}
                      alt="avatar"
                      className="w-12 h-12 rounded-full object-cover"
                       onError={(e) => {
                        e.currentTarget.onerror = null; // empêche les boucles infinies
                        e.currentTarget.src = 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; // chemin de l'image par défaut
                      }}
                    />
                    <div>
                      <div className="font-semibold text-neutral-900 flex items-center gap-2">
                        {professional.first_name} {professional.last_name} • 
                        <span className="text-xs bg-neutral-200 px-2 py-0.5 rounded uppercase font-bold"><span>{professional.title}</span></span>
                      </div>
                      <div className="text-sm text-neutral-600 flex items-center gap-2">
                        <span>{renderRatingStars(professional.rating || 0)}</span>
                        <span>• ({professional.review_count || 0})</span>
                      </div>
                      <div className="text-xs text-neutral-500 mt-1 flex gap-2 flex-wrap">
                        {/* <span>💰 From ${100}/project</span> */}
                        <span>📍 {professional.city || 'City'}, {professional.country || 'Country'}</span>
                        <span>{renderAvailabilityBadge(professional.availability_status)}</span>
                        {/* <span>🛠 {6} Services available</span> */}
                      </div>
                    </div>
                  </div>
                  <button 
                    className="cta-button-primary text-sm font-semibold px-4 py-1 bg-neutral-900 text-blue rounded hover:bg-neutral-800 transition"
                    onClick={() => navigate(`/professionals/${professional.id}`)}
                  >
                    Voir Profil
                  </button>
                </div>

                <div className="mt-4">
                {(professional.service_offer?.length ?? 0) > 0 || (professional.achievements?.length ?? 0) > 0 ? (
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-2">
                    {[...(professional.service_offer ?? []), ...(professional.achievements ?? [])]
                      .slice(0, 5)
                      .map((project, i) => (
                        <img
                          key={i}
                          src={project.image_url}
                          alt={project.title}
                          title={project.title}
                          className="rounded-md object-cover w-full aspect-square cursor-pointer"
                          onClick={() =>
                            navigate('/details-search', {
                              state: { project },
                            })
                          }
                        />
                      ))}
                  </div>
                ) : (
                  <div className="text-center text-gray-500 italic">
                    Aucun service ou projet réalisé n’est disponible pour ce professionnel.
                  </div>
                )}
              </div>


                {/* Images Gallery //professional.gallery_images || professional.min_price || professional.services_count || */}
                 {/* Affichage des services + réalisations */}
                  {/* <div className="mt-4">
                    {(professional.service_offer?.length??0) > 0 || (professional.achievements?.length??0 )> 0 ? (
                      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-2">
                        {[...(professional.service_offer ?? []), ...(professional.achievements ?? [])]
                          .slice(0, 5)
                          .map((project, i) => (
                            <img
                              key={i}
                              src={project.image_url}
                              alt={project.title}
                              title={project.title}
                              className="rounded-md object-cover w-full aspect-square cursor-pointer"
                              onClick={() => {
                                navigate('/details-search', {
                                  state: { project }
                                });
                              }}
                              // onClick={() => navigate(`/details-search`)}
                            />
                          ))}
                      </div>
                    ) : (
                      <div className="text-center text-gray-500 italic">
                        Aucun service ou projet réalisé n’est disponible pour ce professionnel.
                      </div>
                    )}

                  </div> */}

                {/* Tags */}
                <div className="mt-4 flex flex-wrap gap-2">
                  {(professional.skills || ['brand identity', 'logo', 'typography', 'branding', 'photoshop', '+5 skills']).map((tag, i) => (
                    <span key={i} className="bg-neutral-100 text-xs text-neutral-700 px-2 py-1 rounded">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
          ))) : (
            <div className="col-span-full text-center py-8">
              <p className="text-neutral-500">Aucun professionnel ne correspond à vos critères de recherche.</p>
            </div>
          )}
        </Grid>

        <div className="mt-8 text-center md:hidden">
          <Button
            variant="outline"
            onClick={() => navigate('/lists-independants')}
          >
            Voir tous les professionnels
          </Button>
        </div>
      </Section>
      )}

      {/* Featured Projects Section */}
      {!loading && filterType === 'realization' && (
      <Section background="white" spacing="xl">
        <div className="flex justify-between items-center mb-12">
          <h2 className="text-3xl font-bold text-neutral-900">Réalisation</h2>
          {/* <Button
            variant="outline"
            onClick={() => navigate('/projects')}
            className="hidden md:flex items-center"
          >
            Voir tous <ChevronRight className="ml-1 h-4 w-4" />
          </Button> */}
        </div>

        <Grid cols={1} mdCols={2} lgCols={3} gap={8}>
          {filteredProjects.length > 0 ? (
            filteredProjects.map((project) => (
              <div
                key={project.id}
                className="bg-white rounded-lg overflow-hidden shadow-sm border border-neutral-200 transition-shadow hover:shadow-md cursor-pointer"
                // onClick={() => navigate(`/details-search?id=${project.id}&type=realisation`)}
                onClick={() => {
                  navigate('/details-search', {
                    state: { project }
                  });
                }}
              >
              <div className="aspect-video overflow-hidden">
                <img
                  src={project.image_url || "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"}
                  alt={project.title}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.onerror = null; // empêche les boucles infinies
                    e.currentTarget.src = 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; // chemin de l'image par défaut
                  }}
                />
              </div>
              <div className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold">{project.title}</h3>
                    <p className="text-neutral-600 text-sm">{project.category}</p>
                  </div>
                </div>

                <p className="mt-2 text-sm text-neutral-600 line-clamp-2">{project.description}</p>

                <div className="mt-3 flex items-center justify-between">
                  <div className="text-sm">
                    <span className="text-neutral-500">Par </span>
                    <span
                      className="font-medium text-primary-900 hover:underline"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/profile/${project.professional_id}`);
                      }}
                    >
                      {project.professional_name}
                    </span>
                  </div>
                  <span className="text-sm text-neutral-500">{project.client_name}</span>
                </div>
              </div>
            </div>
          ))) : (
            <div className="col-span-full text-center py-8">
              <p className="text-neutral-500">Aucune réalisation ne correspond à vos critères de recherche.</p>
            </div>
          )}
        </Grid>

        <div className="mt-8 text-center md:hidden">
          <Button
            variant="outline"
            onClick={() => navigate('/projects')}
          >
            Voir tous les réalisations
          </Button>
        </div>
      </Section>
      )}

    {!loading && filterType === 'service' && (
      <Section background="white" spacing="xl">
        <div className="flex justify-between items-center mb-12">
          <h2 className="text-3xl font-bold text-neutral-900">Services</h2>
          {/* <Button
            variant="outline"
            onClick={() => navigate('/projects')}
            className="hidden md:flex items-center"
          >
            Voir tous <ChevronRight className="ml-1 h-4 w-4" />
          </Button> */}
        </div>

        <Grid cols={1} mdCols={2} lgCols={3} gap={8}>
          {filteredService.length > 0 ? (
            filteredService.map((project) => (
              <div
                key={project.id}
                className="bg-white rounded-lg overflow-hidden shadow-sm border border-neutral-200 transition-shadow hover:shadow-md cursor-pointer"
                // onClick={() => navigate(`/details-search`
                // )}
                onClick={() => {
                  navigate('/details-search', {
                    state: { project }
                  });
                }}
              >
              <div className="aspect-video overflow-hidden">
                <img
                  src={project.image_url|| "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"}
                  alt={project.title}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.onerror = null; // empêche les boucles infinies
                    e.currentTarget.src = 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; // chemin de l'image par défaut
                  }}
                />
              </div>
              <div className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold">{project.title}</h3>
                    <p className="text-neutral-600 text-sm">{project.category}</p>
                  </div>
                </div>

                <p className="mt-2 text-sm text-neutral-600 line-clamp-2">{project.description}</p>

                <div className="mt-3 flex items-center justify-between">
                  <div className="text-sm">
                    <span className="text-neutral-500">Par </span>
                    <span
                      className="font-medium text-primary-600 hover:underline"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/profile/${project.professional_id}`);
                      }}
                    >
                      {project.professional_name}
                    </span>
                  </div>
                  <span className="text-sm text-neutral-500">€{project.price}</span>
                </div>
              </div>
            </div>
          ))) : (
            <div className="col-span-full text-center py-8">
              <p className="text-neutral-500">Aucun service ne correspond à vos critères de recherche.</p>
            </div>
          )}
        </Grid>

        <div className="mt-8 text-center md:hidden">
          <Button
            variant="outline"
            onClick={() => navigate('/projects')}
          >
            Voir tous les services
          </Button>
        </div>
      </Section>
      )}

      {/* CTA Section */}
      {/* <Section background="primary" spacing="xl">
        <div className="text-center max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold text-white mb-6">Prêt à collaborer ?</h2>
          <p className="text-white text-opacity-90 mb-8">
            Rejoignez notre communauté d'artistes 3D et d'entreprises dès aujourd'hui et découvrez tout ce que Hi 3D Artist peut vous offrir.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button
              variant="primary"
              size="lg"
              onClick={() => navigate('/register')}
              className="bg-white text-primary-700 hover:bg-neutral-100 font-semibold"
            >
              Créer un compte
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() => navigate('/contact')}
              className="border-white text-white hover:bg-white hover:bg-opacity-10"
            >
              Nous contacter
            </Button>
          </div>
        </div>
      </Section> */}

        {/* <Section background="light" spacing="xl"> */}
           <div className="cta-section">
            <div
              className="cta-background"
              style={{
                backgroundImage: `url(https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)`
              }}
            >
              <div className="cta-overlay"></div>
            </div>
            <div className="cta-content">
              <div className="cta-container">
                <div className="cta-text-center">
                  <h2 className="cta-title">
                    Prêt à commencer ?
                  </h2>
                  <p className="cta-description">
                    Rejoignez notre communauté d'artistes 3D et d'entreprises dès aujourd'hui et découvrez tout ce que Hi 3D Artist peut vous offrir.
                  </p>
                  <div className="cta-buttons">
                    <button
                      type="button"
                      onClick={() => navigate('/contact')}
                      className="cta-button-primary"
                    >
                      Nous contacter
                    </button>
                    <button
                      type="button"
                      onClick={() => navigate('/register')}
                      className="cta-button-secondary"
                    >
                      Créer un compte
                    </button>
                  </div>
                </div>
              </div>
            </div>
           </div>

          {/* <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-6">Prêt à collaborer ?</h2>
            <p className="text-white text-opacity-90 mb-8">
              Rejoignez notre communauté d'artistes 3D et d'entreprises dès aujourd'hui et découvrez tout ce que Hi 3D Artist peut vous offrir.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button
                variant="primary"
                size="lg"
                onClick={() => navigate('/register')}
                className="bg-white text-primary-700 hover:bg-neutral-100 font-semibold"
              >
                Créer un compte
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => navigate('/contact')}
                className="border-white text-white hover:bg-white hover:bg-opacity-10"
              >
                Nous contacter
              </Button>
            </div>
          </div> */}
        {/* </Section> */}


      {/* Footer */}
      <Footer />
    </div>
  );
};

export default ExplorePage;
