import React, { ReactNode } from 'react';

interface FormTextareaProps {
  label: string;
  id: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  placeholder?: string;
  error?: string;
  rows?: number;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  icon?: ReactNode;
}

const FormTextarea: React.FC<FormTextareaProps> = ({
  label,
  id,
  name,
  value,
  onChange,
  placeholder = '',
  error,
  rows = 4,
  required = false,
  disabled = false,
  className = '',
  icon,
}) => {
  return (
    <div className={`space-y-1 ${className}`}>
      <label htmlFor={id} className="block text-sm font-medium text-neutral-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      
      <div className="relative">
        {icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            {icon}
          </div>
        )}
        
        <textarea
          id={id}
          name={name}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          rows={rows}
          required={required}
          disabled={disabled}
          className={`
            block w-full rounded-md shadow-sm 
            ${icon ? 'pl-10' : 'pl-3'} 
            pr-3 py-2 
            ${error ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-neutral-300 focus:ring-primary-500 focus:border-primary-500'} 
            ${disabled ? 'bg-neutral-100 text-neutral-500' : ''}
          `}
        />
      </div>
      
      {error && (
        <p className="text-red-600 text-sm mt-1">{error}</p>
      )}
    </div>
  );
};

export default FormTextarea;
