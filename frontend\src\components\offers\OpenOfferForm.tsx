import React, { useState, useEffect } from 'react';
import { Briefcase, DollarSign, Calendar, Globe, Building, Link, Filter, Languages, MapPin, Clock, Tag, ChevronDown, Search, X } from 'lucide-react';
import Button from '../ui/Button';
import FormInput from '../ui/FormInput';
import FormTextarea from '../ui/FormTextarea';
import FormSelect from '../ui/FormSelect';
import FormMultiSelect from '../ui/FormMultiSelect';
import Checkbox from '../ui/Checkbox';
import { Check } from 'lucide-react';
import { MAIN_CATEGORIES, getCategoryOptions } from '../../data/categories';
import { useToast } from '../../context/ToastContext';

export interface OpenOfferFormData {
  id?: number;
  title: string;
  categories: string[];
  budget: string;
  deadline: string;
  company: string;
  website: string;
  description: string;
  recruitmentType: 'company' | 'personal';
  openToApplications: boolean;
  autoInvite: boolean;
  status: 'pending' | 'open' | 'closed' | 'in_progress' | 'completed' | 'invited';
  files: File[];
  filters: {
    languages: string[];
    skills: string[];
    location: string;
    experience_years: number;
    availability_status: string;
  };
}

interface OpenOfferFormProps {
  initialData?: Partial<OpenOfferFormData>;
  onSubmit: (data: OpenOfferFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const OpenOfferForm: React.FC<OpenOfferFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const { showToast } = useToast();
  const isEditMode = !!initialData?.id;

  // Options pour les catégories principales
  const categoryOptions = getCategoryOptions(MAIN_CATEGORIES);

  // États pour la gestion des catégories et compétences
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [filteredSkills, setFilteredSkills] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [allSkills, setAllSkills] = useState<string[]>([]);

  // Liste des compétences par catégorie
  const skillsByCategory: Record<string, string[]> = {
    'modeling': ['Blender', 'Maya', '3ds Max', 'ZBrush', 'Substance Painter', 'Hard Surface Modeling', 'Organic Modeling'],
    'animation': ['Animation de personnages', 'Animation d\'objets', 'Motion Capture', 'Rigging', 'Facial Animation'],
    'architectural': ['SketchUp', 'Revit', 'ArchiCAD', 'Lumion', 'V-Ray', 'Rendu architectural', 'Modélisation BIM'],
    'product': ['Fusion 360', 'SolidWorks', 'Rhino 3D', 'KeyShot', 'Prototypage 3D', 'Design industriel'],
    'character': ['Character Design', 'Character Modeling', 'Character Rigging', 'Facial Rigging', 'Sculpting'],
    'environment': ['Environment Design', 'Landscape Modeling', 'Terrain Generation', 'World Building', 'Level Design'],
    'vr_ar': ['Unity', 'Unreal Engine', 'WebXR', 'A-Frame', 'ARKit', 'ARCore', 'Oculus SDK'],
    'game_art': ['Game Asset Creation', 'Low Poly Modeling', 'Texture Baking', 'UV Mapping', 'PBR Texturing'],
  };

  // Options pour les langues
  const languageOptions = [
    { value: 'french', label: 'Français' },
    { value: 'english', label: 'Anglais' },
    { value: 'spanish', label: 'Espagnol' },
    { value: 'german', label: 'Allemand' },
    { value: 'italian', label: 'Italien' },
    { value: 'portuguese', label: 'Portugais' },
    { value: 'arabic', label: 'Arabe' },
    { value: 'chinese', label: 'Chinois' },
  ];

  // Les compétences sont maintenant gérées par le dictionnaire skillsByCategory

  // Options pour la disponibilité
  const availabilityOptions = [
    { value: 'available', label: 'Disponible' },
    { value: 'limited', label: 'Disponibilité limitée' },
    { value: 'busy', label: 'Occupé' },
    { value: 'unavailable', label: 'Indisponible' },
  ];

  // Options pour le statut
  const statusOptions = [
    { value: 'pending', label: 'En attente' },
    { value: 'open', label: 'Ouvert' },
    { value: 'closed', label: 'Fermé' },
    { value: 'in_progress', label: 'En cours' },
    { value: 'completed', label: 'Terminé' },
    { value: 'invited', label: 'Invité' },
  ];

  // Form state
  const [formData, setFormData] = useState<OpenOfferFormData>({
    title: initialData?.title || '',
    categories: initialData?.categories || [],
    budget: initialData?.budget || '',
    deadline: initialData?.deadline || '',
    company: initialData?.company || '',
    website: initialData?.website || '',
    description: initialData?.description || '',
    recruitmentType: initialData?.recruitmentType || 'company',
    openToApplications: initialData?.openToApplications ?? true,
    autoInvite: initialData?.autoInvite ?? false,
    status: initialData?.status || 'pending',
    files: initialData?.files || [],
    filters: initialData?.filters || {
      languages: [],
      skills: [],
      location: '',
      experience_years: 0,
      availability_status: 'available',
    },
  });

  // Form validation errors
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Toggle advanced filters
  const [showFilters, setShowFilters] = useState(
    initialData?.filters && (
      initialData.filters.languages.length > 0 ||
      initialData.filters.skills.length > 0 ||
      initialData.filters.location ||
      initialData.filters.experience_years > 0
    )
  );

  // Initialiser la liste complète des compétences
  useEffect(() => {
    const skills: string[] = [];
    Object.values(skillsByCategory).forEach(categorySkills => {
      categorySkills.forEach(skill => {
        if (!skills.includes(skill)) {
          skills.push(skill);
        }
      });
    });
    setAllSkills(skills.sort());
    setFilteredSkills(skills.sort());
  }, []);

  // Filtrer les compétences en fonction des catégories sélectionnées
  useEffect(() => {
    if (formData.categories.length > 0) {
      // Fusionner toutes les compétences des catégories sélectionnées
      const mergedSkills = formData.categories
        .flatMap(cat => skillsByCategory[cat] || [])
        .filter((skill, index, arr) => arr.indexOf(skill) === index); // enlever les doublons
      setFilteredSkills(mergedSkills);
    } else {
      setFilteredSkills(allSkills);
    }
  }, [formData.categories, allSkills, skillsByCategory]);

  // Filtrer les compétences en fonction du terme de recherche
  useEffect(() => {
    if (searchTerm) {
      const baseSkills = selectedCategory ? skillsByCategory[selectedCategory] : allSkills;
      const filtered = baseSkills.filter(skill =>
        skill.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredSkills(filtered);
    } else {
      if (selectedCategory && skillsByCategory[selectedCategory]) {
        setFilteredSkills(skillsByCategory[selectedCategory]);
      } else {
        setFilteredSkills(allSkills);
      }
    }
  }, [searchTerm, selectedCategory, allSkills]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Normaliser l'URL si c'est le champ website
    if (name === 'website' && value) {
      let normalizedUrl = value.trim();
      if (!normalizedUrl.startsWith('http://') && !normalizedUrl.startsWith('https://')) {
        normalizedUrl = 'https://' + normalizedUrl;
      }
      setFormData(prev => ({ ...prev, [name]: normalizedUrl }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Handle multi-select changes
  const handleMultiSelectChange = (name: string, values: string[]): void => {
    setFormData(prev => ({ ...prev, [name]: values }));
  };

  // Handle filter changes
  const handleFilterChange = (name: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        [name]: value,
      },
    }));
  };

  // Gestion de la recherche
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Ajouter une compétence depuis la liste
  const handleAddSkillFromList = (skill: string) => {
    if (!formData.filters.skills.includes(skill)) {
      handleFilterChange('skills', [...formData.filters.skills, skill]);
    }
  };

  // Form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('handleSubmit called'); // Debug log

    // Validate form
    const newErrors: Record<string, string> = {};
    let hasErrors = false;

    if (!formData.title.trim()) {
      newErrors.title = 'Le titre est requis';
      showToast('warning', 'Le champ Titre de l\'offre est requis');
      console.log('Le champ Titre de l\'offre est requis');
      hasErrors = true;
    }

    if (!formData.description.trim()) {
      newErrors.description = 'La description est requise';
      showToast('warning', 'Le champ Description est requis');
      console.log('Le champ Description est requis');
      hasErrors = true;
    }

    if (!formData.budget.trim()) {
      newErrors.budget = 'Le budget est requis';
      showToast('warning', 'Le champ Budget est requis');
      console.log('Le champ Budget est requis');
      hasErrors = true;
    }

    if (!formData.deadline) {
      newErrors.deadline = 'La date limite est requise';
      showToast('warning', 'Le champ Date limite est requis');
      hasErrors = true;
    } else {
      const deadlineDate = new Date(formData.deadline);
      const today = new Date();

      if (deadlineDate < today) {
        newErrors.deadline = 'La date limite doit être dans le futur';
        showToast('warning', 'La date limite doit être dans le futur');
        console.log('La date limite doit être dans le futur');
        hasErrors = true;
      }
    }

    if (!formData.company.trim()) {
      newErrors.company = 'L\'entreprise est requise';
      showToast('warning', 'Le champ Entreprise est requis');
      console.log('Le champ Entreprise est requis');
      hasErrors = true;
    }

    if (hasErrors) {
      setErrors(newErrors);
      return;
    }

    // Submit form only if there are no errors
    onSubmit(formData);
  };

  useEffect(() => {
    if (initialData) {
      setFormData({
        title: initialData.title || '',
        categories: initialData.categories || [],
        budget: initialData.budget || '',
        deadline: initialData.deadline || '',
        company: initialData.company || '',
        website: initialData.website || '',
        description: initialData.description || '',
        recruitmentType: initialData.recruitmentType || 'company',
        openToApplications: initialData.openToApplications ?? true,
        autoInvite: initialData.autoInvite ?? false,
        status: initialData.status || 'open',
        files: initialData.files || [],
        filters: initialData.filters || {
          languages: [],
          skills: [],
          location: '',
          experience_years: 0,
          availability_status: 'available',
        },
      });
    }
  }, [initialData]);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-neutral-200 overflow-hidden">
      <div className="px-6 py-4 border-b border-neutral-200">
        <h2 className="text-xl font-semibold text-neutral-900">
          {isEditMode ? 'Modifier l\'offre' : 'Créer une nouvelle offre'}
        </h2>
        <p className="text-neutral-600 text-sm mt-1">
          {isEditMode
            ? 'Mettez à jour les détails de votre offre'
            : 'Remplissez les détails de votre offre pour trouver les meilleurs professionnels'}
        </p>
      </div>

      <form 
        onSubmit={(e) => {
          console.log('Form submitted');
          handleSubmit(e);
        }} 
        className="p-6"
      >
        <div className="space-y-6">
          {/* Offer Title */}
          <FormInput
            label="Titre de l'offre *"
            id="title"
            name="title"
            placeholder="Ex: Création d'un personnage 3D pour jeu vidéo"
            value={formData.title}
            onChange={handleInputChange}
            error={errors.title}
            icon={<Briefcase className="h-5 w-5 text-neutral-400" />}
          />

          {/* Pas de sélecteur de catégories ici - déplacé dans les filtres avancés */}

          {/* Budget */}
          <FormInput
            label="Budget *"
            id="budget"
            name="budget"
            placeholder="Ex: 500€ - 1000€"
            value={formData.budget}
            onChange={handleInputChange}
            error={errors.budget}
            icon={<DollarSign className="h-5 w-5 text-neutral-400" />}
          />

          {/* Deadline */}
          <FormInput
            label="Date limite *"
            id="deadline"
            name="deadline"
            type="date"
            value={formData.deadline ? new Date(formData.deadline).toISOString().split('T')[0] : ''}
            onChange={handleInputChange}
            error={errors.deadline}
            icon={<Calendar className="h-5 w-5 text-neutral-400" />}
          />

          {/* Company */}
          <FormInput
            label="Entreprise *"
            id="company"
            name="company"
            placeholder="Nom de votre entreprise"
            value={formData.company}
            onChange={handleInputChange}
            error={errors.company}
            icon={<Building className="h-5 w-5 text-neutral-400" />}
          />

          {/* Website */}
          <FormInput
            label="Site web"
            id="website"
            name="website"
            placeholder="monsite.com ou https://monsite.com"
            value={formData.website}
            onChange={handleInputChange}
            icon={<Link className="h-5 w-5 text-neutral-400" />}
          />

          {/* Description */}
          <FormTextarea
            label="Description *"
            id="description"
            name="description"
            placeholder="Décrivez votre projet en détail..."
            value={formData.description}
            onChange={handleInputChange}
            error={errors.description}
            rows={6}
          />

          {/* Recruitment Type */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">
              Type de recrutement
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="recruitmentType"
                  value="company"
                  checked={formData.recruitmentType === 'company'}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded"
                />
                <span className="ml-2 text-neutral-700">Entreprise</span>
              </label>

              <label className="flex items-center">
                <input
                  type="radio"
                  name="recruitmentType"
                  value="personal"
                  checked={formData.recruitmentType === 'personal'}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded"
                />
                <span className="ml-2 text-neutral-700">Personnel</span>
              </label>
            </div>
          </div>

          {/* Advanced Filters */}
          <div>
            <button
              type="button"
              className="flex items-center text-sm font-medium text-primary-600 hover:text-primary-700"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-1" />
              {showFilters ? 'Masquer les filtres avancés' : 'Afficher les filtres avancés'}
            </button>

            {showFilters && (
              <div className="mt-4 p-4 bg-neutral-50 rounded-lg border border-neutral-200 space-y-4">
                <h3 className="text-sm font-medium text-neutral-700 mb-2">
                  Filtres pour trouver les professionnels adaptés
                </h3>

                {/* Categories */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Catégories de l'offre (utilisées pour le filtrage)
                  </label>
                  <div className="relative">
                    <button
                      type="button"
                      onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
                      className="w-full flex items-center justify-between px-4 py-2 border border-neutral-300 rounded-lg bg-white text-left focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    >
                      <span>{selectedCategory ? MAIN_CATEGORIES.find(cat => cat.value === selectedCategory)?.label || selectedCategory : "Sélectionner une catégorie"}</span>
                      <ChevronDown size={20} className={`transition-transform ${showCategoryDropdown ? 'rotate-180' : ''}`} />
                    </button>

                    {showCategoryDropdown && (
                      <div className="absolute z-10 mt-1 w-full bg-white border border-neutral-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                        <div
                          className="px-4 py-2 hover:bg-neutral-100 cursor-pointer"
                          onClick={() => {
                            setSelectedCategory("");
                            setShowCategoryDropdown(false);
                          }}
                        >
                          Toutes les compétences
                        </div>
                        {MAIN_CATEGORIES.map((category) => (
                          <div
                            key={category.id}
                            className={`px-4 py-2 hover:bg-neutral-100 cursor-pointer ${
                              formData.categories.includes(category.value) ? 'bg-primary-50 text-primary-700' : ''
                            }`}
                            onClick={() => {
                              setSelectedCategory(category.value);
                              setShowCategoryDropdown(false);

                              // Ajouter ou supprimer la catégorie de la liste des catégories
                              if (formData.categories.includes(category.value)) {
                                handleMultiSelectChange('categories', formData.categories.filter(cat => cat !== category.value));
                              } else {
                                handleMultiSelectChange('categories', [...formData.categories, category.value]);
                              }
                            }}
                          >
                            <div className="flex items-center">
                              <div className={`w-4 h-4 mr-2 rounded-sm flex items-center justify-center ${
                                formData.categories.includes(category.value) ? 'bg-primary-500' : 'border border-neutral-300'
                              }`}>
                                {formData.categories.includes(category.value) && (
                                  <Check className="h-3 w-3 text-white" />
                                )}
                              </div>
                              {category.label}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Afficher les catégories sélectionnées */}
                  {formData.categories.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-2">
                      {formData.categories.map((categoryValue, index) => {
                        const category = MAIN_CATEGORIES.find(cat => cat.value === categoryValue);
                        return (
                          <div
                            key={index}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-50 text-primary-700"
                          >
                            {category?.label || categoryValue}
                            <button
                              type="button"
                              onClick={() => handleMultiSelectChange('categories', formData.categories.filter(cat => cat !== categoryValue))}
                              className="ml-1.5 text-primary-500 hover:text-primary-700"
                              title={`Supprimer ${category?.label || categoryValue}`}
                              aria-label={`Supprimer ${category?.label || categoryValue}`}
                            >
                              <X className="h-3.5 w-3.5" />
                            </button>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>

                {/* Languages */}
                <FormMultiSelect
                  label="Langues"
                  id="languages"
                  options={languageOptions}
                  value={formData.filters.languages}
                  onChange={(values: string[]) => handleFilterChange('languages', values)}
                  placeholder="Sélectionnez les langues requises"
                  icon={<Languages className="h-5 w-5 text-neutral-400" />}
                />

                {/* Skills */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Compétences
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search size={18} className="text-neutral-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Rechercher une compétence..."
                      value={searchTerm}
                      onChange={handleSearchChange}
                      className="pl-10 w-full px-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>

                  {/* Liste des compétences suggérées */}
                  <div className="grid grid-cols-2 gap-2 mt-2 max-h-40 overflow-y-auto p-2 border border-neutral-200 rounded-md">
                    {filteredSkills.map((skill, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => handleAddSkillFromList(skill)}
                        disabled={formData.filters.skills.includes(skill)}
                        className={`text-left px-3 py-2 rounded-lg text-sm ${
                          formData.filters.skills.includes(skill)
                            ? 'bg-neutral-100 text-neutral-500 cursor-not-allowed'
                            : 'bg-primary-50 text-primary-700 hover:bg-primary-100'
                        }`}
                        title={formData.filters.skills.includes(skill) ? "Déjà ajouté" : "Ajouter cette compétence"}
                      >
                        {skill}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Compétences sélectionnées */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Compétences sélectionnées
                  </label>
                  <div className="p-4 border border-neutral-200 rounded-lg bg-neutral-50 min-h-[100px]">
                    {formData.filters.skills.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {formData.filters.skills.map((skill, index) => (
                          <div
                            key={index}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white border border-neutral-300 shadow-sm"
                          >
                            {skill}
                            <button
                              type="button"
                              onClick={() => handleFilterChange('skills', formData.filters.skills.filter(s => s !== skill))}
                              className="ml-1.5 text-neutral-500 hover:text-red-500"
                              title={`Supprimer ${skill}`}
                              aria-label={`Supprimer ${skill}`}
                            >
                              <X className="h-3.5 w-3.5" />
                            </button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-full text-neutral-500">
                        Aucune compétence ajoutée
                      </div>
                    )}
                  </div>
                </div>

                {/* Location */}
                <FormInput
                  label="Localisation"
                  id="location"
                  name="location"
                  placeholder="Ex: Paris, France"
                  value={formData.filters.location}
                  onChange={(e) => handleFilterChange('location', e.target.value)}
                  icon={<MapPin className="h-5 w-5 text-neutral-400" />}
                />

                {/* Experience Years */}
                <FormInput
                  label="Années d'expérience minimales"
                  id="experience_years"
                  name="experience_years"
                  type="number"
                  min="0"
                  max="30"
                  value={formData.filters.experience_years.toString()}
                  onChange={(e) => handleFilterChange('experience_years', parseInt(e.target.value) || 0)}
                  icon={<Briefcase className="h-5 w-5 text-neutral-400" />}
                />

                {/* Availability */}
                <FormSelect
                  label="Disponibilité"
                  id="availability_status"
                  name="availability_status"
                  options={availabilityOptions}
                  value={formData.filters.availability_status}
                  onChange={(e) => handleFilterChange('availability_status', e.target.value)}
                  icon={<Clock className="h-5 w-5 text-neutral-400" />}
                />
              </div>
            )}
          </div>

          {/* Offer Options */}
          <div className="space-y-3">
            <Checkbox
              id="openToApplications"
              name="openToApplications"
              label="Ouvert aux candidatures (les professionnels peuvent postuler à votre offre)"
              checked={formData.openToApplications}
              onChange={handleCheckboxChange}
            />

            <Checkbox
              id="autoInvite"
              name="autoInvite"
              label="Inviter automatiquement les professionnels correspondant à vos critères"
              checked={formData.autoInvite}
              onChange={handleCheckboxChange}
            />

            {/* Afficher un message si l'offre est créée depuis la page d'un professionnel */}
            {new URLSearchParams(window.location.search).get('invite') && (
              <div className="mt-2 p-4 bg-blue-50 text-blue-700 rounded-md border border-blue-200 flex items-start">
                <div className="mr-3 flex-shrink-0 mt-0.5">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm font-medium">
                    <strong>Invitation automatique activée</strong>
                  </p>
                  <p className="text-sm mt-1">
                    Le professionnel consulté sera automatiquement invité à cette offre après sa création.
                  </p>
                </div>
              </div>
            )}

            {/* Status */}
            {/* <FormSelect
              label="Statut"
              id="status"
              name="status"
              options={statusOptions}
              value={formData.status}
              onChange={handleInputChange}
              icon={<Clock className="h-5 w-5 text-neutral-400" />}
            /> */}
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-neutral-200">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Annuler
            </Button>

            <Button
              type="submit"
              variant="primary"
              isLoading={isLoading}
              leftIcon={isEditMode ? <Check className="h-5 w-5" /> : undefined}
              style={{ backgroundColor: '#2980b9', color: 'black' }}
            >
              {isEditMode ? 'Mettre à jour l\'offre' : 'Créer l\'offre'}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default OpenOfferForm;
