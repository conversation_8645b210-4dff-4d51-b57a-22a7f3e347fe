<?php

namespace App\Models;

use App\Models\User;
use <PERSON><PERSON>\Scout\Searchable;
use App\Models\DashboardProject;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ServiceOffer extends Model
{
    use HasFactory, Searchable;

    protected $fillable = [
        'user_id',
        'title',
        'description',
        'price',
        'price_unit',
        'average_duration',
        'cover_image',
        'image_category',
        'associated_project_id',
        'execution_time',
        'concepts',
        'revisions',
        'is_private',
        'status',
        'categories',
        'files',
        'views',
        'likes',
        'rating',
        'image',
    ];

    protected $casts = [
        'categories' => 'array', // Cast 'categories' to array
        'files' => 'array',      // Cast 'files' to array
        'is_private' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the associated project.
     */
    public function associatedProject(): BelongsTo
    {
        return $this->belongsTo(DashboardProject::class, 'associated_project_id');
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        return [
            'title' => $this->title,
            'description' => $this->description,
            // Add other fields you want to be searchable
        ];
    }

    /**
     * Get the human-readable label for price unit.
     *
     * @return string
     */
    public function getPriceUnitLabel(): string
    {
        return match($this->price_unit) {
            'per_image' => 'par image',
            'per_sqm' => 'par m²',
            'per_project' => 'par projet',
            default => 'par projet'
        };
    }

    /**
     * Get the human-readable label for average duration.
     *
     * @return string
     */
    public function getAverageDurationLabel(): string
    {
        return match($this->average_duration) {
            'less_than_week' => 'moins d\'une semaine',
            '1_to_2_weeks' => '1 à 2 semaines',
            '1_month' => '1 mois',
            '2_months' => '2 mois',
            '3_months' => '3 mois',
            default => ''
        };
    }
}
