import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Button from './ui/Button';
import Container from './layout/Container';
import Section from './layout/Section';
import Grid from './layout/Grid';
import Layout from './layout/Layout';
import Animation from './ui/Animation';
import { ArrowRight, CheckCircle, Users, Briefcase, Star, ChevronRight, LayoutDashboard } from 'lucide-react';
import './HomePage.css';

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isProfessional, setIsProfessional] = useState(false);

  useEffect(() => {
    // Vérifier si l'utilisateur est connecté
    const token = localStorage.getItem('token');
    const user = JSON.parse(localStorage.getItem('user') || '{}');

    if (token && user && user.id) {
      setIsAuthenticated(true);
      setIsProfessional(user.is_professional === true);
    }
  }, []);

  // Données pour les témoignages
  const testimonials = [
    {
      id: 1,
      name: 'Sophie Martin',
      role: 'Directrice Artistique',
      company: 'Studio Créatif',
      content: 'Hi 3D Artist m\'a permis de trouver rapidement des talents exceptionnels pour nos projets d\'animation. La qualité des professionnels sur la plateforme est impressionnante.',
      avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
    {
      id: 2,
      name: 'Thomas Dubois',
      role: 'Artiste 3D Indépendant',
      company: '',
      content: 'Depuis que j\'ai rejoint Hi 3D Artist, j\'ai pu développer mon réseau professionnel et trouver des projets passionnants qui correspondent parfaitement à mes compétences.',
      avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
    {
      id: 3,
      name: 'Camille Leroy',
      role: 'Responsable de Production',
      company: 'GameStudio',
      content: 'La plateforme nous a fait gagner un temps précieux dans notre processus de recrutement. Nous avons trouvé des artistes 3D talentueux qui ont apporté une réelle valeur ajoutée à nos projets.',
      avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
  ];

  // Données pour les fonctionnalités
  const features = [
    {
      title: 'Trouvez des talents exceptionnels',
      description: 'Accédez à une communauté d\'artistes 3D qualifiés et vérifiés, spécialisés dans différents domaines de la création 3D.',
      icon: <Users className="h-6 w-6 text-primary-600" />,
    },
    {
      title: 'Proposez vos services',
      description: 'Mettez en valeur vos compétences et trouvez des projets qui correspondent à votre expertise et à vos disponibilités.',
      icon: <Briefcase className="h-6 w-6 text-primary-600" />,
    },
    {
      title: 'Collaborez efficacement',
      description: 'Utilisez nos outils de communication et de gestion de projet pour une collaboration fluide et productive.',
      icon: <Star className="h-6 w-6 text-primary-600" />,
    },
  ];



  return (
    <Layout>

      {/* Hero Section */}
      <div className="relative overflow-hidden bg-white">
        <Animation type="fadeIn" duration={800}>
          <div className="absolute inset-y-0 right-0 hidden w-1/2 lg:block">
            <img
              className="absolute inset-0 h-full w-full object-cover"
              src="https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
              alt="3D Artist working"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-white to-transparent"></div>
          </div>
        </Animation>

        <Container className="relative py-20 lg:py-32">
          <div className="max-w-xl lg:max-w-lg">
            <h1 className="text-4xl font-bold tracking-tight text-neutral-900 sm:text-5xl">
              La plateforme des artistes 3D
            </h1>
            <p className="mt-6 text-lg leading-8 text-neutral-600">
              Connectez-vous avec des professionnels de la 3D, trouvez des projets passionnants et développez votre carrière dans l'univers de la création 3D.
            </p>
            <div className="mt-10 flex items-center gap-x-6">
              {isAuthenticated ? (
                <>
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={() => navigate('/dashboard')}
                    className="flex items-center"
                  >
                    <LayoutDashboard className="mr-2 h-5 w-5" /> Accéder au tableau de bord
                  </Button>
                  <Button
                    variant="ghost"
                    size="lg"
                    onClick={() => navigate(isProfessional ? '/lists-independants' : '/create-project-client')}
                    className="flex items-center"
                  >
                    {isProfessional ? 'Explorer les projets' : 'Créer un projet'} <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={() => navigate('/register')}
                    className="text-white font-semibold homepage-cta-button"
                  >
                    Commencer gratuitement
                  </Button>
                  <Button
                    variant="ghost"
                    size="lg"
                    onClick={() => navigate('/lists-independants')}
                    className="flex items-center"
                  >
                    Explorer les talents <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </>
              )}
            </div>
          </div>
        </Container>
      </div>

      {/* Stats Section */}
      <div className="bg-primary-50 py-12">
        <Container>
          <div className="grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-2 lg:grid-cols-3 xl:gap-x-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-primary-600">500+</div>
              <div className="mt-2 text-neutral-600">Artistes 3D qualifiés</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary-600">1,200+</div>
              <div className="mt-2 text-neutral-600">Projets réalisés</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary-600">98%</div>
              <div className="mt-2 text-neutral-600">Clients satisfaits</div>
            </div>
          </div>
        </Container>
      </div>

      {/* Features Section */}
      <Section background="white" spacing="xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-neutral-900 mb-4">Comment ça fonctionne</h2>
          <p className="text-neutral-600 max-w-2xl mx-auto">
            Hi 3D Artist simplifie la mise en relation entre les artistes 3D et les entreprises à la recherche de talents.
          </p>
        </div>

        <Grid cols={1} mdCols={3} gap={8}>
          {features.map((feature, index) => (
            <div key={index} className="p-6 rounded-xl border border-neutral-200 bg-white shadow-sm hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center mb-4">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
              <p className="text-neutral-600">{feature.description}</p>
            </div>
          ))}
        </Grid>
      </Section>

      {/* Categories Section */}
      <Section background="light" spacing="xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-neutral-900 mb-4">Explorez nos catégories</h2>
          <p className="text-neutral-600 max-w-2xl mx-auto">
            Découvrez des talents spécialisés dans différents domaines de la création 3D.
          </p>
        </div>

        <Grid cols={1} mdCols={2} lgCols={4} gap={6}>
          {[
            { name: 'Modélisation 3D', image: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D' },
            { name: 'Animation', image: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D' },
            { name: 'Rendu', image: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D' },
            { name: 'Texturing', image: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D' },
          ].map((category, index) => (
            <div
              key={index}
              className="relative overflow-hidden rounded-lg group cursor-pointer"
              onClick={() => navigate(`/lists-independants?category=${category.name}`)}
            >
              <div className="aspect-square">
                <img
                  src={category.image}
                  alt={category.name}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-neutral-900 to-transparent opacity-70"></div>
                <div className="absolute bottom-0 left-0 right-0 p-4 flex justify-between items-center">
                  <h3 className="text-white font-semibold">{category.name}</h3>
                  <ChevronRight className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>
          ))}
        </Grid>
      </Section>

      {/* Testimonials Section */}
      <Section background="white" spacing="xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-neutral-900 mb-4">Ce que disent nos utilisateurs</h2>
          <p className="text-neutral-600 max-w-2xl mx-auto">
            Découvrez les témoignages de nos utilisateurs satisfaits.
          </p>
        </div>

        <Grid cols={1} mdCols={3} gap={8}>
          {testimonials.map((testimonial) => (
            <div key={testimonial.id} className="p-6 rounded-xl border border-neutral-200 bg-white shadow-sm">
              <div className="flex items-center mb-4">
                <img
                  src={testimonial.avatar}
                  alt={testimonial.name}
                  className="w-12 h-12 rounded-full mr-4"
                />
                <div>
                  <h3 className="font-semibold">{testimonial.name}</h3>
                  <p className="text-sm text-neutral-500">{testimonial.role}{testimonial.company && `, ${testimonial.company}`}</p>
                </div>
              </div>
              <p className="text-neutral-600 italic">"{testimonial.content}"</p>
            </div>
          ))}
        </Grid>
      </Section>

      {/* Why Join Section */}
      <Section background="light" spacing="xl">
        <Grid cols={1} mdCols={2} gap={12}>
          <div className="space-y-8">
            <h2 className="text-3xl font-bold text-neutral-900">Pourquoi rejoindre Hi 3D Artist ?</h2>
            <p className="text-neutral-600">
              Rejoignez une plateforme dédiée aux artistes 3D pour développer votre carrière et collaborer sur des projets passionnants.
            </p>

            <div className="space-y-4">
              {[
                "Accédez à des projets exclusifs",
                "Développez votre réseau professionnel",
                "Apprenez des meilleurs artistes 3D",
                "Monétisez vos compétences",
              ].map((benefit) => (
                <div key={benefit} className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-primary-600 mr-2 flex-shrink-0" />
                  <p>{benefit}</p>
                </div>
              ))}
            </div>

            <div>
              <Button
                variant="primary"
                size="lg"
                onClick={() => navigate('/register')}
              >
                Créer un compte
              </Button>
            </div>
          </div>

          <div className="bg-neutral-200 rounded-lg overflow-hidden">
            <img
              src="https://plus.unsplash.com/premium_photo-1716206284519-20b1ced49f39?q=80&w=1036&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
              alt="Workspace 3D"
              className="w-full h-full object-cover"
            />
          </div>
        </Grid>
      </Section>

      {/* CTA Section avec classes CSS */}
      <div className="cta-section">
        <div
          className="cta-background"
          style={{
            backgroundImage: `url(https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)`
          }}
        >
          <div className="cta-overlay"></div>
        </div>

        <div className="cta-content">
          <div className="cta-container">
            <div className="cta-text-center">
              <h2 className="cta-title">
                Prêt à commencer ?
              </h2>
              <p className="cta-description">
                Rejoignez Hi 3D Artist dès aujourd'hui et commencez à explorer des opportunités passionnantes dans le monde de la 3D.
              </p>
              <div className="cta-buttons">
                <button
                  type="button"
                  onClick={() => navigate('/lists-independants')}
                  className="cta-button-primary"
                >
                  Trouver un professionnel
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/register')}
                  className="cta-button-secondary"
                >
                  Créer un compte
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

    </Layout>
  );
};

// Optimiser le composant avec React.memo pour éviter les re-rendus inutiles
export default React.memo(HomePage);
