import React, { useState, useEffect } from 'react';
import { Briefcase, DollarSign, Clock, Image, Tag, Check, Eye, EyeOff } from 'lucide-react';
import Button from '../ui/Button';
import FormInput from '../ui/FormInput';
import FormTextarea from '../ui/FormTextarea';
import FormSelect from '../ui/FormSelect';
import FormMultiSelect from '../ui/FormMultiSelect';
import Checkbox from '../ui/Checkbox';
import type { ServiceOffer, ServiceOfferFormData } from './types';
import { MAIN_CATEGORIES, ARCHITECTURE_3D_CATEGORIES, getCategoryOptions } from '../../data/categories';

interface ServiceOfferFormProps {
  initialData?: ServiceOffer;
  onSubmit: (data: ServiceOfferFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const ServiceOfferForm: React.FC<ServiceOfferFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const isEditMode = !!initialData?.id;

  // Options pour les catégories principales
  const categoryOptions = getCategoryOptions(MAIN_CATEGORIES);

  // Options pour les sous-catégories d'Architecture 3D
  const [showArchSubcategories, setShowArchSubcategories] = useState<boolean>(
    !!initialData?.categories && initialData.categories.some(cat =>
      cat === 'architectural' ||
      cat.startsWith('arch_') ||
      cat.startsWith('viz_') ||
      cat.startsWith('bim_') ||
      cat.startsWith('interior_') ||
      cat.startsWith('vr_arch_')
    ) || false
  );

  const architecturalSubcategoryOptions = getCategoryOptions(ARCHITECTURE_3D_CATEGORIES);

  // Options pour le temps d'exécution
  const executionTimeOptions = [
    { value: '', label: 'Sélectionnée une période' },
    { value: 'Moins d\'une semaine', label: 'Moins d\'une semaine' },
    { value: 'D\'ici 1 à 2 semaines', label: 'D\'ici 1 à 2 semaines' },
    { value: 'Dans le mois', label: 'Dans le mois' },
    { value: 'Dans les 2 mois', label: 'Dans les 2 mois' },
    { value: 'Dans les 3 mois', label: 'Dans les 3 mois' },
  ];

  // Options pour les concepts
  // const conceptOptions = Array.from({ length: 10 }, (_, i) => {
  //   const num = i + 1;
  //   return {
  //     value: `${num} concept${num > 1 ? 's' : ''}`,
  //     label: `${num} concept${num > 1 ? 's' : ''}`
  //   };
  // });

  // // Options pour les révisions
  // const revisionOptions = Array.from({ length: 10 }, (_, i) => {
  //   const num = i + 1;
  //   return {
  //     value: `${num} révision${num > 1 ? 's' : ''}`,
  //     label: `${num} révision${num > 1 ? 's' : ''}`
  //   };
  // });

  // Options pour les concepts avec valeur par défaut
const conceptOptions = [
  { value: '', label: 'Sélectionner un concept' },
  ...Array.from({ length: 10 }, (_, i) => {
    const num = i + 1;
    return {
      value: `${num} concept${num > 1 ? 's' : ''}`,
      label: `${num} concept${num > 1 ? 's' : ''}`
    };
  })
];

// Options pour les révisions avec valeur par défaut
const revisionOptions = [
  { value: '', label: 'Sélectionner une révision' },
  ...Array.from({ length: 10 }, (_, i) => {
    const num = i + 1;
    return {
      value: `${num} révision${num > 1 ? 's' : ''}`,
      label: `${num} révision${num > 1 ? 's' : ''}`
    };
  })
];


  // Options pour le statut
  const statusOptions = [
    { value: 'draft', label: 'Brouillon' },
    { value: 'published', label: 'Publié' },
    { value: 'archived', label: 'Archivé' },
  ];

  // Form state
  const [formData, setFormData] = useState<ServiceOfferFormData>({
    title: initialData?.title || '',
    description: initialData?.description || '',
    price: initialData?.price || '',
    execution_time: initialData?.execution_time || '',
    concepts: initialData?.concepts || '',
    revisions: initialData?.revisions || '',
    categories: initialData?.categories || [],
    is_private: initialData?.is_private ?? false,
    status: initialData?.status || 'published',
    image: null,
  });

  // Image preview
  const [imagePreview, setImagePreview] = useState<string | null>(initialData?.imageUrl || null);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);


  // Form validation errors
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Current step (for multi-step form)
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 2;

  useEffect(() => {
    if (initialData?.file_urls && Array.isArray(initialData.file_urls)) {
      setImagePreviews(initialData.file_urls);
      setFormData(prev => ({ ...prev, images: [] })); // vide pour les nouveaux ajouts
    }
  }, [initialData]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Handle multi-select changes
  const handleMultiSelectChange = (name: string, values: string[]): void => {
    setFormData(prev => ({ ...prev, [name]: values }));
  };

  // Handle image change
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // const file = e.target.files?.[0] || null;

    // if (file) {
    //   // Vérifier si le type de fichier est autorisé
    //   const allowedTypes = [
    //     'image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/svg+xml',
    //     'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    //     'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    //     'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    //     'application/zip', 'application/x-rar-compressed'
    //   ];

    //   if (!allowedTypes.includes(file.type)) {
    //     // Afficher une erreur si le type n'est pas autorisé
    //     setErrors(prev => ({
    //       ...prev,
    //       image: `Le type de fichier "${file.type}" n'est pas autorisé. Types autorisés : JPEG, PNG, GIF, SVG, PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, ZIP, RAR.`
    //     }));
    //     return;
    //   }

    //   // Effacer l'erreur si le type est autorisé
    //   if (errors.image) {
    //     setErrors(prev => {
    //       const newErrors = { ...prev };
    //       delete newErrors.image;
    //       return newErrors;
    //     });
    //   }

    //   // Mettre à jour le state avec le fichier
    //   setFormData(prev => ({ ...prev, image: file }));

    //   // Afficher l'aperçu
    //   const reader = new FileReader();
    //   reader.onloadend = () => {
    //     setImagePreview(reader.result as string);
    //   };
    //   reader.readAsDataURL(file);
    // } else {
    //   setImagePreview(initialData?.imageUrl || null);
    //   setFormData(prev => ({ ...prev, image: null }));
    // }

    const files = e.target.files;
    if (files) {
      const fileList = Array.from(files);
      setFormData(prev => ({ ...prev, images: fileList }));

      // Créer des URLs de prévisualisation pour toutes les images
      const previews = fileList
        .filter(file => file.type.startsWith('image/'))
        .map(file => URL.createObjectURL(file));
      setImagePreviews(previews);
    }
  };

  // Form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Si nous ne sommes pas à la dernière étape, ne pas soumettre
    if (currentStep < totalSteps) {
      nextStep();
      return;
    }

    // Validate form
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Le titre est requis';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'La description est requise';
    }

    if (!formData.price) {
      newErrors.price = 'Le prix est requis';
    }

    if (!formData.execution_time) {
      newErrors.execution_time = 'Le temps d\'exécution est requis';
    }

    if (!formData.concepts) {
      newErrors.concepts = 'Le nombre de concepts est requis';
    }

    if (!formData.revisions) {
      newErrors.revisions = 'Le nombre de révisions est requis';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Submit form
    onSubmit(formData);
  };

  // Next step
  const nextStep = () => {
    // Validate current step
    const newErrors: Record<string, string> = {};

    if (currentStep === 1) {
      if (!formData.title.trim()) {
        newErrors.title = 'Le titre est requis';
      }

      if (!formData.description.trim()) {
        newErrors.description = 'La description est requise';
      }

      if (!formData.price) {
        newErrors.price = 'Le prix est requis';
      }

      if (!formData.execution_time) {
        newErrors.execution_time = 'Le temps d\'exécution est requis';
      }
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setCurrentStep(prev => Math.min(prev + 1, totalSteps));
  };

  // Previous step
  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-neutral-200 overflow-hidden">
      <div className="px-6 py-4 border-b border-neutral-200">
        <h2 className="text-xl font-semibold text-neutral-900">
          {isEditMode ? 'Modifier le service' : 'Créer un nouveau service'}
        </h2>
        <p className="text-neutral-600 text-sm mt-1">
          {isEditMode
            ? 'Mettez à jour les détails de votre service'
            : 'Remplissez les détails de votre service pour le proposer aux clients'}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="p-6">
        {/* Step indicator */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            {Array.from({ length: totalSteps }).map((_, index) => (
              <div key={index} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  currentStep > index + 1
                    ? 'bg-green-500 text-white'
                    : currentStep === index + 1
                    ? 'bg-blue-500 text-white'
                    : 'bg-neutral-200 text-neutral-600'
                }`}>
                  {currentStep > index + 1 ? <Check className="h-5 w-5" /> : index + 1}
                </div>
                {index < totalSteps - 1 && (
                  <div className={`h-1 w-16 sm:w-32 ${
                    currentStep > index + 1 ? 'bg-green-500' : 'bg-neutral-200'
                  }`}></div>
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-between mt-2">
            <span className="text-sm text-neutral-600">Informations de base</span>
            <span className="text-sm text-neutral-600">Détails du service</span>
          </div>
        </div>

        <div className="space-y-6">
          {currentStep === 1 && (
            <>
              {/* Image */}
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">
                  Image du service
                  {!isEditMode && <span className="text-red-500 ml-1">*</span>}
                </label>
                <div className="mt-1 flex items-center">
                  {imagePreviews.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {imagePreviews.map((preview, index) => (
                        <div key={index} className="relative">
                          <img
                            src={preview}
                            alt={`Aperçu ${index + 1}`}
                            className="w-32 h-32 object-cover rounded-md border border-neutral-300"
                          />
                          <button
                            type="button"
                            onClick={() => {
                              const newImages = [...(formData.images || [])];
                              const newPreviews = [...imagePreviews];

                              newImages.splice(index, 1);
                              newPreviews.splice(index, 1);

                              setFormData(prev => ({ ...prev, images: newImages }));
                              setImagePreviews(newPreviews);
                            }}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                            title="Supprimer l'image"
                          >
                            ✕
                          </button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex items-center justify-center w-32 h-32 border-2 border-dashed border-neutral-300 rounded-md">
                      <div className="text-center">
                        <Image className="mx-auto h-8 w-8 text-neutral-400" />
                        <span className="mt-1 block text-sm font-medium text-neutral-500">
                          Ajouter une image
                        </span>
                      </div>
                      <input
                        type="file"
                        id="images"
                        name="images"
                        accept="image/*,application/pdf"
                        multiple
                        onChange={handleImageChange}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        title="Sélectionner des fichiers"
                      />
                    </div>
                  )}

                  {/* {imagePreview ? (
                    <div className="relative">
                      <img
                        src={imagePreview}
                        alt="Aperçu"
                        className="w-32 h-32 object-cover rounded-md border border-neutral-300"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          setImagePreview(null);
                          setFormData(prev => ({ ...prev, image: null }));
                        }}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                        title="Supprimer l'image"
                      >
                        <span className="sr-only">Supprimer l'image</span>
                        ✕
                      </button>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center w-32 h-32 border-2 border-dashed border-neutral-300 rounded-md">
                      <div className="text-center">
                        <Image className="mx-auto h-8 w-8 text-neutral-400" />
                        <span className="mt-1 block text-sm font-medium text-neutral-500">
                          Ajouter une image
                        </span>
                      </div>
                      <input
                        type="file"
                        id="image"
                        name="image"
                        accept="image/jpeg,image/png,image/jpg,image/gif,image/svg+xml,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,application/zip,application/x-rar-compressed"
                        multiple
                        onChange={handleImageChange}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        title="Sélectionner un fichier"
                      />
                    </div>
                  )} */}
                </div>
                {errors.image && (
                  <p className="mt-1 text-sm text-red-600">{errors.image}</p>
                )}
              </div>

              {/* Title */}
              <FormInput
                label="Titre du service"
                id="title"
                name="title"
                placeholder="Ex: Création de personnage 3D réaliste"
                value={formData.title}
                onChange={handleInputChange}
                error={errors.title}
                icon={<Briefcase className="h-5 w-5 text-neutral-400" />}
                required
              />

              {/* Description */}
              <FormTextarea
                label="Description"
                id="description"
                name="description"
                placeholder="Décrivez votre service en détail..."
                value={formData.description}
                onChange={handleInputChange}
                error={errors.description}
                rows={4}
                required
              />

              {/* Price */}
              <FormInput
                label="Prix (€)"
                id="price"
                name="price"
                type="number"
                min="0"
                step="0.01"
                placeholder="Ex: 150"
                value={formData.price.toString()}
                onChange={handleInputChange}
                error={errors.price}
                icon={<DollarSign className="h-5 w-5 text-neutral-400" />}
                required
              />

              {/* Execution Time */}
              <FormSelect
                label="Temps d'exécution"
                id="execution_time"
                name="execution_time"
                options={executionTimeOptions}
                value={formData.execution_time}
                onChange={handleInputChange}
                error={errors.execution_time}
                icon={<Clock className="h-5 w-5 text-neutral-400" />}
                required
              />

              {/* Categories */}
              <FormMultiSelect
                label="Catégories"
                id="categories"
                options={categoryOptions}
                value={formData.categories.filter(cat => !cat.startsWith('arch_') && !cat.startsWith('viz_') && !cat.startsWith('bim_') && !cat.startsWith('interior_') && !cat.startsWith('vr_arch_'))}
                onChange={(values: string[]) => {
                  // Vérifier si Architecture 3D est sélectionné
                  const hasArchitectural = values.includes('architectural');
                  setShowArchSubcategories(hasArchitectural);

                  // Filtrer les sous-catégories d'Architecture 3D existantes
                  const existingSubcategories = formData.categories.filter(cat =>
                    cat.startsWith('arch_') || cat.startsWith('viz_') || cat.startsWith('bim_') ||
                    cat.startsWith('interior_') || cat.startsWith('vr_arch_')
                  );

                  // Si Architecture 3D n'est plus sélectionné, supprimer les sous-catégories
                  const newCategories = hasArchitectural
                    ? [...values, ...existingSubcategories]
                    : values.filter(cat => cat !== 'architectural');

                  handleMultiSelectChange('categories', newCategories);
                }}
                placeholder="Sélectionnez les catégories"
                icon={<Tag className="h-5 w-5 text-neutral-400" />}
              />

              {/* Sous-catégories d'Architecture 3D */}
              {showArchSubcategories && (
                <div className="mt-4 p-4 bg-neutral-50 rounded-lg border border-neutral-200">
                  <h3 className="text-sm font-medium text-neutral-700 mb-2">
                    Sous-catégories d'Architecture 3D
                  </h3>
                  <FormMultiSelect
                    label="Spécialités en Architecture 3D"
                    id="architectural_subcategories"
                    options={architecturalSubcategoryOptions}
                    value={formData.categories.filter(cat =>
                      cat.startsWith('arch_') || cat.startsWith('viz_') || cat.startsWith('bim_') ||
                      cat.startsWith('interior_') || cat.startsWith('vr_arch_')
                    )}
                    onChange={(values: string[]) => {
                      // Filtrer les catégories principales
                      const mainCategories = formData.categories.filter(cat =>
                        !cat.startsWith('arch_') && !cat.startsWith('viz_') && !cat.startsWith('bim_') &&
                        !cat.startsWith('interior_') && !cat.startsWith('vr_arch_')
                      );

                      // Combiner avec les nouvelles sous-catégories
                      handleMultiSelectChange('categories', [...mainCategories, ...values]);
                    }}
                    placeholder="Sélectionnez vos spécialités en Architecture 3D"
                    icon={<Tag className="h-5 w-5 text-neutral-400" />}
                  />
                </div>
              )}
            </>
          )}

          {currentStep === 2 && (
            <>
              {/* Concepts */}
              <FormSelect
                label="Nombre de concepts"
                id="concepts"
                name="concepts"
                options={conceptOptions}
                value={formData.concepts}
                onChange={handleInputChange}
                error={errors.concepts}
                required
              />

              {/* Revisions */}
              <FormSelect
                label="Nombre de révisions"
                id="revisions"
                name="revisions"
                options={revisionOptions}
                value={formData.revisions}
                onChange={handleInputChange}
                error={errors.revisions}
                required
              />

              {/* Status */}
              <FormSelect
                label="Statut"
                id="status"
                name="status"
                options={statusOptions}
                value={formData.status}
                onChange={handleInputChange}
                error={errors.status}
              />

              {/* Is Private */}
              <div className="flex items-center">
                <Checkbox
                  id="is_private"
                  name="is_private"
                  checked={formData.is_private}
                  onChange={handleCheckboxChange}
                />
                <div className="ml-3">
                  <label htmlFor="is_private" className="text-sm font-medium text-neutral-700">
                    Service privé
                  </label>
                  <p className="text-sm text-neutral-500">
                    Si activé, ce service ne sera visible que par vous et les clients que vous invitez
                  </p>
                </div>
                {formData.is_private ? (
                  <EyeOff className="ml-2 h-5 w-5 text-neutral-400" />
                ) : (
                  <Eye className="ml-2 h-5 w-5 text-neutral-400" />
                )}
              </div>
            </>
          )}

          {/* Form Actions */}
          <div className="flex justify-between space-x-3 pt-4 border-t border-neutral-200">
            {currentStep > 1 && (
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
                disabled={isLoading}
              >
                Précédent
              </Button>
            )}

            <div className="flex-1"></div>

            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Annuler
            </Button>

            {currentStep < totalSteps ? (
              <Button
                type="button"
                variant="primary"
                onClick={nextStep}
                style={{ backgroundColor: '#2980b9', color: 'black' }}
              >
                Suivant
              </Button>
            ) : (
              <Button
                type="button"
                variant="primary"
                onClick={handleSubmit}
                isLoading={isLoading}
                style={{ backgroundColor: '#2980b9', color: 'black' }}
              >
                {isEditMode ? 'Mettre à jour le service' : 'Créer le service'}
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default ServiceOfferForm;
