<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Service Offers Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration pour les offres de service
    |
    */

    'price_units' => [
        'per_image' => [
            'value' => 'per_image',
            'label' => 'par image',
            'description' => 'Prix calculé par image produite'
        ],
        'per_sqm' => [
            'value' => 'per_sqm',
            'label' => 'par m²',
            'description' => 'Prix calculé par mètre carré'
        ],
        'per_project' => [
            'value' => 'per_project',
            'label' => 'par projet',
            'description' => 'Prix forfaitaire pour l\'ensemble du projet'
        ],
    ],

    'average_durations' => [
        'less_than_week' => [
            'value' => 'less_than_week',
            'label' => 'moins d\'une semaine',
            'description' => 'Livraison en moins de 7 jours'
        ],
        '1_to_2_weeks' => [
            'value' => '1_to_2_weeks',
            'label' => '1 à 2 semaines',
            'description' => 'Livraison entre 1 et 2 semaines'
        ],
        '1_month' => [
            'value' => '1_month',
            'label' => '1 mois',
            'description' => 'Livraison en 1 mois'
        ],
        '2_months' => [
            'value' => '2_months',
            'label' => '2 mois',
            'description' => 'Livraison en 2 mois'
        ],
        '3_months' => [
            'value' => '3_months',
            'label' => '3 mois',
            'description' => 'Livraison en 3 mois'
        ],
    ],

    'image_categories' => [
        'architectural' => 'Architecture 3D',
        'product' => 'Produit 3D',
        'character' => 'Personnage 3D',
        'environment' => 'Environnement 3D',
        'modeling' => 'Modélisation 3D',
        'animation' => 'Animation',
        'rendering' => 'Rendu 3D',
        'visualization' => 'Visualisation',
        'other' => 'Autre',
    ],
];
