import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Mail, Lock, User, AlertCircle, CheckCircle, Github, Facebook } from 'lucide-react';
import { authService, ApiError } from '../../services/api';
import { useToast } from '../../context/ToastContext';
import Button from '../ui/Button';
import FormInput from '../ui/FormInput';
import Checkbox from '../ui/Checkbox';
import FormDivider from '../ui/FormDivider';
import SocialButton from '../ui/SocialButton';
import FormSelect from '../ui/FormSelect';
import './AuthButton.css';

interface RegisterFormProps {
  onToggleForm?: () => void;
}

const RegisterForm: React.FC<RegisterFormProps> = ({ onToggleForm }) => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [accountType, setAccountType] = useState('client');
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Form validation
  const [formErrors, setFormErrors] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeTerms: '',
  });

  const validateForm = () => {
    let isValid = true;
    const errors = {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      confirmPassword: '',
      agreeTerms: '',
    };

    // First name validation
    if (!firstName.trim()) {
      errors.firstName = 'Le prénom est obligatoire';
      isValid = false;
    }

    // Last name validation
    if (!lastName.trim()) {
      errors.lastName = 'Le nom est obligatoire';
      isValid = false;
    }

    // Email validation
    if (!email) {
      errors.email = 'L\'adresse email est obligatoire';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = 'L\'adresse email est invalide';
      isValid = false;
    }

    // Password validation
    if (!password) {
      errors.password = 'Le mot de passe est obligatoire';
      isValid = false;
    } else if (password.length < 8) {
      errors.password = 'Le mot de passe doit contenir au moins 8 caractères';
      isValid = false;
    }

    // Confirm password validation
    if (password !== confirmPassword) {
      errors.confirmPassword = 'Les mots de passe ne correspondent pas';
      isValid = false;
    }

    // Terms agreement validation
    if (!agreeTerms) {
      errors.agreeTerms = 'Vous devez accepter les conditions d\'utilisation';
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    console.log('Register form submitted');

    if (!validateForm()) {
      console.log('Form validation failed');
      // Afficher un toast pour les erreurs de validation
      const hasErrors = Object.values(formErrors).some(error => error !== '');
      if (hasErrors) {
        showToast('error', 'Veuillez corriger les erreurs dans le formulaire');
      }
      return;
    }

    console.log('Form validation passed, attempting registration with:', {
      firstName,
      lastName,
      email,
      password: '***',
      accountType
    });
    setIsLoading(true);
    setError('');
    setSuccess('');

    const payload = {
      first_name: firstName,
      last_name: lastName,
      email,
      password,
      password_confirmation: confirmPassword,
      is_professional: accountType === 'professional',
    };

    console.log('Payload envoyé au serveur:', { ...payload, password: '***', password_confirmation: '***' });

    try {
      // Test de connectivité d'abord
      console.log('Test de connectivité avec le serveur...');

      try {
        await authService.ping();
        console.log('Connectivité OK, envoi de la requête d\'inscription...');
      } catch (pingError) {
        console.error('Erreur lors du test de connectivité:', pingError);
        const errorMessage = 'Impossible de se connecter au serveur. Veuillez réessayer plus tard.';
        setError(errorMessage);
        showToast('error', errorMessage);
        setIsLoading(false);
        return;
      }

      // Inscription
      const result = await authService.register(payload);

      const successMessage = result && typeof result === 'object' && 'message' in result
        ? result.message as string
        : 'Inscription réussie ! Veuillez vérifier votre email pour activer votre compte.';

      setSuccess(successMessage);
      showToast('success', successMessage);

      // Clear form
      setFirstName('');
      setLastName('');
      setEmail('');
      setPassword('');
      setConfirmPassword('');
      setAgreeTerms(false);

      // Redirect to login page after a delay
      setTimeout(() => {
        if (onToggleForm) {
          onToggleForm();
        } else {
          navigate('/login');
        }
      }, 3000);
    } catch (err) {
      console.error('Erreur lors de l\'inscription:', err);
      console.log('API Error Object:', JSON.stringify(err, null, 2));

      let errorMessage = 'Une erreur inconnue est survenue';

      if ((err as ApiError).message) {
        const apiError = err as ApiError;
        // Initialise avec le message de l'API par défaut, ou une valeur générique si non présent
        errorMessage = apiError.message || 'Une erreur est survenue côté serveur.';

        // Gestion spécifique des erreurs d'inscription pour le statut 422 (erreurs de validation)
        if (apiError.status === 422) {
          // Si le message de l'API contient la phrase spécifique d'email déjà utilisé,
          // alors nous utilisons ce message. Sinon, un message générique est affiché.
          if (apiError.message && apiError.message.includes('Cette adresse email est déjà utilisée')) {
            errorMessage = 'Cette adresse email est déjà utilisée.';
          } else {
            // Message générique pour toutes les autres erreurs de validation 422
            errorMessage = 'Veuillez vérifier les informations saisies.';
          }
        }
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }

      setError(errorMessage);
      showToast('error', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-2xl font-bold text-neutral-900">Créer un compte</h1>
        <p className="text-neutral-600 mt-2">
          Rejoignez notre plateforme pour trouver des professionnels ou proposer vos services
        </p>
      </div>

      {/* Success message */}
      {success && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
          <p className="text-green-700">{success}</p>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* Registration form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <FormInput
            label="Prénom"
            type="text"
            id="firstName"
            placeholder="Jean"
            value={firstName}
            onChange={(e) => setFirstName(e.target.value)}
            icon={<User className="h-5 w-5 text-neutral-400" />}
            error={formErrors.firstName}
            required
          />

          <FormInput
            label="Nom"
            type="text"
            id="lastName"
            placeholder="Dupont"
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
            icon={<User className="h-5 w-5 text-neutral-400" />}
            error={formErrors.lastName}
            required
          />
        </div>

        <FormInput
          label="Adresse email"
          type="email"
          id="email"
          placeholder="<EMAIL>"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          icon={<Mail className="h-5 w-5 text-neutral-400" />}
          error={formErrors.email}
          required
        />

        <FormSelect
          label="Type de compte"
          id="accountType"
          value={accountType}
          onChange={(e) => setAccountType(e.target.value)}
          options={[
            { value: 'client', label: 'Client - Je cherche des professionnels' },
            { value: 'professional', label: 'Professionnel - Je propose mes services' },
          ]}
        />

        <FormInput
          label="Mot de passe"
          type="password"
          id="password"
          placeholder="••••••••"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          icon={<Lock className="h-5 w-5 text-neutral-400" />}
          error={formErrors.password}
          showPasswordToggle
          helperText="8 caractères minimum"
          required
        />

        <FormInput
          label="Confirmer le mot de passe"
          type="password"
          id="confirmPassword"
          placeholder="••••••••"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          icon={<Lock className="h-5 w-5 text-neutral-400" />}
          error={formErrors.confirmPassword}
          showPasswordToggle
          required
        />

        <Checkbox
          id="agree-terms"
          label={
            <span>
              J'accepte les{' '}
              <Link to="/terms" className="text-primary-600 hover:text-primary-700">
                conditions d'utilisation
              </Link>{' '}
              et la{' '}
              <Link to="/privacy" className="text-primary-600 hover:text-primary-700">
                politique de confidentialité
              </Link>
            </span>
          }
          checked={agreeTerms}
          onChange={() => setAgreeTerms(!agreeTerms)}
          error={formErrors.agreeTerms}
        />

        <Button
          type="submit"
          variant="primary"
          fullWidth
          disabled={isLoading}
          aria-busy={isLoading}
          className="auth-button"
          style={{ color: 'black' }}
        >
          {isLoading ? 'Chargement...' : 'Créer un compte'}
        </Button>
      </form>

      <FormDivider text="Ou continuer avec" />

      <div className="grid grid-cols-2 gap-3 mt-6">
        <SocialButton
          icon={<Github className="h-5 w-5" />}
          provider="Github"
        />
        <SocialButton
          icon={<Facebook className="h-5 w-5 text-blue-600" />}
          provider="Facebook"
        />
      </div>

      <p className="mt-8 text-center text-sm text-neutral-600">
        Vous avez déjà un compte ?{' '}
        {onToggleForm ? (
          <button
            type="button"
            onClick={onToggleForm}
            className="font-medium text-primary-600 hover:text-primary-700"
          >
            Se connecter
          </button>
        ) : (
          <Link
            to="/login"
            className="font-medium text-primary-600 hover:text-primary-700"
          >
            Se connecter
          </Link>
        )}
      </p>
    </div>
  );
};

export default RegisterForm;
