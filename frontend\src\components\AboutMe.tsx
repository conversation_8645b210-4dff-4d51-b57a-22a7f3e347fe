import React, { useState, useEffect } from "react";
import { X, ChevronDown, Search } from "lucide-react";
import { API_BASE_URL } from '../config';
import { useProfile } from "./ProfileContext";
import { MAIN_CATEGORIES, getAllCategories, Category } from "../data/categories";

const AboutMe = () => {
  const { profile, setProfile } = useProfile();
  const storedProfile = JSON.parse(localStorage.getItem("userProfile") || "{}");
  const initialSkills = storedProfile?.profile_data?.skills || [];

  const [skills, setSkills] = useState<string[]>(initialSkills);
  const [newSkill, setNewSkill] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [filteredSkills, setFilteredSkills] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");

  // Liste des compétences par catégorie
  const skillsByCategory: Record<string, string[]> = {
    'modeling': ['Blender', 'Maya', '3ds Max', 'ZBrush', 'Substance Painter', 'Hard Surface Modeling', 'Organic Modeling'],
    'animation': ['Animation de personnages', 'Animation d\'objets', 'Motion Capture', 'Rigging', 'Facial Animation'],
    'architectural': ['SketchUp', 'Revit', 'ArchiCAD', 'Lumion', 'V-Ray', 'Rendu architectural', 'Modélisation BIM'],
    'product': ['Fusion 360', 'SolidWorks', 'Rhino 3D', 'KeyShot', 'Prototypage 3D', 'Design industriel'],
    'character': ['Character Design', 'Character Modeling', 'Character Rigging', 'Facial Rigging', 'Sculpting'],
    'environment': ['Environment Design', 'Landscape Modeling', 'Terrain Generation', 'World Building', 'Level Design'],
    'vr_ar': ['Unity', 'Unreal Engine', 'WebXR', 'A-Frame', 'ARKit', 'ARCore', 'Oculus SDK'],
    'game_art': ['Game Asset Creation', 'Low Poly Modeling', 'Texture Baking', 'UV Mapping', 'PBR Texturing'],
  };

  // Liste complète de toutes les compétences
  const [allSkills, setAllSkills] = useState<string[]>([]);

  // Initialiser la liste complète des compétences
  useEffect(() => {
    const skills: string[] = [];
    Object.values(skillsByCategory).forEach(categorySkills => {
      categorySkills.forEach(skill => {
        if (!skills.includes(skill)) {
          skills.push(skill);
        }
      });
    });
    setAllSkills(skills.sort());
    setFilteredSkills(skills.sort());
  }, []);

  // Filtrer les compétences en fonction de la catégorie sélectionnée
  useEffect(() => {
    if (selectedCategory && skillsByCategory[selectedCategory]) {
      setFilteredSkills(skillsByCategory[selectedCategory]);
    } else if (selectedCategory === "") {
      setFilteredSkills(allSkills);
    }
  }, [selectedCategory, allSkills]);

  // Filtrer les compétences en fonction du terme de recherche
  useEffect(() => {
    if (searchTerm) {
      const baseSkills = selectedCategory ? skillsByCategory[selectedCategory] : allSkills;
      const filtered = baseSkills.filter(skill =>
        skill.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredSkills(filtered);
    } else {
      if (selectedCategory && skillsByCategory[selectedCategory]) {
        setFilteredSkills(skillsByCategory[selectedCategory]);
      } else {
        setFilteredSkills(allSkills);
      }
    }
  }, [searchTerm, selectedCategory, allSkills]);

  // Ajouter une compétence
  const handleAddSkill = () => {
    if (newSkill.trim() !== "" && !skills.includes(newSkill.trim())) {
      setSkills([...skills, newSkill.trim()]);
      setNewSkill("");
    }
  };

  // Ajouter une compétence depuis la liste
  const handleAddSkillFromList = (skill: string) => {
    if (!skills.includes(skill)) {
      setSkills([...skills, skill]);
    }
  };

  // Supprimer une compétence
  const handleRemoveSkill = (skillToRemove: string) => {
    setSkills(skills.filter((skill) => skill !== skillToRemove));
  };

  // Gestion de l'entrée utilisateur
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewSkill(e.target.value);
  };

  // Gestion de la recherche
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Ajouter une compétence avec "Entrée"
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleAddSkill();
    }
  };

  // Envoyer les compétences à l'API
  const handleSubmit = async () => {
    setLoading(true);
    setMessage("");

    const token = localStorage.getItem("token");

    try {
      const response = await fetch(
        `${API_BASE_URL}/api/profile/completion/skills`,
        {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ skills }),
        }
      );

      const data = await response.json();

      if (response.ok) {
        setMessage("✅ Compétences mises à jour avec succès !");
        // Mettre à jour le localStorage
        const updatedProfile = {
          ...storedProfile,
          profile_data: {
            ...storedProfile.profile_data,
            skills: skills,
          },
        };

        const updateJiab = {
          ...storedProfile,
          profile_data: {
            ...storedProfile.profile_data,
            skills: skills,
          },
        };
        localStorage.setItem("userProfile", JSON.stringify(updatedProfile));
        setProfile(updateJiab)
      } else {
        setMessage("❌ Erreur : " + (data.message || "Impossible d'enregistrer les compétences."));
      }
    } catch (error) {
      setMessage("❌ Erreur réseau, veuillez réessayer.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white px-4 sm:px-6 lg:px-8">
      <h2 className="text-sm font-bold text-gray-700 mb-4">MES COMPÉTENCES</h2>

      {message && (
        <div className={`mb-4 p-2 text-white rounded ${message.startsWith("✅") ? "bg-green-500" : "bg-red-500"}`}>
          {message}
        </div>
      )}

      <div className="space-y-6">
        {/* Sélection de catégorie */}
        <div className="grid grid-cols-1 gap-4">
          <h3 className="text-lg font-semibold mb-2">Sélectionner une catégorie</h3>
          <div className="relative">
            <button
              type="button"
              onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
              className="w-full flex items-center justify-between px-4 py-2 border border-gray-300 rounded-lg bg-white text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <span>{selectedCategory ? MAIN_CATEGORIES.find(cat => cat.value === selectedCategory)?.label || selectedCategory : "Toutes les catégories"}</span>
              <ChevronDown size={20} className={`transition-transform ${showCategoryDropdown ? 'rotate-180' : ''}`} />
            </button>

            {showCategoryDropdown && (
              <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                <div
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                  onClick={() => {
                    setSelectedCategory("");
                    setShowCategoryDropdown(false);
                  }}
                >
                  Toutes les catégories
                </div>
                {MAIN_CATEGORIES.map((category) => (
                  <div
                    key={category.id}
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => {
                      setSelectedCategory(category.value);
                      setShowCategoryDropdown(false);
                    }}
                  >
                    {category.label}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Recherche de compétences */}
        <div className="grid grid-cols-1 gap-4">
          <h3 className="text-lg font-semibold mb-2">Compétences suggérées</h3>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Rechercher une compétence..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Liste des compétences suggérées */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
            {filteredSkills.map((skill, index) => (
              <button
                key={index}
                type="button"
                onClick={() => handleAddSkillFromList(skill)}
                disabled={skills.includes(skill)}
                className={`text-left px-3 py-2 rounded-lg text-sm ${
                  skills.includes(skill)
                    ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-50 text-blue-700 hover:bg-blue-100'
                }`}
                title={skills.includes(skill) ? "Déjà ajouté" : "Ajouter cette compétence"}
              >
                {skill}
              </button>
            ))}
          </div>
        </div>

        {/* Ajout manuel de compétence */}
        <div className="grid grid-cols-1 gap-4">
          <h3 className="text-lg font-semibold mb-2">Ajouter une compétence personnalisée</h3>
          <div className="flex">
            <input
              type="text"
              placeholder="Saisir une compétence personnalisée"
              value={newSkill}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <button
              type="button"
              onClick={handleAddSkill}
              className="px-4 py-2 bg-blue-500 text-white rounded-r-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              title="Ajouter cette compétence"
            >
              Ajouter
            </button>
          </div>
        </div>

        {/* Liste des compétences ajoutées */}
        <div className="grid grid-cols-1 gap-4">
          <h3 className="text-lg font-semibold mb-2">Mes compétences ({skills.length})</h3>
          <div className="flex gap-2 flex-wrap p-4 border border-gray-200 rounded-lg bg-gray-50 min-h-[100px]">
            {skills.length > 0 ? (
              skills.map((skill, index) => (
                <div key={index} className="flex items-center gap-2 bg-white border border-gray-300 rounded-lg px-3 py-1 shadow-sm">
                  <span>{skill}</span>
                  <button
                    type="button"
                    onClick={() => handleRemoveSkill(skill)}
                    className="text-red-500 hover:text-red-700"
                    title="Supprimer cette compétence"
                  >
                    <X size={14} />
                  </button>
                </div>
              ))
            ) : (
              <div className="text-gray-500 text-center w-full flex items-center justify-center">
                Aucune compétence ajoutée
              </div>
            )}
          </div>
        </div>

        {/* Bouton pour envoyer les compétences à l'API */}
        <div className="mt-6">
          <button
            type="button"
            onClick={handleSubmit}
            className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-blue-300"
            disabled={loading}
          >
            {loading ? "Enregistrement en cours..." : "Enregistrer mes compétences"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AboutMe;
