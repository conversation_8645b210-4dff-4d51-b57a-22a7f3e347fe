[2025-07-01 04:38:31] local.INFO: <PERSON><PERSON><PERSON><PERSON><PERSON> lente détectée {"url":"http://127.0.0.1:8000/api/register","method":"POST","execution_time":"5161.52 ms","memory_usage":"3 MB","status_code":422,"user_id":"guest","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 04:38:32] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/register","method":"POST","execution_time":"5161.52 ms","memory_usage":"3 MB","status_code":422,"user_id":"guest","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 04:42:07] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/register","method":"POST","execution_time":"404.37 ms","memory_usage":"3 MB","status_code":422,"user_id":"guest","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:12:34] local.INFO: Requête lente détectée {"url":"http://127.0.0.1:8000/api/register","method":"POST","execution_time":"6643.71 ms","memory_usage":"8 MB","status_code":201,"user_id":"guest","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:12:34] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/register","method":"POST","execution_time":"6643.71 ms","memory_usage":"8 MB","status_code":201,"user_id":"guest","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:12:55] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/email/verify/1/727bb7d57e94fc0838ed1534f01874a1854e8701?expires=1751353948&redirect=http%253A%252F%252Flocalhost%253A3000%252Flogin%253Fverified%253Dtrue&signature=e1cfedf34f3e1c00f559b06aa32e9f7d643c55979fd929a93c094e6c94660982","method":"GET","execution_time":"620.5 ms","memory_usage":"2.12 MB","status_code":302,"user_id":"guest","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:13:15] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/login","method":"POST","execution_time":"781.9 ms","memory_usage":"4.14 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:13:18] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/profile/completion","method":"GET","execution_time":"111.81 ms","memory_usage":"1.26 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:13:19] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/dashboard","method":"GET","execution_time":"324.49 ms","memory_usage":"1.73 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:13:19] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/profile/completion","method":"GET","execution_time":"34.83 ms","memory_usage":"1.26 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:13:20] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/dashboard","method":"GET","execution_time":"61.03 ms","memory_usage":"1.73 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:13:21] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/offer-applications/received","method":"GET","execution_time":"33.87 ms","memory_usage":"0.93 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:13:21] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/offer-applications/received","method":"GET","execution_time":"21.84 ms","memory_usage":"0.93 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:15:31] local.INFO: Requête lente détectée {"url":"http://127.0.0.1:8000/api/profile/complete-profile","method":"POST","execution_time":"1652.61 ms","memory_usage":"3.48 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:15:31] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/profile/complete-profile","method":"POST","execution_time":"1652.61 ms","memory_usage":"3.48 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:15:34] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/profile/completion","method":"GET","execution_time":"44.65 ms","memory_usage":"1.26 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:15:42] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/profile","method":"GET","execution_time":"34.98 ms","memory_usage":"1.27 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:15:42] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/profile","method":"GET","execution_time":"38.91 ms","memory_usage":"1.27 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:15:49] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/profile","method":"GET","execution_time":"35.04 ms","memory_usage":"1.27 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-01 06:15:50] local.DEBUG: Performance {"url":"http://127.0.0.1:8000/api/profile","method":"GET","execution_time":"46.32 ms","memory_usage":"1.27 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
