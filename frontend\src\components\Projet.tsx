import React, { useState } from "react";
import Tabs from "./Tabs";
import ExperiencePro from "./ExperiencePro";
import ProjetPerso from "./ProjetPerso";
import Realisations from "./Realisations";
import ServicesOffert from "./ServicesOffert";
import TraveauxPro from "./TraveauxPro";

const projets = [
  { id: 1, title: "Voiture de course 3D", image: "https://mir-s3-cdn-cf.behance.net/project_modules/fs/96bef2182510483.652efbbf698a2.png", likes: 10, views: 25 },
  { id: 2, title: "Modélisation 3D réaliste", image: "https://mir-s3-cdn-cf.behance.net/project_modules/1400/56c6a5152228177.631a0d192ad00.png", likes: 14, views: 30 },
  { id: 3, title: "Illustration festive", image: "https://mir-s3-cdn-cf.behance.net/project_modules/fs/b07abb152228483.631a0df261254.png", likes: 8, views: 20 },
  { id: 4, title: "Sculpture numérique", image: "https://mir-s3-cdn-cf.behance.net/project_modules/max_1200/d87997152315713.631b961cd3e4b.png", likes: 12, views: 28 },
  { id: 5, title: "Web design avec Blender", image: "https://mir-s3-cdn-cf.behance.net/projects/max_808/e5c708152292343.Y3JvcCw5MDAsNzAzLDAsOTg.png", likes: 6, views: 15 },
  { id: 6, title: "UI/UX", image: "https://mir-s3-cdn-cf.behance.net/project_modules/fs/bedfc7115006887.60466001bd411.png", likes: 16, views: 40 },
];

const Projet: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>("Travaux");

  // Définir la liste des onglets
  const tabs = [
    { id: "Travaux", label: "Travaux" },
    { id: "Services", label: "Services" },
    { id: "Experiences", label: "Expériences" },
    // { id: "ProjetPerso", label: "Projets" },
    { id: "Realisations", label: "Réalisations" },
  ];

  return (
    <div className="min-h-screen bg-white px-4 sm:px-6 lg:px-8">
      {/* Passer la liste des onglets à Tabs */}
      <Tabs tabs={tabs} activeTab={activeTab} setActiveTab={setActiveTab} />

      {activeTab === "Experiences" && <ExperiencePro />}
      {activeTab === "ProjetPerso" && <ProjetPerso />}
      {activeTab === "Realisations" && <Realisations />}
      {activeTab === "Services" && <ServicesOffert />}
      {activeTab === "Travaux" && <TraveauxPro />}
      {activeTab === "Travaux" && (
        <div className="max-w-7xl mx-auto py-12">
          <h2 className="text-3xl font-bold mb-8">Mes Travaux</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
            {projets.map((projet) => (
              <div key={projet.id} className="relative bg-white border rounded-2xl shadow-md overflow-hidden hover:shadow-lg transition duration-300">
                <img src={projet.image} alt={projet.title} className="w-full h-48 object-cover" />
                <div className="p-4">
                  <h3 className="text-xl font-bold text-gray-900">{projet.title}</h3>
                  <div className="flex justify-between items-center mt-2 text-gray-600 text-sm">
                    <span>❤️ {projet.likes}</span>
                    <span>👁️ {projet.views}</span>
                  </div>
                  <button className="w-full mt-4 px-6 py-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition duration-300">
                    Voir le projet
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Projet;