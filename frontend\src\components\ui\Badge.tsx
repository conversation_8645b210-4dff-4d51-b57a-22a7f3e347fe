import React, { ReactNode } from 'react';

export interface BadgeProps {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'neutral' | 'success' | 'warning' | 'error';
  color?: string; // Ajout de la propriété color pour compatibilité
  size?: 'sm' | 'md';
  className?: string;
  onClick?: () => void;
  removable?: boolean;
  onRemove?: () => void;
}

const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'neutral',
  color, // Ajout du paramètre color
  size = 'md',
  className = '',
  onClick,
  removable = false,
  onRemove,
}) => {
  // Base classes
  const baseClasses = 'inline-flex items-center rounded-full font-medium';

  // Variant classes
  const variantClasses = {
    primary: 'bg-primary-100 text-primary-800',
    secondary: 'bg-secondary-100 text-secondary-800',
    neutral: 'bg-neutral-100 text-neutral-800',
    success: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    error: 'bg-red-100 text-red-800',
  };

  // Size classes
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-0.5 text-sm',
  };

  // Clickable classes
  const clickableClasses = onClick ? 'cursor-pointer hover:bg-opacity-80' : '';

  // Combine all classes
  // Si color est fourni, utiliser cette couleur au lieu de la variante
  const variantClass = color ? `bg-${color}-100 text-${color}-800` : variantClasses[variant];
  const badgeClasses = `${baseClasses} ${variantClass} ${sizeClasses[size]} ${clickableClasses} ${className}`;

  return (
    <span
      className={badgeClasses}
      onClick={onClick}
    >
      {children}

      {removable && (
        <button
          type="button"
          className="ml-1.5 -mr-1 h-4 w-4 rounded-full inline-flex items-center justify-center text-current hover:bg-opacity-25 hover:bg-neutral-900 focus:outline-none"
          onClick={(e) => {
            e.stopPropagation();
            onRemove && onRemove();
          }}
        >
          <span className="sr-only">Remove</span>
          <svg className="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
            <path strokeLinecap="round" strokeWidth="1.5" d="M1 1l6 6m0-6L1 7" />
          </svg>
        </button>
      )}
    </span>
  );
};

export default Badge;
