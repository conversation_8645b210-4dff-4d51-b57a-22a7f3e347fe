# Nouveaux champs pour les offres de service

## Résumé des modifications

La logique d'ajout de service a été mise à jour pour inclure les nouveaux champs demandés. Voici un résumé des changements apportés :

## Nouveaux champs ajoutés

### 1. Nom du service (title)
- **Type** : Input texte
- **Validation** : Obligatoire, max 255 caractères
- **Champ existant** : Déjà présent, pas de modification nécessaire

### 2. Catégorie (categories)
- **Type** : Sélecteur de catégories prédéfinies
- **Validation** : Obligatoire, tableau de chaînes
- **Champ existant** : <PERSON>éjà présent, pas de modification nécessaire

### 3. Description (description)
- **Type** : Zone de texte avec possibilité de mise en forme
- **Validation** : Obligatoire, texte
- **Champ existant** : D<PERSON>jà présent, validation mise à jour (maintenant obligatoire)

### 4. Prix de base (price)
- **Type** : Champ numérique avec unité "USD"
- **Validation** : Obligatoire, numérique, min 0
- **Champ existant** : Déjà présent, pas de modification nécessaire

### 5. Unité du prix (price_unit) - NOUVEAU
- **Type** : Menu déroulant
- **Options** : 
  - `per_image` : par image
  - `per_sqm` : par m²
  - `per_project` : par projet
- **Validation** : Obligatoire
- **Base de données** : Enum avec valeur par défaut 'per_project'

### 6. Durée moyenne de réalisation (average_duration) - NOUVEAU
- **Type** : Liste déroulante ou boutons radio
- **Options** :
  - `less_than_week` : moins d'une semaine
  - `1_to_2_weeks` : 1 à 2 semaines
  - `1_month` : 1 mois
  - `2_months` : 2 mois
  - `3_months` : 3 mois
- **Validation** : Obligatoire
- **Base de données** : Enum nullable

### 7. Image de couverture (cover_image) - NOUVEAU
- **Type** : Champ de sélection/upload d'image
- **Validation** : Optionnel, fichier image (jpeg, png, jpg, gif, svg, webp), max 10MB
- **Base de données** : String nullable
- **Stockage** : `storage/service_cover_images/`

### 8. Catégorie d'image (image_category) - NOUVEAU
- **Type** : Sélecteur de catégorie d'image
- **Options** : Configurables via `config/service_offers.php`
- **Validation** : Optionnel, string max 255
- **Base de données** : String nullable

### 9. Projet associé (associated_project_id) - NOUVEAU
- **Type** : Champ de sélection lié aux projets publiés
- **Validation** : Optionnel, doit exister dans `dashboard_projects`
- **Base de données** : Foreign key vers `dashboard_projects` avec `onDelete('set null')`

## Fichiers modifiés

### 1. Migration
- `2025_07_01_063608_add_new_fields_to_service_offers_table.php`
- Ajoute les nouveaux champs à la table `service_offers`

### 2. Modèle
- `app/Models/ServiceOffer.php`
  - Ajout des nouveaux champs dans `$fillable`
  - Ajout de la relation `associatedProject()`
  - Ajout des méthodes helper `getPriceUnitLabel()` et `getAverageDurationLabel()`

### 3. Requests de validation
- `app/Http/Requests/StoreServiceOfferRequest.php`
  - Ajout des règles de validation pour les nouveaux champs
  - Mise à jour des messages d'erreur
- `app/Http/Requests/UpdateServiceOfferRequest.php`
  - Ajout des règles de validation pour les nouveaux champs
  - Mise à jour des messages d'erreur

### 4. Contrôleur
- `app/Http/Controllers/Api/ServiceOfferController.php`
  - Gestion de l'upload de l'image de couverture dans `store()` et `update()`
  - Suppression de l'image de couverture dans `destroy()`
  - Ajout de la méthode `getAvailableProjects()`

### 5. Ressource API
- `app/Http/Resources/ServiceOfferResource.php`
  - Ajout des nouveaux champs dans la réponse JSON
  - Inclusion des labels lisibles pour les enums
  - Ajout de l'URL complète pour l'image de couverture

### 6. Configuration
- `config/service_offers.php` (nouveau fichier)
  - Configuration des options pour les unités de prix
  - Configuration des options pour les durées moyennes
  - Configuration des catégories d'images

### 7. Contrôleur de configuration
- `app/Http/Controllers/Api/ServiceOfferConfigController.php` (nouveau fichier)
  - Endpoints pour récupérer les options de configuration

### 8. Routes
- `routes/api.php`
  - Ajout de la route `GET /service-offers/available-projects`
  - Ajout des routes de configuration :
    - `GET /service-offers/config/price-units`
    - `GET /service-offers/config/average-durations`
    - `GET /service-offers/config/image-categories`
    - `GET /service-offers/config/all-options`

## Nouvelles API endpoints

### Configuration des services
```
GET /api/service-offers/config/all-options
GET /api/service-offers/config/price-units
GET /api/service-offers/config/average-durations
GET /api/service-offers/config/image-categories
```

### Projets disponibles
```
GET /api/service-offers/available-projects
```

## Utilisation côté frontend

### Récupération des options de configuration
```javascript
// Récupérer toutes les options
const response = await fetch('/api/service-offers/config/all-options');
const options = await response.json();

// Utiliser les options dans les formulaires
const priceUnits = options.price_units;
const durations = options.average_durations;
const imageCategories = options.image_categories;
```

### Récupération des projets disponibles
```javascript
const response = await fetch('/api/service-offers/available-projects');
const projects = await response.json();
```

### Création d'une offre de service
```javascript
const formData = new FormData();
formData.append('title', 'Mon service');
formData.append('description', 'Description du service');
formData.append('price', '100');
formData.append('price_unit', 'per_project');
formData.append('average_duration', '1_to_2_weeks');
formData.append('categories[]', 'modeling');
formData.append('cover_image', imageFile);
formData.append('image_category', 'architectural');
formData.append('associated_project_id', '1');

const response = await fetch('/api/service-offers', {
    method: 'POST',
    body: formData,
    headers: {
        'Authorization': 'Bearer ' + token
    }
});
```

## Notes importantes

1. **Rétrocompatibilité** : Les anciens champs (`execution_time`, `concepts`, `revisions`) sont maintenant optionnels
2. **Validation** : La description est maintenant obligatoire
3. **Stockage** : Les images de couverture sont stockées dans `storage/service_cover_images/`
4. **Relations** : Nouvelle relation avec `dashboard_projects`
5. **Configuration** : Les options sont centralisées dans `config/service_offers.php`
