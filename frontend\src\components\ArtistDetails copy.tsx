import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { API_BASE_URL } from '../config';
import Header from './Header'; // Import du Header
import Footer from './Footer'; // Import du Footer
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faClock, faMapMarkerAlt, faEnvelope } from "@fortawesome/free-solid-svg-icons";

interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  email_verified_at: string | null;
  is_professional: boolean;
  created_at: string;
  updated_at: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  skills?: string[];
  languages?: string[];
  availability_status?: string;
  services_offered?: string[];
  completion_percentage?: number;
}

function ArtistDetails() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // État pour l'artiste et l'état de chargement
  const [artist, setArtist] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // État pour les commentaires
  const [comments, setComments] = useState<string[]>([]);
  const [newComment, setNewComment] = useState('');

  // Fonction pour ajouter un commentaire
  const handleAddComment = () => {
    if (newComment.trim()) {
      setComments([...comments, newComment]);
      setNewComment('');
    }
  };

  useEffect(() => {
    const fetchArtistCompletion = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/profile/completion`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        });
        const data = await response.json();

        console.log("data============", data)


        if (response.ok) {
          setArtist({
            ...data.profile_data,
            completion_percentage: data.completion_percentage,
          });
        } else {
          setError('Impossible de récupérer les détails de l\'artiste');
        }
      } catch (err) {
        setError('Une erreur est survenue lors de la récupération des données');
      } finally {
        setLoading(false);
      }
    };

    const fetchArtist = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/users/${id}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        });
        const data = await response.json();

        console.log("data============", data)


        if (response.ok) {
          setArtist({
            ...data.user,
            completion_percentage: data.completion_percentage,
          });
        } else {
          setError('Impossible de récupérer les détails de l\'artiste');
        }
      } catch (err) {
        setError('Une erreur est survenue lors de la récupération des données');
      } finally {
        setLoading(false);
      }
    };

    fetchArtist();
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex justify-center items-center">
        <p>Chargement...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white flex justify-center items-center">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <Header /> {/* Utilisation du Header */}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <button
          onClick={() => navigate(-1)}
          className="mb-4 text-blue-600 hover:text-blue-800"
        >
          &larr; Retour
        </button>

        {/* Section principale de l'artiste */}
        <div className="bg-gray-100 rounded-lg overflow-hidden">
          <img
            src="https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D"
            alt="Artiste"
            className="w-full h-96 object-cover"
          />
          <div className="p-6">
            <h1 className="text-3xl font-bold mb-4">
              {artist ? `${artist.first_name} ${artist.last_name}` : 'Chargement...'}
            </h1>
            <p className="text-gray-600 mb-4">{artist ? artist.email : 'Chargement...'}</p>
            <p className="text-gray-600 mb-4">
              <strong>Professionnel : </strong>
              {artist ? (artist.is_professional ? 'Oui' : 'Non') : 'Chargement...'}
            </p>
            <p className="text-gray-600 mb-4">
              <strong>Inscrit le :</strong>{' '}
              {artist ? new Date(artist.created_at).toLocaleDateString() : 'Chargement...'}
            </p>

            {/* Informations supplémentaires du profil */}
            <div className="space-y-2 text-gray-600">
              <p className="flex items-center">
                <FontAwesomeIcon icon={faClock} className="mr-2 text-gray-400" />
                Compétences :{' '}
                {artist?.skills ? artist.skills.join(', ') : 'Aucune compétence renseignée'}
              </p>
              <p className="flex items-center">
                <FontAwesomeIcon icon={faClock} className="mr-2 text-gray-400" />
                Services offerts :{' '}
                {artist?.services_offered ? artist.services_offered.join(', ') : 'Aucun service renseigné'}
              </p>
              <p className="flex items-center">
                <FontAwesomeIcon icon={faClock} className="mr-2 text-gray-400" />
                Langues :{' '}
                {artist?.languages ? artist.languages.join(', ') : 'Aucune langue renseignée'}
              </p>
              <p className="flex items-center">
                <FontAwesomeIcon icon={faMapMarkerAlt} className="mr-2 text-gray-400" />
                {artist?.city}, {artist?.country}
              </p>
            </div>
          </div>
        </div>

        {/* Section des commentaires */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold mb-6">Commentaires</h2>

          {/* Formulaire pour ajouter un commentaire */}
          <div className="mb-8">
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Ajouter un commentaire..."
              className="w-full p-4 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows={4}
            />
            <button
              onClick={handleAddComment}
              className="mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Poster un commentaire
            </button>
          </div>

          {/* Liste des commentaires */}
          <div className="space-y-4">
            {comments.map((comment, index) => (
              <div key={index} className="bg-white p-4 border rounded-lg shadow-sm">
                <p className="text-gray-600">{comment}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      <Footer /> {/* Utilisation du Footer */}
    </div>
  );
}

export default ArtistDetails;