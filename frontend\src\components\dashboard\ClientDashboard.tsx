import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Briefcase,
  DollarSign,
  Users,
  Clock,
  MessageSquare,
  Plus,
  FileText,
  Bell,
  CheckCircle,
  XCircle,
  Calendar,
  Search,
  Star,
  Send
} from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from './DashboardLayout';
import StatCard from './StatCard';
import ActivityFeed from './ActivityFeed';
import ProjectCard, { ProjectCardProps } from './ProjectCard';
import PendingOfferCard from './PendingOfferCard';
import UnifiedProfileLink from './UnifiedProfileLink';
import Button from '../ui/Button';
import Avatar from '../ui/Avatar';

interface ClientDashboardProps {
  completionPercentage: number;
}

const ClientDashboard: React.FC<ClientDashboardProps> = ({ completionPercentage }) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState({
    activeProjects: 0,
    totalSpent: '0',
    connectedProfessionals: 0,
    projectsCompleted: 0,
  });
  const [projects, setProjects] = useState<ProjectCardProps[]>([]);
  const [inProgressOffers, setInProgressOffers] = useState<ProjectCardProps[]>([]);
  const [completedOffers, setCompletedOffers] = useState<ProjectCardProps[]>([]);
  const [activities, setActivities] = useState<any[]>([]);
  const [profile, setProfile] = useState<any>(null);
  const [recommendedProfessionals, setRecommendedProfessionals] = useState<any[]>([]);
  const [pendingOffers, setPendingOffers] = useState<any[]>([]);

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');

  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        // Fetch dashboard data from API
        const dashboardResponse = await fetch(`${API_BASE_URL}/api/dashboard`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        // Fetch projects from DashboardProject API
        const projectsResponse = await fetch(`${API_BASE_URL}/api/dashboard/projects`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        // Fetch open offers in progress
        const inProgressOffersResponse = await fetch(`${API_BASE_URL}/api/client/open-offers/in-progress`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        // Fetch completed open offers // closed-completed-offers
        const completedOffersResponse = await fetch(`${API_BASE_URL}/api/client/open-offers/completed`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        // Fetch pending offers (appels d'offre en attente)
        const pendingOffersResponse = await fetch(`${API_BASE_URL}/api/client/open-offers`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!dashboardResponse.ok || !projectsResponse.ok) {
          // Si l'API renvoie une erreur, utiliser des données statiques
          console.warn('Fallback to static data due to API error:',
            dashboardResponse.status, projectsResponse.status);
          loadStaticData();
          return;
        }

        const dashboardData = await dashboardResponse.json();
        const projectsData = await projectsResponse.json();

        // Ajouter des logs détaillés pour vérifier la structure des données
        console.log('Dashboard data structure:', JSON.stringify(dashboardData, null, 2));
        console.log('Projects data structure:', JSON.stringify(projectsData, null, 2));

        // Traiter les données des appels d'offre en attente
        if (pendingOffersResponse.ok) {
          const pendingOffersData = await pendingOffersResponse.json();
          console.log('Client open offers data:', pendingOffersData);

          if (pendingOffersData.client_open_offers && pendingOffersData.client_open_offers.length > 0) {
            setPendingOffers(pendingOffersData.client_open_offers.map((offer: any) => ({
              id: offer.id,
              title: offer.title,
              description: offer.description,
              createdAt: offer.created_at,
              user_id: offer.user_id, // Ajouter l'ID de l'utilisateur propriétaire
              filters: {
                location: offer.location,
                languages: offer.languages || [],
                skills: offer.skills || [],
              },
              maxProposals: offer.max_proposals || 10,
            })));
          }
        } else {
          console.warn('Failed to fetch pending offers:', pendingOffersResponse.status);
          // Utiliser des données de secours pour les appels d'offre en attente
          setPendingOffers([]);
        }

        // Traiter les données des offres ouvertes en cours
        if (inProgressOffersResponse.ok) {
          const inProgressOffersData = await inProgressOffersResponse.json();
          console.log('In progress offers data:', inProgressOffersData);

          if (inProgressOffersData.offers && inProgressOffersData.offers.length > 0) {
            setInProgressOffers(inProgressOffersData.offers.map((offer: any) => ({
              id: offer.id,
              title: offer.title,
              description: offer.description,
              budget: offer.budget,
              deadline: offer.deadline,
              status: 'in_progress',
              professional: offer.professional || null,
              skills: offer.skills || [],
              category: offer.category || 'Design 3D',
            })));
          } else {
            setInProgressOffers([]);
          }
        } else {
          console.warn('Failed to fetch in progress offers:', inProgressOffersResponse.status);
          setInProgressOffers([]);
        }

        // Traiter les données des offres ouvertes terminées
        if (completedOffersResponse.ok) {
          const completedOffersData = await completedOffersResponse.json();
          console.log('Completed offers data:', completedOffersData);

          if (completedOffersData.offers && completedOffersData.offers.length > 0) {
            setCompletedOffers(completedOffersData.offers.map((offer: any) => ({
              id: offer.id,
              title: offer.title,
              description: offer.description,
              budget: offer.budget,
              deadline: offer.deadline,
              status: 'completed',
              professional: offer.professional || null,
              skills: offer.skills || [],
              category: offer.category || 'Design 3D',
            })));
          } else {
            setCompletedOffers([]);
          }
        } else {
          console.warn('Failed to fetch completed offers:', completedOffersResponse.status);
          setCompletedOffers([]);
        }

        console.log('Projects data:', projectsData);

        // Set profile data
        setProfile(dashboardData.profile);
        localStorage.setItem('userProfile', JSON.stringify(dashboardData.profile));

        // Calculer les statistiques en fonction des statuts des projets
        const inProgressProjects = projectsData.projects?.filter((project: any) => project.status === 'in_progress').length || 0;
        const completedProjects = projectsData.projects?.filter((project: any) => project.status === 'completed').length || 0;

        // Set stats
        setStats({
          activeProjects: dashboardData.stats?.activeProjects || inProgressProjects || 0,
          totalSpent: dashboardData.stats?.totalSpent || '0 €',
          connectedProfessionals: dashboardData.stats?.connectedProfessionals || 0,
          projectsCompleted: dashboardData.stats?.projectsCompleted || completedProjects || 0,
        });

        // Set projects from DashboardProject API
        if (projectsData.projects && projectsData.projects.length > 0) {
          setProjects(projectsData.projects.map((project: any) => ({
            id: project.id,
            title: project.title,
            description: project.description,
            budget: project.budget,
            deadline: project.deadline,
            status: project.status || 'open',
            professional: project.professional || null,
            skills: project.skills || [],
            category: project.category || 'Design 3D',
          })));
        } else {
          // Fallback to dashboard projects if available
          setProjects(dashboardData.projects?.map((project: any) => ({
            id: project.id,
            title: project.title,
            description: project.description,
            budget: project.budget,
            deadline: project.deadline,
            status: project.status,
            professional: project.professional,
          })) || []);
        }

        // Set activities with proper icons
        setActivities(dashboardData.activities.map((activity: any) => {
          // Déterminer l'icône appropriée en fonction du type d'activité
          let icon;
          switch(activity.icon) {
            case 'MessageSquare':
              icon = <MessageSquare className="h-5 w-5" />;
              break;
            case 'CheckCircle':
              icon = <CheckCircle className="h-5 w-5" />;
              break;
            case 'Star':
              icon = <Star className="h-5 w-5" />;
              break;
            case 'DollarSign':
              icon = <DollarSign className="h-5 w-5" />;
              break;
            case 'Users':
              icon = <Users className="h-5 w-5" />;
              break;
            case 'Briefcase':
              icon = <Briefcase className="h-5 w-5" />;
              break;
            default:
              icon = <MessageSquare className="h-5 w-5" />;
          }

          return {
            ...activity,
            icon,
          };
        }));

        // Set recommended professionals
        setRecommendedProfessionals(dashboardData.recommendedProfessionals || []);

        setError(null);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        // En cas d'erreur, utiliser des données statiques
        loadStaticData();
      } finally {
        setLoading(false);
      }
    };

    // Fonction pour charger des données statiques en cas d'erreur
    const loadStaticData = () => {
      // Mock projects détaillés
      const mockDetailedProjects = [
        {
          id: 1,
          title: 'Création d\'un personnage 3D pour jeu vidéo',
          description: 'Modélisation et animation d\'un personnage principal pour un jeu d\'aventure.',
          budget: '1 200 €',
          deadline: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(), // 15 days from now
          status: 'in_progress',
          professional: {
            id: 201,
            name: 'Thomas Martin',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
          },
        },
        {
          id: 2,
          title: 'Animation d\'une scène d\'introduction',
          description: 'Création d\'une animation 3D de 30 secondes pour l\'introduction d\'une application mobile.',
          budget: '800 €',
          deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
          status: 'in_progress',
          professional: {
            id: 202,
            name: 'Sophie Dubois',
            avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
          },
        },
        {
          id: 3,
          title: 'Modélisation d\'objets pour environnement virtuel',
          description: 'Création de 10 objets 3D pour un environnement de réalité virtuelle.',
          budget: '500 €',
          deadline: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
          status: 'completed',
          professional: {
            id: 203,
            name: 'Lucas Bernard',
            avatar: 'https://randomuser.me/api/portraits/men/67.jpg',
          },
        },
        {
          id: 4,
          title: 'Logo 3D pour entreprise',
          description: 'Création d\'un logo 3D animé pour une entreprise de technologie.',
          budget: '700 €',
          deadline: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days ago
          status: 'completed',
          professional: {
            id: 204,
            name: 'Emma Petit',
            avatar: 'https://randomuser.me/api/portraits/women/22.jpg',
          },
        },
        {
          id: 5,
          title: 'Modélisation de produit pour e-commerce',
          description: 'Création de modèles 3D de produits pour un site e-commerce.',
          budget: '900 €',
          deadline: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(), // 20 days ago
          status: 'completed',
          professional: {
            id: 205,
            name: 'Nicolas Roux',
            avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
          },
        },
      ] as ProjectCardProps[];

      // Calculer les statistiques en fonction des statuts des projets
      const inProgressCount = mockDetailedProjects.filter(p => p.status === 'in_progress').length;
      const completedCount = mockDetailedProjects.filter(p => p.status === 'completed').length;

      setStats({
        activeProjects: inProgressCount,
        totalSpent: '3 200 €',
        connectedProfessionals: 5,
        projectsCompleted: completedCount,
      });

      // Définir les projets
      setProjects(mockDetailedProjects);

      // Définir les offres ouvertes en cours
      setInProgressOffers(mockDetailedProjects.filter(project => project.status === 'in_progress'));

      // Définir les offres ouvertes terminées
      setCompletedOffers(mockDetailedProjects.filter(project => project.status === 'completed'));

      // Mock activities
      setActivities([
        {
          id: 1,
          title: 'Nouveau message de Thomas Martin',
          description: 'J\'ai terminé la première version du personnage 3D, vous pouvez la consulter.',
          timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
          icon: <MessageSquare className="h-5 w-5" />,
          user: {
            name: 'Thomas Martin',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
          },
        },
        {
          id: 2,
          title: 'Projet terminé',
          description: 'Lucas Bernard a marqué "Modélisation d\'objets pour environnement virtuel" comme terminé',
          timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
          icon: <CheckCircle className="h-5 w-5" />,
        },
        {
          id: 3,
          title: 'Paiement effectué',
          description: 'Paiement de 500 € pour "Modélisation d\'objets pour environnement virtuel"',
          timestamp: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(), // 4 days ago
          icon: <DollarSign className="h-5 w-5" />,
          iconBackground: 'bg-green-100',
          iconColor: 'text-green-600',
        },
        {
          id: 4,
          title: 'Nouveau professionnel connecté',
          description: 'Sophie Dubois a accepté votre offre pour "Animation d\'une scène d\'introduction"',
          timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
          icon: <Users className="h-5 w-5" />,
          iconBackground: 'bg-blue-100',
          iconColor: 'text-blue-600',
        },
      ]);

      // Mock recommended professionals
      setRecommendedProfessionals([
        {
          id: 301,
          name: 'Julie Moreau',
          title: 'Spécialiste 3D & Animation',
          avatar: 'https://randomuser.me/api/portraits/women/33.jpg',
          rating: 4.9,
          hourlyRate: '45 €/h',
        },
        {
          id: 302,
          name: 'Antoine Leroy',
          title: 'Modeleur 3D & Texturing',
          avatar: 'https://randomuser.me/api/portraits/men/54.jpg',
          rating: 4.7,
          hourlyRate: '40 €/h',
        },
        {
          id: 303,
          name: 'Camille Dupont',
          title: 'Artiste 3D & Rendu',
          avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
          rating: 4.8,
          hourlyRate: '50 €/h',
        },
      ]);

      // Mock pending offers (appels d'offre en attente)
      setPendingOffers([
        {
          id: 101,
          title: 'Création d\'un environnement 3D pour jeu mobile',
          description: 'Nous recherchons un artiste 3D pour créer un environnement complet pour notre jeu mobile d\'aventure.',
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
          filters: {
            location: 'France',
            languages: ['Français', 'Anglais'],
            skills: ['Blender', 'Unity', 'Texturing'],
          },
          maxProposals: 5,
        },
        {
          id: 102,
          title: 'Modélisation de personnages pour série animée',
          description: 'Projet de modélisation de 5 personnages principaux pour une série animée en 3D.',
          createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
          filters: {
            location: 'Europe',
            languages: ['Français'],
            skills: ['Character Design', 'Maya', 'ZBrush'],
          },
          maxProposals: 8,
        },
      ]);

      // Afficher un message d'erreur discret
      setError('Utilisation de données de démonstration - La connexion à la base de données a échoué');
    };

    fetchDashboardData();
  }, [token]);

  const handleProjectClick = (projectId: number) => {
    navigate(`/dashboard/client/offers/${projectId}`);
  };

  const handleViewAllProjects = () => {
    // navigate('/all-projects');
    navigate('/dashboard/projects');
  };

  const handleCreateProject = () => {
    navigate('/dashboard/projects?create=true');
  };

  const handleViewProfessional = (professionalId: number) => {
    navigate(`/artist/${professionalId}`);
  };

  const handleFindProfessionals = () => {
    navigate('/lists-independants');
  };

  const handlePublishOffer = (id: number) => {
    navigate(`/dashboard/client/offers/${id}`);
  };

  const handleEditOffer = (id: number) => {
    navigate(`/dashboard/open-offers/${id}`);
  };

  const handleViewPendingOffers = () => {
    navigate('/dashboard/pending-offers');
  };

  const handleViewOfferStats = () => {
    navigate('/dashboard/offer-stats');
  };

  const handleViewServices = () => {
    navigate('/dashboard/client/services');
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-lg font-semibold text-red-700 mb-2">Erreur</h2>
          <p className="text-red-600">{error}</p>
          <Button
            variant="primary"
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Réessayer
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  // Utiliser le pourcentage de complétion passé en prop au lieu de celui du profil
  const profileData = profile?.profile_data || {};

  return (
    <DashboardLayout
      title={`Bonjour, ${user.first_name || 'Client'}`}
      subtitle="Bienvenue sur votre tableau de bord"
      actions={
        <div style={{ display: 'flex', gap: '0.75rem' }}>
          <Button
            variant="outline"
            leftIcon={<Bell className="h-5 w-5" />}
            onClick={() => console.log('Notifications')}
          >
            Notifications
          </Button>
          <Button
            variant="outline"
            leftIcon={<Briefcase className="h-5 w-5" />}
            onClick={handleViewServices}
          >
            Explorer les Services
          </Button>
          <Button
            variant="primary"
            leftIcon={<Plus className="h-5 w-5" />}
            onClick={handleCreateProject}
            style={{ backgroundColor: '#2980b9', color: 'white', padding: '0.75rem 1.5rem', fontWeight: 'bold', borderRadius: '0.5rem' }}
          >
            Créer une offre
          </Button>
        </div>
      }
    >
      {/* New Unified Profile Link */}
      <UnifiedProfileLink />

      {/* Profile completion alert if needed */}
      {completionPercentage < 100 && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6 flex items-center justify-between">
          <div className="flex items-center">
            <Bell className="h-5 w-5 text-amber-500 mr-3" />
            <div>
              <p className="text-amber-800 font-medium">Votre profil est incomplet</p>
              <p className="text-amber-700 text-sm">Complétez votre profil pour une meilleure expérience sur la plateforme.</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => navigate('/dashboard/client-profile/edit')}
              style={{ padding: '0.5rem 1rem', fontWeight: 'bold', borderColor: '#2980b9', color: '#2980b9' }}
            >
              Compléter mon profil
            </Button>
            <Button
              variant="primary"
              onClick={() => navigate('/dashboard/client-profile-dashboard')}
              style={{ backgroundColor: '#2980b9', color: 'white', padding: '0.5rem 1rem', fontWeight: 'bold', borderRadius: '0.5rem' }}
            >
              Voir mon profil
            </Button>
          </div>
        </div>
      )}

      {/* Stats row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <StatCard
          title="Offres ouvertes actives"
          value={stats.activeProjects}
          icon={<Briefcase className="h-6 w-6" />}
        />
        <StatCard
          title="Budget total dépensé"
          value={stats.totalSpent}
          icon={<DollarSign className="h-6 w-6" />}
        />
        <StatCard
          title="Professionnels connectés"
          value={stats.connectedProfessionals}
          icon={<Users className="h-6 w-6" />}
        />
        <StatCard
          title="Offres ouvertes terminées"
          value={stats.projectsCompleted}
          icon={<CheckCircle className="h-6 w-6" />}
        />
      </div>

      {/* Offres ouvertes */}
      {pendingOffers && pendingOffers.length > 0 && (
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-neutral-900">Offres ouvertes</h2>
            <Button
              variant="outline"
              size="sm"
              // onClick={() => navigate('/dashboard/open-offers')}
              onClick={handleViewAllProjects}
            >
              Voir tous
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {pendingOffers.slice(0, 2).map(offer => (
              <PendingOfferCard
                key={offer.id}
                {...offer}
                onPublish={handlePublishOffer}
                onEdit={handleEditOffer}
              />
            ))}
          </div>
        </div>
      )}

      {/* Main content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Projects section */}
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-neutral-900">Offres ouvertes en cours</h3>
              <Button
                variant="ghost"
                size="sm"
                // onClick={() => navigate('/dashboard/open-offers')}
                onClick={handleViewAllProjects}
              >
                Voir tous
              </Button>
            </div>

            <div className="p-6 grid grid-cols-1 gap-6">
              {inProgressOffers.length === 0 ? (
                <div className="text-center py-8">
                  <Briefcase className="h-12 w-12 text-neutral-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-neutral-700 mb-1">Aucune offre ouverte en cours</h3>
                  <p className="text-neutral-500 mb-4">Créez votre première offre pour trouver des professionnels qualifiés.</p>
                  <Button
                    variant="primary"
                    onClick={handleCreateProject}
                    style={{ backgroundColor: '#2980b9', color: 'white', padding: '0.75rem 1.5rem', fontWeight: 'bold', borderRadius: '0.5rem' }}
                  >
                    Créer une offre
                  </Button>
                </div>
              ) : (
                inProgressOffers.map(project => (
                  <ProjectCard
                    key={project.id}
                    {...project}
                    onClick={() => handleProjectClick(project.id)}
                  />
                ))
              )}
            </div>
          </div>

          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-neutral-900">Offres ouvertes terminées</h3>
              <Button
                variant="ghost"
                size="sm"
                // onClick={() => navigate('/dashboard/open-offers')}
                onClick={handleViewAllProjects}
              >
                Voir tous
              </Button>
            </div>

            <div className="p-6 grid grid-cols-1 gap-6">
              {completedOffers.length === 0 ? (
                <div className="text-center py-6 text-neutral-500">
                  Aucune offre ouverte terminée
                </div>
              ) : (
                completedOffers
                  .slice(0, 2) // Show only the first 2 completed projects
                  .map(project => (
                    <ProjectCard
                      key={project.id}
                      {...project}
                      onClick={() => handleProjectClick(project.id)}
                    />
                  ))
              )}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Activity feed */}
          <ActivityFeed
            activities={activities}
            maxItems={5}
            showViewAll
            onViewAll={() => navigate('/all-activities')}
          />

          {/* Recommended professionals */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-neutral-900">Professionnels recommandés</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/lists-independants')}
              >
                Voir tous
              </Button>
            </div>

            <div className="divide-y divide-neutral-200">
              {recommendedProfessionals.map(professional => (
                <div
                  key={professional.id}
                  className="p-4 hover:bg-neutral-50 cursor-pointer"
                  onClick={() => handleViewProfessional(professional.id)}
                >
                  <div className="flex items-center">
                    <Avatar
                      src={professional.avatar}
                      fallback={professional.name.charAt(0)}
                      size="md"
                      className="mr-3"
                    />
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-neutral-900 truncate">
                        {professional.name}
                      </h4>
                      <p className="text-xs text-neutral-500 truncate">
                        {professional.title}
                      </p>
                      <div className="flex items-center mt-1">
                        <div className="flex items-center text-yellow-500 mr-2">
                          ★ <span className="ml-1 text-xs text-neutral-700">{professional.rating}</span>
                        </div>
                        <div className="text-xs text-neutral-700">
                          {professional.hourlyRate}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="p-4 border-t border-neutral-200 bg-neutral-50">
              <Button
                variant="outline"
                fullWidth
                leftIcon={<Search className="h-4 w-4" />}
                onClick={handleFindProfessionals}
              >
                Trouver des professionnels
              </Button>
            </div>
          </div>

          {/* Quick actions */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h3 className="text-lg font-semibold text-neutral-900">Actions rapides</h3>
            </div>

            <div className="p-4 space-y-3">
              <Button
                variant="primary"
                fullWidth
                leftIcon={<Plus className="h-4 w-4" />}
                onClick={handleCreateProject}
                style={{ backgroundColor: '#2980b9', color: 'white', fontWeight: 'bold' }}
              >
                Créer une nouvelle offre
              </Button>

              <Button
                variant="outline"
                fullWidth
                leftIcon={<MessageSquare className="h-4 w-4" />}
                onClick={() => navigate(`/discussions/${user.id}`)}
              >
                Voir mes messages
              </Button>

              <Button
                variant="outline"
                fullWidth
                leftIcon={<FileText className="h-4 w-4" />}
                onClick={handleViewOfferStats}
              >
                Statistiques des appels d'offre
              </Button>

              <Button
                variant="outline"
                fullWidth
                leftIcon={<Briefcase className="h-4 w-4" />}
                onClick={handleViewServices}
              >
                Explorer les services
              </Button>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ClientDashboard;
