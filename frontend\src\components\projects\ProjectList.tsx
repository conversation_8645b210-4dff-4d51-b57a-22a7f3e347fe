import React, { useState } from 'react';
import { Search, Filter, Plus, Briefcase } from 'lucide-react';
import Button from '../ui/Button';
import ProjectCard from '../dashboard/ProjectCard';
import Badge from '../ui/Badge';

export interface ProjectListProps {
  projects: any[];
  isLoading?: boolean;
  error?: string;
  onProjectClick: (projectId: number) => void;
  onCreateProject?: () => void;
  onFilterChange?: (filters: any) => void;
  onSearchChange?: (query: string) => void;
  isProfessional?: boolean;
  emptyMessage?: string;
}

const ProjectList: React.FC<ProjectListProps> = ({
  projects,
  isLoading = false,
  error,
  onProjectClick,
  onCreateProject,
  onFilterChange,
  onSearchChange,
  isProfessional = false,
  emptyMessage = 'Aucun projet trouvé',
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: 'all',
    category: 'all',
    budget: 'all',
    sortBy: 'newest',
  });

  // Available filter options
  const statusOptions = [
    { value: 'all', label: 'Tous les statuts' },
    { value: 'open', label: 'Ouverts' },
    { value: 'in_progress', label: 'En cours' },
    { value: 'completed', label: 'Terminés' },
    { value: 'cancelled', label: 'Annulés' },
  ];

  const categoryOptions = [
    { value: 'all', label: 'Toutes les catégories' },
    { value: 'Modélisation 3D', label: 'Modélisation 3D' },
    { value: 'Animation 3D', label: 'Animation 3D' },
    { value: 'Rendu 3D', label: 'Rendu 3D' },
    { value: 'Texturing et Shading', label: 'Texturing et Shading' },
    { value: 'Effets visuels (VFX)', label: 'Effets visuels (VFX)' },
    { value: 'Conception de personnages 3D', label: 'Conception de personnages 3D' },
    { value: 'Environnements 3D', label: 'Environnements 3D' },
  ];

  const budgetOptions = [
    { value: 'all', label: 'Tous les budgets' },
    { value: 'under_500', label: 'Moins de 500 €' },
    { value: '500_1000', label: '500 € - 1 000 €' },
    { value: '1000_5000', label: '1 000 € - 5 000 €' },
    { value: 'over_5000', label: 'Plus de 5 000 €' },
  ];

  const sortOptions = [
    { value: 'newest', label: 'Plus récents' },
    { value: 'oldest', label: 'Plus anciens' },
    { value: 'budget_high', label: 'Budget (décroissant)' },
    { value: 'budget_low', label: 'Budget (croissant)' },
    { value: 'deadline', label: 'Date limite' },
  ];

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    
    if (onSearchChange) {
      onSearchChange(query);
    }
  };

  // Handle filter change
  const handleFilterChange = (name: string, value: string) => {
    const newFilters = {
      ...filters,
      [name]: value,
    };
    
    setFilters(newFilters);
    
    if (onFilterChange) {
      onFilterChange(newFilters);
    }
  };

  // Reset filters
  const handleResetFilters = () => {
    const defaultFilters = {
      status: 'all',
      category: 'all',
      budget: 'all',
      sortBy: 'newest',
    };
    
    setFilters(defaultFilters);
    
    if (onFilterChange) {
      onFilterChange(defaultFilters);
    }
  };

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
        <div className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search Input */}
            <div className="flex-1 relative">
              <input
                type="text"
                placeholder="Rechercher des projets..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-neutral-400" />
            </div>
            
            {/* Filter Toggle Button */}
            <Button
              variant="outline"
              leftIcon={<Filter className="h-5 w-5" />}
              onClick={() => setShowFilters(!showFilters)}
            >
              Filtres
            </Button>
            
            {/* Create Project Button (for clients) */}
            {!isProfessional && onCreateProject && (
              <Button
                variant="primary"
                leftIcon={<Plus className="h-5 w-5" />}
                onClick={onCreateProject}
              >
                Créer un projet
              </Button>
            )}
          </div>
          
          {/* Filters Panel */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-neutral-200">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Status Filter */}
                <div>
                  <label htmlFor="status-filter" className="block text-sm font-medium text-neutral-700 mb-1">
                    Statut
                  </label>
                  <select
                    id="status-filter"
                    value={filters.status}
                    onChange={(e) => handleFilterChange('status', e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    {statusOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                {/* Category Filter */}
                <div>
                  <label htmlFor="category-filter" className="block text-sm font-medium text-neutral-700 mb-1">
                    Catégorie
                  </label>
                  <select
                    id="category-filter"
                    value={filters.category}
                    onChange={(e) => handleFilterChange('category', e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    {categoryOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                {/* Budget Filter */}
                <div>
                  <label htmlFor="budget-filter" className="block text-sm font-medium text-neutral-700 mb-1">
                    Budget
                  </label>
                  <select
                    id="budget-filter"
                    value={filters.budget}
                    onChange={(e) => handleFilterChange('budget', e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    {budgetOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                {/* Sort By */}
                <div>
                  <label htmlFor="sort-by" className="block text-sm font-medium text-neutral-700 mb-1">
                    Trier par
                  </label>
                  <select
                    id="sort-by"
                    value={filters.sortBy}
                    onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    {sortOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              {/* Filter Actions */}
              <div className="flex justify-end mt-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleResetFilters}
                >
                  Réinitialiser les filtres
                </Button>
              </div>
            </div>
          )}
        </div>
        
        {/* Active Filters */}
        {(filters.status !== 'all' || filters.category !== 'all' || filters.budget !== 'all') && (
          <div className="px-4 py-2 bg-neutral-50 border-t border-neutral-200 flex flex-wrap items-center gap-2">
            <span className="text-sm text-neutral-600">Filtres actifs:</span>
            
            {filters.status !== 'all' && (
              <Badge color="primary" size="sm">
                {statusOptions.find(o => o.value === filters.status)?.label}
                <button
                  className="ml-1 text-primary-700 hover:text-primary-900"
                  onClick={() => handleFilterChange('status', 'all')}
                >
                  ×
                </button>
              </Badge>
            )}
            
            {filters.category !== 'all' && (
              <Badge color="primary" size="sm">
                {categoryOptions.find(o => o.value === filters.category)?.label}
                <button
                  className="ml-1 text-primary-700 hover:text-primary-900"
                  onClick={() => handleFilterChange('category', 'all')}
                >
                  ×
                </button>
              </Badge>
            )}
            
            {filters.budget !== 'all' && (
              <Badge color="primary" size="sm">
                {budgetOptions.find(o => o.value === filters.budget)?.label}
                <button
                  className="ml-1 text-primary-700 hover:text-primary-900"
                  onClick={() => handleFilterChange('budget', 'all')}
                >
                  ×
                </button>
              </Badge>
            )}
          </div>
        )}
      </div>
      
      {/* Projects List */}
      <div className="space-y-6">
        {isLoading ? (
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-12 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-neutral-600">Chargement des projets...</p>
          </div>
        ) : error ? (
          <div className="bg-white rounded-lg border border-red-200 shadow-sm p-8 text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button
              variant="primary"
              onClick={() => window.location.reload()}
            >
              Réessayer
            </Button>
          </div>
        ) : projects.length === 0 ? (
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-12 text-center">
            <Briefcase className="h-12 w-12 text-neutral-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-neutral-700 mb-1">{emptyMessage}</h3>
            {!isProfessional && onCreateProject && (
              <div className="mt-4">
                <Button
                  variant="primary"
                  onClick={onCreateProject}
                >
                  Créer un projet
                </Button>
              </div>
            )}
          </div>
        ) : (
          projects.map((project) => (
            <ProjectCard
              key={project.id}
              {...project}
              onClick={() => onProjectClick(project.id)}
            />
          ))
        )}
      </div>
    </div>
  );
};

export default ProjectList;
