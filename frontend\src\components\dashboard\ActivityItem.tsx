import React, { ReactNode } from 'react';
import Avatar from '../ui/Avatar';

export interface ActivityItemProps {
  id: string | number;
  title: React.ReactNode;
  description?: React.ReactNode;
  timestamp: string;
  icon?: ReactNode;
  iconBackground?: string;
  iconColor?: string;
  user?: {
    name: string;
    avatar?: string;
  };
  onClick?: () => void;
}

const ActivityItem: React.FC<ActivityItemProps> = ({
  title,
  description,
  timestamp,
  icon,
  iconBackground = 'bg-primary-100',
  iconColor = 'text-primary-600',
  user,
  onClick,
}) => {
  // Format timestamp
  const formatTime = (dateString: string) => {
    if (!dateString) return "";

  const date = new Date(dateString);
  return date.toLocaleString("fr-FR", {
    dateStyle: "medium", // ex: 12 mai 2025
    timeStyle: "short"   // ex: 10:41
  });
    // const date = new Date(dateString);
    // const now = new Date();
    // const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    // if (diffInSeconds < 60) {
    //   return 'À l\'instant';
    // }
    
    // const diffInMinutes = Math.floor(diffInSeconds / 60);
    // if (diffInMinutes < 60) {
    //   return `Il y a ${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''}`;
    // }
    
    // const diffInHours = Math.floor(diffInMinutes / 60);
    // if (diffInHours < 24) {
    //   return `Il y a ${diffInHours} heure${diffInHours > 1 ? 's' : ''}`;
    // }
    
    // const diffInDays = Math.floor(diffInHours / 24);
    // if (diffInDays < 7) {
    //   return `Il y a ${diffInDays} jour${diffInDays > 1 ? 's' : ''}`;
    // }
    
    // return date.toLocaleDateString('fr-FR', {
    //   day: 'numeric',
    //   month: 'short',
    //   year: 'numeric',
    // });
  };

  return (
    <div 
      className={`flex items-start p-4 ${onClick ? 'cursor-pointer hover:bg-neutral-50' : ''}`}
      onClick={onClick}
    >
      {icon ? (
        <div className={`flex-shrink-0 mr-4 ${iconBackground} ${iconColor} p-2 rounded-full`}>
          {icon}
        </div>
      ) : user ? (
        <div className="flex-shrink-0 mr-4">
          <Avatar
            src={user.avatar}
            fallback={user.name.charAt(0)}
            alt={user.name}
            size="md"
          />
        </div>
      ) : null}
      
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium text-neutral-900">{title}</div>
        {description && (
          <div className="mt-1 text-sm text-neutral-600">{description}</div>
        )}
        <div className="mt-1 text-xs text-neutral-500">{formatTime(timestamp)}</div>
      </div>
    </div>
  );
};

export default ActivityItem;
