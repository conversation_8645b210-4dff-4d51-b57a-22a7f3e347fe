import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronDown, User, Menu, Globe, Settings, BarChart, LogOut, Bell } from 'lucide-react';
import ProfileWizard from './ProfileWizard';
import Button from './ui/Button';
import Avatar from './ui/Avatar';
import Dropdown from './ui/Dropdown';
import MobileMenu from './ui/MobileMenu';
import Container from './layout/Container';
import NotificationDropdown from './notifications/NotificationDropdown';
import { useNotifications } from './notifications/NotificationContext';

// Define types for navigation links
interface NavLink {
  label: string;
  href: string;
  children?: Array<{ label: string; href: string }>;
}

function Header() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Get notifications from context
  const { notifications, markAsRead, markAllAsRead, loading, error } = useNotifications();

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');
  
  // Afficher les infos user dans la console
  console.log('User object:', user);
  console.log('user.is_professional:', user.is_professional);
  
  // Check if user is authenticated (has token and user data)
  const isAuthenticated = !!token && !!user && !!user.id;
  const navigate = useNavigate();

  // Navigation links
  const navLinks: NavLink[] = [
    { label: 'Qui sommes nous ?', href: '/about' },
    { label: 'Nos offres', href: '/offers' },
    { label: 'Explorer', href: '/explore' },
  ];

  // User dropdown items
  const userDropdownItems = [
    {
      label: `${user.first_name || ''} ${user.last_name || ''}`,
      href: '/dashboard/profile',
      divider: true
    },
    {
      label: 'Tableau de bord',
      href: '/dashboard',
      icon: <BarChart className="w-4 h-4" />
    },
    {
      label: 'Mon profil',
      href: user.is_professional !== false ? '/dashboard/profile' : '/dashboard/client-profile',
      icon: <User className="w-4 h-4" />
    },
    {
      label: 'Paramètres',
      href: '/settings',
      icon: <Settings className="w-4 h-4" />
    },
    {
      label: 'Statistiques',
      href: '/stats',
      icon: <BarChart className="w-4 h-4" />
    },
    {
      label: 'Se déconnecter',
      onClick: handleLogout,
      icon: <LogOut className="w-4 h-4" />,
      divider: true
    },
  ];

  // Language dropdown items
  const languageDropdownItems = [
    { label: 'Français', onClick: () => {} },
    { label: 'English', onClick: () => {} },
  ];

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  function handleRegisterClick() {
    navigate('/register');
  }

  function handleLoginClick() {
    navigate('/login');
  }

  function handleLogout() {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    localStorage.removeItem('userProfile');
    navigate('/');
  }

  function handleHomeClick() {
    if (isAuthenticated) {
      navigate('/dashboard');
    } else {
      navigate('/');
    }
  }

  return (
    <header className={`sticky top-0 z-40 w-full transition-all duration-200 ${isScrolled ? 'bg-white shadow-md' : 'bg-white border-b'}`}>
      <Container>
        <div className="flex justify-between items-center h-16">
          {/* Logo and Navigation */}
          <div className="flex items-center space-x-8">
            <div
              className="text-xl font-bold cursor-pointer text-primary-700"
              onClick={handleHomeClick}
            >
              Hi 3D Artiste
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex space-x-6">
              {navLinks.map((link, index) => (
                <div key={index} className="relative">
                  {link.children ? (
                    <Dropdown
                      trigger={
                        <button type="button" className="text-neutral-600 hover:text-neutral-900 flex items-center">
                          {link.label}
                          <ChevronDown className="ml-1 h-4 w-4" />
                        </button>
                      }
                      items={link.children.map(child => ({
                        label: child.label,
                        href: child.href
                      }))}
                    />
                  ) : (
                    <a
                      href={link.href}
                      className="text-neutral-600 hover:text-neutral-900"
                    >
                      {link.label}
                    </a>
                  )}
                </div>
              ))}
            </nav>
          </div>

          {/* Right side: Auth buttons or User menu */}
          <div className="flex items-center space-x-4">
            {isAuthenticated ? (
              <>
                {/* Notification Dropdown */}
                <NotificationDropdown
                  notifications={notifications}
                  onMarkAsRead={markAsRead}
                  onMarkAllAsRead={markAllAsRead}
                  loading={loading}
                  error={error}
                />

                {/* User Dropdown */}
                <Dropdown
                  trigger={
                    <div className="flex items-center space-x-2 text-neutral-600 cursor-pointer">
                      <Avatar
                        size="sm"
                        fallback={`${user.first_name?.[0]}${user.last_name?.[0]}`}
                      />
                      <span className="hidden sm:inline">{user.first_name} {user.last_name}</span>
                      <ChevronDown className="w-4 h-4" />
                    </div>
                  }
                  items={userDropdownItems}
                  align="right"
                />
              </>
            ) : (
              <div className="hidden sm:flex space-x-3">
                <button
                  type="button"
                  onClick={handleLoginClick}
                  className="px-3 py-1.5 text-sm rounded-full border border-primary-600 text-primary-600 hover:bg-primary-50 inline-flex items-center justify-center font-medium transition-colors"
                >
                  Se connecter
                </button>
                <button
                  type="button"
                  onClick={handleRegisterClick}
                  className="px-3 py-1.5 text-sm rounded-full bg-primary-600 text-white hover:bg-primary-700 inline-flex items-center justify-center font-medium transition-colors"
                  style={{ backgroundColor: '#2980b9', color: 'white' }}
                >
                  Créer un compte
                </button>
              </div>
            )}

            {/* Language Selector */}
            <Dropdown
              trigger={
                <div className="flex items-center text-neutral-600 cursor-pointer">
                  <Globe className="w-5 h-5 sm:mr-1" />
                  <span className="hidden sm:inline">Français</span>
                  <ChevronDown className="ml-1 h-4 w-4" />
                </div>
              }
              items={languageDropdownItems}
              align="right"
              width="w-32"
            />

            {/* Mobile Menu Button */}
            <button
              type="button"
              className="md:hidden p-2 rounded-md hover:bg-neutral-100"
              onClick={() => setIsMobileMenuOpen(true)}
              aria-label="Ouvrir le menu mobile"
            >
              <Menu className="h-6 w-6" />
            </button>
          </div>
        </div>
      </Container>

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        links={navLinks}
        authButtons={
          !isAuthenticated ? (
            <div className="flex flex-col space-y-3">
              <button
                type="button"
                onClick={handleLoginClick}
                className="w-full px-4 py-2 rounded-full border border-primary-600 text-primary-600 hover:bg-primary-50 inline-flex items-center justify-center font-medium transition-colors"
              >
                Se connecter
              </button>
              <button
                type="button"
                onClick={handleRegisterClick}
                className="w-full px-4 py-2 rounded-full bg-primary-600 text-white hover:bg-primary-700 inline-flex items-center justify-center font-medium transition-colors"
                style={{ backgroundColor: '#2980b9', color: 'white' }}
              >
                Créer un compte
              </button>
            </div>
          ) : null
        }
      />

      {/* Profile Wizard Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-3/4 max-w-lg">
            <button
              type="button"
              className="absolute top-3 right-3 text-neutral-500 hover:text-neutral-800"
              onClick={() => setIsModalOpen(false)}
              aria-label="Fermer"
            >
              ✖
            </button>
            <ProfileWizard />
          </div>
        </div>
      )}
    </header>
  );
}

export default Header;