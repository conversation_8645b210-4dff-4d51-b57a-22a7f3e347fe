import React, { useState } from 'react';
import { Plus, X } from 'lucide-react';
import type { ProfileFormData } from '../types';

interface ServicesFormProps {
  data: ProfileFormData;
  onChange: (data: Partial<ProfileFormData>) => void;
}

const RATE_UNITS = ['hour', 'day', 'project'] as const;

export default function ServicesForm({ data, onChange }: ServicesFormProps) {
  const [newService, setNewService] = useState({
    title: '',
    description: '',
    rate: 0,
    rateUnit: 'hour' as typeof RATE_UNITS[number],
  });

  // const handleAddService = () => {
  //   if (newService.title && newService.rate > 0) {
  //     onChange({
  //       services: [...data.services, newService],
  //     });
  //     setNewService({
  //       title: '',
  //       description: '',
  //       rate: 0,
  //       rateUnit: 'hour',
  //     });
  //   }
  // };

  const handleRemoveService = (index: number) => {
    onChange({
      services: data.services.filter((_, i) => i !== index),
    });
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">Offres de service</h3>
        <div className="space-y-4">
          <input
            type="text"
            placeholder="Titre du service"
            value={newService.title}
            onChange={(e) => setNewService({ ...newService, title: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <textarea
            placeholder="Description du service"
            value={newService.description}
            onChange={(e) => setNewService({ ...newService, description: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 h-24"
          />
          <div className="flex gap-4">
            <div className="flex-1">
              <input
                type="number"
                placeholder="Tarif"
                value={newService.rate || ''}
                onChange={(e) => setNewService({ ...newService, rate: parseFloat(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <select
              value={newService.rateUnit}
              onChange={(e) => setNewService({ ...newService, rateUnit: e.target.value as typeof RATE_UNITS[number] })}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="hour">Par heure</option>
              <option value="day">Par jour</option>
              <option value="project">Par projet</option>
            </select>
            <button
              // onClick={handleAddService}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* <div className="mt-4 space-y-4">
          {data.services.map((service, index) => (
            <div key={index} className="p-4 bg-gray-50 rounded-lg relative">
              <button
                onClick={() => handleRemoveService(index)}
                className="absolute top-2 right-2 p-1 text-gray-500 hover:text-red-500"
              >
                <X className="w-4 h-4" />
              </button>
              <h4 className="font-semibold">{service.title}</h4>
              <p className="text-gray-700 mt-1">{service.description}</p>
              <p className="text-sm font-medium text-blue-600 mt-2">
                {service.rate}€ / {service.rateUnit === 'hour' ? 'heure' : service.rateUnit === 'day' ? 'jour' : 'projet'}
              </p>
            </div>
          ))}
        </div> */}
      </div>
    </div>
  );
}