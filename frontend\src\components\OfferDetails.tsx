// import React, { useState, useEffect } from 'react';
// import { useParams, useNavigate } from 'react-router-dom';
// import { API_BASE_URL } from '../config';
// import NavBarProfil from './NavBarProfil';
// import Footer from './Footer';

// const OfferDetails = () => {
//     const { id } = useParams();
//     const navigate = useNavigate();
//     const user = JSON.parse(localStorage.getItem("user") || '{}');

//     const [offer, setOffer] = useState({
//         id: null,
//         title: "",
//         company: "",
//         budget: "",
//         deadline: "",
//         description: "",
//         website: "",
//         categories: "[]",
//         status: "",
//         views_count: 0,
//         applications: [],
//     });
//     const [loading, setLoading] = useState(true);
//     const [error, setError] = useState<string | null>(null);
//     const [submitError, setSubmitError] = useState<string | null>(null);

//     useEffect(() => {
//         const fetchOfferDetails = async () => {
//             const token = localStorage.getItem("token");
//             try {
//                 const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}`, {
//                     method: 'GET',
//                     headers: {
//                         Authorization: `Bearer ${token}`,
//                         'Content-Type': 'application/json',
//                     },
//                 });
//                 if (!response.ok) {
//                     throw new Error(`Erreur HTTP : ${response.status}`);
//                 }
//                 const data = await response.json();
//                 setOffer(data.open_offer);
//             } catch (err) {
//                 if (err instanceof Error) {
//                     setError(err.message);
//                 } else {
//                     setError("Une erreur inconnue est survenue.");
//                 }
//             } finally {
//                 setLoading(false);
//             }
//         };

//         fetchOfferDetails();
//     }, [id]);

//     const handleApply = async () => {
//         setSubmitError(null);
//         const token = localStorage.getItem("token");

//         try {
//             const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}/apply`, {
//                 method: 'POST',
//                 headers: {
//                     Authorization: `Bearer ${token}`,
//                     'Content-Type': 'application/json',
//                 },
//             });

//             if (!response.ok) {
//                 const errorData = await response.json();
//                 throw new Error(errorData.message || 'Une erreur est survenue');
//             }

//             navigate('/pro/offres');
//         } catch (err) {
//             if (err instanceof Error) {
//                 setSubmitError(err.message);
//             } else {
//                 setSubmitError("Une erreur inconnue est survenue.");
//             }
//         }
//     };

//     const getStatusBadge = (status: string) => {
//         switch (status) {
//             case 'pending':
//                 return <span className="px-3 py-1 rounded-full text-white bg-yellow-500">En attente</span>;
//             case 'accepted':
//                 return <span className="px-3 py-1 rounded-full text-white bg-green-500">Accepté</span>;
//             case 'rejected':
//                 return <span className="px-3 py-1 rounded-full text-white bg-red-500">Refusé</span>;
//             default:
//                 return <span className="px-3 py-1 rounded-full text-white bg-gray-500">Inconnu</span>;
//         }
//     };

//     const hasAlreadyApplied = () => {
//         return offer.applications.some((application: any) => application.freelance_profile?.user?.id === user.id);
//     };

//     if (loading) return <p className="text-center text-gray-600 text-lg font-semibold">Chargement...</p>;
//     if (error) return <p className="text-center text-red-500 text-lg font-semibold">Erreur : {error}</p>;
//     if (!offer.id) return <p className="text-center text-gray-600 text-lg font-semibold">Aucune offre trouvée.</p>;

//     return (
//         <div className="flex flex-col min-h-screen">
//             <NavBarProfil />
//             <div className="container mx-auto p-8 bg-white rounded-lg shadow-lg max-w-4xl flex-grow">
//                 <h1 className="text-3xl font-extrabold mb-6 text-blue-600">{offer.title}</h1>
//                 <div className="mb-4">
//                     <p className="text-gray-700 text-lg"><strong>Entreprise :</strong> {offer.company}</p>
//                     <p className="text-gray-700 text-lg"><strong>Budget :</strong> {offer.budget || 'Non spécifié'} €</p>
//                     <p className="text-gray-700 text-lg"><strong>Deadline :</strong> {offer.deadline ? new Date(offer.deadline).toLocaleDateString() : 'Non défini'}</p>
//                     <p className="text-gray-700 text-lg"><strong>Description :</strong> {offer.description}</p>
//                 </div>
//                 <div className="mb-4 p-4 bg-gray-100 rounded-md">
//                     <p className="text-gray-700 text-lg"><strong>Site Web :</strong> {offer.website ? <a href={offer.website} target="_blank" rel="noopener noreferrer" className="text-blue-500 underline">{offer.website}</a> : 'Aucun'}</p>
//                     <p className="text-gray-700 text-lg"><strong>Catégories :</strong> {offer.categories ? JSON.parse(offer.categories).join(', ') : 'Aucune'}</p>
//                     <p className="text-gray-700 text-lg"><strong>Statut :</strong> {offer.status}</p>
//                     <p className="text-gray-700 text-lg"><strong>Nombre de vues :</strong> {offer.views_count}</p>
//                 </div>

//                 {user?.is_professional === false ? (
//                     <>
//                         <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">Pros invités</h2>
//                         {offer.applications.length > 0 ? (
//                             <ul className="list-none p-0">
//                                 {offer.applications.map((application: any, index: number) => (
//                                     <li key={index} className="mb-3 p-3 bg-white shadow rounded-md">
//                                         <div className="flex justify-between items-center">
//                                             <div>
//                                                 <p className="text-gray-800 font-semibold">
//                                                     {application.freelance_profile.first_name} {application.freelance_profile.last_name}
//                                                 </p>
//                                                 <p className="text-sm text-gray-600">{application.freelance_profile.city}, {application.freelance_profile.country}</p>
//                                                 <p className="text-sm text-gray-600">Compétences : {application.freelance_profile.skills.join(', ')}</p>
//                                                 <p className="text-sm text-gray-600">Langues : {application.freelance_profile.languages.join(', ')}</p>
//                                             </div>
//                                             <div className="flex flex-col items-end gap-2">
//                                                 {getStatusBadge(application.status)}
//                                                 <button
//                                                     className="text-blue-600 text-sm underline hover:text-blue-800"
//                                                     onClick={() => navigate(`/discussions/${offer.id}`)}
//                                                 >
//                                                     Discuter
//                                                 </button>
//                                             </div>
//                                         </div>
//                                     </li>
//                                 ))}
//                             </ul>
//                         ) : (
//                             <p className="text-gray-600 text-lg">Aucun Pro invité.</p>
//                         )}
//                     </>
//                 ) : (
//                     offer.status === 'open' && (
//                         <div className="flex flex-col gap-4 mt-6">
//                             {submitError && <p className="text-red-500 font-medium">❌ {submitError}</p>}
//                             {hasAlreadyApplied() ? (
//                                 <p className="text-green-700 font-semibold">✅ Vous êtes déjà intéressé par cette offre.</p>
//                             ) : (
//                                 <div className="flex gap-4">
//                                     <button
//                                         onClick={handleApply}
//                                         className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
//                                     >
//                                         Intéressé
//                                     </button>
//                                     <button className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
//                                         Refuser
//                                     </button>
//                                 </div>
//                             )}
//                         </div>
//                     )
//                 )}
//             </div>
//             <Footer />
//         </div>
//     );
// };

// export default OfferDetails;

import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { API_BASE_URL } from "../config";
import NavBarProfil from "./NavBarProfil";
import Footer from "./Footer";

const OfferDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const user = JSON.parse(localStorage.getItem("user") || "{}");

  const [offer, setOffer] = useState({
    id: null,
    title: "",
    company: "",
    budget: "",
    deadline: "",
    description: "",
    website: "",
    categories: [],
    status: "",
    views_count: 0,
    applications: [],
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [professionals, setProfessionals] = useState<any[]>([]);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [selectedProfessionalId, setSelectedProfessionalId] =
    useState<string>("");

  useEffect(() => {
    const fetchOfferDetails = async () => {
      const token = localStorage.getItem("token");
      try {
        const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}`, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        });
        if (!response.ok) {
          throw new Error(`Erreur HTTP : ${response.status}`);
        }
        const data = await response.json();
        setOffer(data.open_offer);
      } catch (err) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError("Une erreur inconnue est survenue.");
        }
      } finally {
        setLoading(false);
      }
    };

    fetchOfferDetails();
  }, [id]);

  // const fetchProfessionals = async () => {
  //     const token = localStorage.getItem("token");
  //     try {
  //         const response = await fetch(`${API_BASE_URL}/api/users`, {
  //             method: 'GET',
  //             headers: {
  //                 Authorization: `Bearer ${token}`,
  //                 'Content-Type': 'application/json',
  //             },
  //         });
  //         const data = await response.json();
  //         setProfessionals(data);
  //     } catch (err) {
  //         console.error("Erreur lors du chargement des professionnels", err);
  //     }
  // };

  const fetchProfessionals = async () => {
    const token = localStorage.getItem("token");
    try {
      const response = await fetch(`${API_BASE_URL}/api/users`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });
      const data = await response.json();
      setProfessionals(Array.isArray(data.users) ? data.users : []);
    } catch (err) {
      console.error("Erreur lors du chargement des professionnels", err);
      setProfessionals([]); // fail-safe
    }
  };

  const handleInvite = async () => {
    const token = localStorage.getItem("token");
    console.error("Id Pro sélectionné :", `${id}`);
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/open-offers/${id}/invite`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ professional_id: selectedProfessionalId }),
        }
      );
      console.error(
        "Donné envoyée :",
        JSON.stringify({ professional_id: selectedProfessionalId })
      );
      if (!response.ok) throw new Error("Erreur lors de l'invitation");
      alert("Invitation envoyée avec succès");
      setShowInviteModal(false);
    } catch (error) {
      alert("Échec de l'envoi de l'invitation");
    }
  };

  const openInviteModal = () => {
    fetchProfessionals();
    setShowInviteModal(true);
  };

  const handleApply = async () => {
    setSubmitError(null);
    const token = localStorage.getItem("token");

    try {
      const response = await fetch(
        `${API_BASE_URL}/api/open-offers/${id}/apply`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Une erreur est survenue");
      }
      alert("Offre accepté avec succès");
      navigate("/pro/offres");
    } catch (err) {
      if (err instanceof Error) {
        setSubmitError(err.message);
        alert(err.message);
      } else {
        setSubmitError("Une erreur inconnue est survenue.");
        alert("Une erreur inconnue est survenue.");
      }
    }
  };

  const handleReject = async () => {
    setSubmitError(null);
    const token = localStorage.getItem("token");

    try {
      const response = await fetch(
        `${API_BASE_URL}/api/open-offers/${id}/reject`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || "Une erreur est survenue lors du refus"
        );
      }

      const result = await response.json();
      alert(result.message || "Offre refusée avec succès.");
      navigate("/pro/offres");
    } catch (err) {
      if (err instanceof Error) {
        setSubmitError(err.message);
        alert(err.message);
      } else {
        setSubmitError("Une erreur inconnue est survenue.");
        alert("Une erreur inconnue est survenue.");
      }
    }
  };

  const handleAcceptApplication = async (applicationId: number) => {
    const token = localStorage.getItem("token");
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/offer-applications/${applicationId}/status`,
        {
          method: "PATCH",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ status: "accepted" }),
        }
      );
      if (!response.ok) throw new Error("Erreur lors de l'acceptation");
      window.location.reload();
    } catch (error) {
      alert("Échec de l'acceptation de la candidature.");
    }
  };

  const handleCompleteOffer = async () => {
    const token = localStorage.getItem("token");
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/open-offers/${id}/complete`,
        {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
      if (!response.ok) throw new Error("Erreur de complétion");
      window.location.reload();
    } catch (error) {
      alert("Échec de la complétion de l'offre.");
    }
  };

  const handleCloseOffer = async () => {
    const token = localStorage.getItem("token");
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/open-offers/${id}/close`,
        {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
      if (!response.ok) throw new Error("Erreur de clôture");
      window.location.reload();
    } catch (error) {
      alert("Échec de la clôture de l'offre.");
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <span className="px-3 py-1 rounded-full text-white bg-yellow-500">
            En attente
          </span>
        );
      case "accepted":
        return (
          <span className="px-3 py-1 rounded-full text-white bg-green-500">
            Accepté
          </span>
        );
      case "rejected":
        return (
          <span className="px-3 py-1 rounded-full text-white bg-red-500">
            Refusé
          </span>
        );
      case "invited":
        return (
          <span className="px-3 py-1 rounded-full text-white bg-blue-500">
            Invité
          </span>
        );
      default:
        return (
          <span className="px-3 py-1 rounded-full text-white bg-gray-500">
            Inconnu
          </span>
        );
    }
  };

  const hasAlreadyApplied = () => {
    // return offer.applications.some((application: any) => application.freelance_profile?.user?.id === user.id);
    return offer.applications.some((application: any) => {
      return (
        application.freelance_profile?.user?.id === user.id &&
        application.status !== "invited"
      );
    });
  };

  if (loading)
    return (
      <p className="text-center text-gray-600 text-lg font-semibold">
        Chargement...
      </p>
    );
  if (error)
    return (
      <p className="text-center text-red-500 text-lg font-semibold">
        Erreur : {error}
      </p>
    );
  if (!offer.id)
    return (
      <p className="text-center text-gray-600 text-lg font-semibold">
        Aucune offre trouvée.
      </p>
    );

  return (
    <div className="flex flex-col min-h-screen">
      <NavBarProfil />
      <div className="container mx-auto p-8 bg-white rounded-lg shadow-lg max-w-4xl flex-grow">
        <h1 className="text-3xl font-extrabold mb-6 text-blue-600">
          {offer.title}
        </h1>
        <div className="mb-4">
          <p className="text-gray-700 text-lg">
            <strong>Entreprise :</strong> {offer.company}
          </p>
          <p className="text-gray-700 text-lg">
            <strong>Budget :</strong> {offer.budget || "Non spécifié"} €
          </p>
          <p className="text-gray-700 text-lg">
            <strong>Deadline :</strong>{" "}
            {offer.deadline
              ? new Date(offer.deadline).toLocaleDateString()
              : "Non défini"}
          </p>
          <p className="text-gray-700 text-lg">
            <strong>Description :</strong> {offer.description}
          </p>
        </div>
        <div className="mb-4 p-4 bg-gray-100 rounded-md">
          <p className="text-gray-700 text-lg">
            <strong>Site Web :</strong>{" "}
            {offer.website ? (
              <a
                href={offer.website}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 underline"
              >
                {offer.website}
              </a>
            ) : (
              "Aucun"
            )}
          </p>
          <p className="text-gray-700 text-lg">
            <strong>Catégories :</strong>
            {Array.isArray(offer.categories)
              ? offer.categories.join(", ")
              : "Aucune"}
          </p>
          <p className="text-gray-700 text-lg">
            <strong>Statut :</strong> {offer.status}
          </p>
          <p className="text-gray-700 text-lg">
            <strong>Nombre de vues :</strong> {offer.views_count}
          </p>
        </div>

        {user?.is_professional === false ? (
          <>
            <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">
              Pros invités & intéressés
            </h2>
            {offer.applications.length > 0 ? (
              <ul className="list-none p-0">
                {offer.applications.map((application: any, index: number) => (
                  <li
                    key={index}
                    className="mb-3 p-3 bg-white shadow rounded-md"
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-gray-800 font-semibold">
                          {application.freelance_profile.first_name}{" "}
                          {application.freelance_profile.last_name}
                        </p>
                        <p className="text-sm text-gray-600">
                          {application.freelance_profile.city},{" "}
                          {application.freelance_profile.country}
                        </p>
                        <p className="text-sm text-gray-600">
                          Compétences :{" "}
                          {application.freelance_profile.skills.join(", ")}
                        </p>
                        <p className="text-sm text-gray-600">
                          Langues :{" "}
                          {application.freelance_profile.languages.join(", ")}
                        </p>
                      </div>
                      <div className="flex flex-col items-end gap-2">
                        {getStatusBadge(application.status)}
                        <button
                          className="text-blue-600 text-sm underline hover:text-blue-800"
                          onClick={() => {
                            localStorage.setItem(
                              "receiver_id",
                              application.freelance_profile.user.id
                            ); // <-- on stocke ici
                            navigate(`/discussions/${offer.id}`);
                          }}
                        >
                          Discuter
                        </button>
                        {application.status === "pending" &&
                          offer.status === "open" && (
                            <button
                              className="text-sm bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700"
                              onClick={() =>
                                handleAcceptApplication(application.id)
                              }
                            >
                              Accepter
                            </button>
                          )}
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-600 text-lg">
                Aucun Pro invité & intéressé.
              </p>
            )}

            {(offer.status === "in_progress" || offer.status === "open") && (
              <>
                <div className="mt-6 flex gap-4">
                  <button
                    className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
                    onClick={openInviteModal}
                  >
                    Inviter un professionnel
                  </button>
                  <button
                    onClick={handleCompleteOffer}
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    Marquer comme terminé
                  </button>
                  <button
                    onClick={handleCloseOffer}
                    className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    Clôturer l'offre
                  </button>
                </div>

                {showInviteModal && (
                  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-lg w-full max-w-md shadow">
                      <h2 className="text-xl font-bold mb-4">
                        Inviter un professionnel
                      </h2>
                      <select
                        value={selectedProfessionalId}
                        onChange={(e) =>
                          setSelectedProfessionalId(e.target.value)
                        }
                        className="w-full mb-4 border border-gray-300 p-2 rounded"
                      >
                        <option value="">-- Choisir un professionnel --</option>
                        {/* {professionals.map((pro: any) => (
                                                <option key={pro.id} value={pro.id}>
                                                    {pro.first_name} {pro.last_name} ({pro.email})
                                                </option>
                                            ))} */}

                        {Array.isArray(professionals) &&
                          professionals.map((pro: any) => (
                            <option key={pro.id} value={pro.id}>
                              {pro.first_name} {pro.last_name} ({pro.email})
                            </option>
                          ))}
                      </select>
                      <div className="flex justify-end gap-3">
                        <button
                          onClick={() => setShowInviteModal(false)}
                          className="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400"
                        >
                          Annuler
                        </button>
                        <button
                          onClick={handleInvite}
                          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                        >
                          Inviter
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </>
        ) : (
          offer.status === "open" && (
            <div className="flex flex-col gap-4 mt-6">
              {submitError && (
                <p className="text-red-500 font-medium">❌ {submitError}</p>
              )}
              {hasAlreadyApplied() ? (
                <p className="text-green-700 font-semibold">
                  ✅ Vous êtes déjà intéressé par cette offre.
                </p>
              ) : (
                <div className="flex gap-4">
                  <button
                    onClick={handleApply}
                    className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                  >
                    Intéressé
                  </button>
                  <button
                    onClick={handleReject}
                    className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    Refuser
                  </button>
                </div>
              )}
            </div>
          )
        )}
      </div>
      <Footer />
    </div>
  );
};

export default OfferDetails;
