import React from 'react';
import { Check, CheckCheck } from 'lucide-react';
import Avatar from '../ui/Avatar';

export interface MessageProps {
  id: number;
  text: string;
  timestamp: string;
  sender: {
    id: number;
    name: string;
    avatar?: string;
  };
  isCurrentUser: boolean;
  status?: 'sent' | 'delivered' | 'read';
}

const ChatMessage: React.FC<MessageProps> = ({
  text,
  timestamp,
  sender,
  isCurrentUser,
  status = 'read',
}) => {
  // Format timestamp
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Status icon
  const StatusIcon = () => {
    if (!isCurrentUser) return null;
    
    return status === 'read' ? (
      <CheckCheck className="h-4 w-4 text-primary-500" />
    ) : (
      <Check className="h-4 w-4 text-neutral-400" />
    );
  };

  return (
    <div
      className={`flex ${
        isCurrentUser ? 'justify-end' : 'justify-start'
      } mb-4`}
    >
      {!isCurrentUser && (
        <div className="mr-2 flex-shrink-0">
          <Avatar
            size="sm"
            src={sender.avatar}
            fallback={sender.name.charAt(0)}
          />
        </div>
      )}

      <div
        className={`max-w-[75%] ${
          isCurrentUser
            ? 'bg-primary-500 text-white rounded-tl-lg rounded-tr-lg rounded-bl-lg'
            : 'bg-neutral-100 text-neutral-800 rounded-tl-lg rounded-tr-lg rounded-br-lg'
        } px-4 py-3 shadow-sm`}
      >
        {!isCurrentUser && (
          <div className="font-medium text-sm mb-1">{sender.name}</div>
        )}
        <div className="text-sm whitespace-pre-wrap break-words">{text}</div>
        <div className="flex items-center justify-end mt-1 space-x-1">
          <span className="text-xs opacity-70">{formatTime(timestamp)}</span>
          <StatusIcon />
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
