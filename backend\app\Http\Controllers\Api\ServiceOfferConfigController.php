<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

class ServiceOfferConfigController extends Controller
{
    /**
     * Get price units configuration.
     *
     * @return JsonResponse
     */
    public function getPriceUnits(): JsonResponse
    {
        return response()->json(config('service_offers.price_units'));
    }

    /**
     * Get average durations configuration.
     *
     * @return JsonResponse
     */
    public function getAverageDurations(): JsonResponse
    {
        return response()->json(config('service_offers.average_durations'));
    }

    /**
     * Get image categories configuration.
     *
     * @return JsonResponse
     */
    public function getImageCategories(): JsonResponse
    {
        return response()->json(config('service_offers.image_categories'));
    }

    /**
     * Get all service offer configuration options.
     *
     * @return JsonResponse
     */
    public function getAllOptions(): JsonResponse
    {
        return response()->json([
            'price_units' => config('service_offers.price_units'),
            'average_durations' => config('service_offers.average_durations'),
            'image_categories' => config('service_offers.image_categories'),
        ]);
    }
}
