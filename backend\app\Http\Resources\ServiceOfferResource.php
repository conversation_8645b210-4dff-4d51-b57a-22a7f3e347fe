<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ServiceOfferResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'first_name' => $this->user->first_name,
                    'last_name' => $this->user->last_name,
                    'email' => $this->user->email,
                    'avatar' => $this->user->avatar,
                    'is_professional' => $this->user->is_professional,
                    'professional_details' => $this->user->freelanceProfile,
                ];
            }),
            'title' => $this->title,
            'description' => $this->description,
            'price' => $this->price,
            'price_unit' => $this->price_unit,
            'price_unit_label' => $this->getPriceUnitLabel(),
            'average_duration' => $this->average_duration,
            'average_duration_label' => $this->getAverageDurationLabel(),
            'cover_image' => $this->cover_image,
            'cover_image_url' => $this->cover_image ? asset('storage/' . $this->cover_image) : null,
            'image_category' => $this->image_category,
            'associated_project_id' => $this->associated_project_id,
            'associated_project' => $this->whenLoaded('associatedProject', function () {
                return [
                    'id' => $this->associatedProject->id,
                    'title' => $this->associatedProject->title,
                    'description' => $this->associatedProject->description,
                    'category' => $this->associatedProject->category,
                ];
            }),
            'execution_time' => $this->execution_time,
            'concepts' => $this->concepts,
            'revisions' => $this->revisions,
            'is_private' => $this->is_private,
            'categories' => $this->categories,
            'files' => $this->files,
            'status' => $this->status,
            'likes' => $this->likes,
            'views' => $this->views,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
