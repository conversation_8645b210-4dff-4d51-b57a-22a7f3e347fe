import { useState } from "react";
import CreatProjetClient from "./CreatProjetClient"; // Assurez-vous que le chemin d'importation est correct

const NewProjectClient = () => {
  const [selectedType, setSelectedType] = useState("Indépendants");
  const [selectedCategory, setSelectedCategory] = useState("Tout");
  const [isModalOpen, setIsModalOpen] = useState(false); // État pour gérer l'ouverture du modal

  const categories = [
    "Modélisation 3D",
    "Animation 3D",
    "Rendu 3D",
    "Texturing et Shading",
    "Effets visuels (VFX)",
    "Conception de personnages 3D",
    "Environnements 3D",
    "Réalité virtuelle (VR)",
    "Réalité augmentée (AR)",
    "Simulations physiques",
    "Rigging 3D",
    "Lighting 3D",
    "Compositing 3D",
    "Design de produits 3D",
    "Architecture 3D",
    "Jeux vidéo 3D",
    "Cinéma 4D",
    "Blender",
    "ZBrush",
    "Substance Painter",
  ];

  // Fonction pour ouvrir le modal
  const openModal = () => {
    setIsModalOpen(true);
  };

  // Fonction pour fermer le modal
  const closeModal = () => {
    setIsModalOpen(false);
  };

  return (
    <div className="w-80 p-4 bg-white rounded-lg shadow-lg">
      {/* Bouton "Nouveau projet" */}
      <button
        className="w-full bg-blue-600 text-white font-semibold py-2 rounded-lg flex items-center justify-center gap-2"
        onClick={openModal} // Ouvrir le modal au clic
      >
        Nouveau projet
      </button>

      {/* Section Type */}
      <div className="mt-4">
        <h3 className="text-gray-700 font-semibold flex items-center gap-2">
          🔍 Type
        </h3>
        <div className="mt-2 flex space-x-2">
          <button
            className={`flex-1 py-2 border rounded-lg ${
              selectedType === "Indépendants"
                ? "bg-blue-50 border-blue-500 text-blue-600"
                : "bg-gray-100 text-gray-600"
            }`}
            onClick={() => setSelectedType("Indépendants")}
          >
            Indépendants
          </button>
          <button
            className={`flex-1 py-2 border rounded-lg ${
              selectedType === "Services"
                ? "bg-blue-50 border-blue-500 text-blue-600"
                : "bg-gray-100 text-gray-600"
            }`}
            onClick={() => setSelectedType("Services")}
          >
            Services
          </button>
        </div>
      </div>

      {/* Section Catégories */}
      <div className="mt-6">
        <h3 className="text-gray-700 font-semibold flex items-center gap-2">
          🗂 Catégories
        </h3>
        <div className="mt-2">
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="radio"
              name="category"
              value="Tout"
              checked={selectedCategory === "Tout"}
              onChange={() => setSelectedCategory("Tout")}
              className="accent-blue-500"
            />
            <span>Tout</span>
          </label>

          {/* Catégories populaires */}
          <div className="mt-3 text-gray-500 text-sm">POPULAIRES</div>
          {categories.map((category) => (
            <label
              key={category}
              className="flex items-center space-x-2 mt-2 cursor-pointer"
            >
              <input
                type="radio"
                name="category"
                value={category}
                checked={selectedCategory === category}
                onChange={() => setSelectedCategory(category)}
                className="accent-blue-500"
              />
              <span>{category}</span>
            </label>
          ))}
        </div>

        {/* Lien "Voir toutes les catégories" */}
        <div className="mt-3">
          <a href="#" className="text-blue-600 text-sm font-medium">
            Voir toutes les catégories
          </a>
        </div>
      </div>

      {/* Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl relative">
            <button
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
              onClick={closeModal} // Fermer le modal au clic
            >
              ✕
            </button>
            <CreatProjetClient
            onClose={() => setIsModalOpen(false)}
            onAddOffer={() => {}}
            existingOffer={undefined}
            /> {/* Afficher le composant du modal */}
          </div>
        </div>
      )}
    </div>
  );
};

export default NewProjectClient;