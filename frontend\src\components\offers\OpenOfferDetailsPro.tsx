import React,{ useState, useEffect } from 'react';
import { Calendar, DollarSign, Clock, User, Building, Globe, Eye, Users, Edit, Trash2, Share2, MessageSquare } from 'lucide-react';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import Avatar from '../ui/Avatar';
import { API_BASE_URL } from '../../config';
import { useNavigate, useParams } from 'react-router-dom';
import DashboardLayout from '../dashboard/DashboardLayout';

interface OpenOfferDetailsProps {
  id: number;
  title: string;
  description: string;
  categories?: string[];
  budget: string;
  deadline: string;
  company?: string;
  website?: string;
  recruitment_type?: 'company' | 'personal';
  open_to_applications?: boolean;
  status: string;
  created_at: string;
  updated_at: string;
  views_count?: number;
  applications_count?: number;
  client?: {
    id: number;
    name: string;
    avatar?: string;
  };
  filters?: {
    languages?: string[];
    skills?: string[];
    location?: string;
    experience_years?: number;
    availability_status?: string;
  };
  onEdit?: (offer: any) => void;
  onDelete?: (id: number) => void;
  onApply?: (id: number) => void;
  onContact?: (clientId: number) => void;
  onShare?: (id: number) => void;
  isOwner?: boolean;
  isProfessional?: boolean;
}

const OpenOfferDetailsPro: React.FC= () => {
const navigate = useNavigate();
const { id } = useParams<{ id: string }>();
const [isLoading, setIsLoading] = useState(true);
const [offreEncours, setOffreEncour] = useState<any | null>(null);
const [offerss, setOffer] = useState({
        id: null,
        title: "",
        company: "",
        budget: "",
        deadline: "",
        description: "",
        website: "",
        categories: "[]",
        status: "",
        views_count: 0,
        applications: [],
    });

// Récupérer le token d'authentification
const token = localStorage.getItem('token');
const user = JSON.parse(localStorage.getItem('user') || '{}');
const isProfessional = user.role === 'professional';

const fetchOfferById = async () => {
  setIsLoading(true);
  try {
    const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Erreur lors de la récupération de l\'offre');
    }

    const data = await response.json();
    console.log("Réponse API pour l'offre spécifique:", data);
    
    if (data.open_offer) {
      setOffer(data.open_offer);

      const offer = data.open_offer;

      // ❗ JSON.parse peut jeter une erreur : il est préférable de l’encadrer
      let parsedCategories: any[] = [];
      if (typeof offer.categories === 'string') {
        try {
          parsedCategories = JSON.parse(offer.categories);
        } catch (e) {
          console.warn('Erreur lors du parsing de categories :', e);
        }
      } else if (Array.isArray(offer.categories)) {
        parsedCategories = offer.categories;
      }

      const formattedOffer = {
        id: offer.id,
        title: offer.title,
        description: offer.description,
        categories: parsedCategories,
        budget: offer.budget,
        deadline: offer.deadline,
        company: offer.company,
        website: offer.website,
        recruitment_type: offer.recruitment_type || 'company',
        open_to_applications: offer.open_to_applications !== false,
        auto_invite: offer.auto_invite || false,
        status: offer.status,
        created_at: offer.created_at,
        updated_at: offer.updated_at,
        views_count: offer.views_count || 0,
        applications_count: offer.applications_count || 0,
        user_id: offer.user_id,
        client: offer.user ? {
          id: offer.user.id,
          name: `${offer.user.first_name} ${offer.user.last_name}`,
          avatar: offer.user.profile_picture_path,
        } : null,
        files: offer.files || [],
        filters: offer.filters || {
          languages: [],
          skills: [],
          location: '',
          experience_years: 0,
          availability_status: 'available',
        },
      };

      setOffreEncour(formattedOffer);
    } else {
      console.error('Erreur: offre non trouvée');
    }
  } catch (err) {
    console.error('Erreur:', err);
  } finally {
    setIsLoading(false);
  }
};

// ✅ Pour éviter que l'effet se déclenche à chaque rendu
useEffect(() => {
  fetchOfferById();
}, [id]);

// const fetchOfferById = async () => {
//     setIsLoading(true);
//     try {
//       const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}`, {
//         method: 'GET',
//         headers: {
//           'Authorization': `Bearer ${token}`,
//           'Content-Type': 'application/json',
//         },
//       });

//       if (!response.ok) {
//         setIsLoading(false);
//         throw new Error('Erreur lors de la récupération de l\'offre');
        
//       }

//       const data = await response.json();
//       console.log("Réponse API pour l'offre spécifique:", data);

//       if (data.open_offer) {
//         // Formater l'offre pour correspondre à notre structure
//         const offer = data.open_offer;
//         const formattedOffer = {
//           id: offer.id,
//           title: offer.title,
//           description: offer.description,
//           categories: typeof offer.categories === 'string' ? JSON.parse(offer.categories) : (offer.categories || []),
//           budget: offer.budget,
//           deadline: offer.deadline,
//           company: offer.company,
//           website: offer.website,
//           recruitment_type: offer.recruitment_type || 'company',
//           open_to_applications: offer.open_to_applications !== false,
//           auto_invite: offer.auto_invite || false,
//           status: offer.status,
//           created_at: offer.created_at,
//           updated_at: offer.updated_at,
//           views_count: offer.views_count || 0,
//           applications_count: offer.applications_count || 0,
//           user_id: offer.user_id, // Ajouter l'ID de l'utilisateur propriétaire
//           client: offer.user ? {
//             id: offer.user.id,
//             name: `${offer.user.first_name} ${offer.user.last_name}`,
//             avatar: offer.user.profile_picture_path,
//           } : null,
//           files: offer.files || [],
//           filters: offer.filters || {
//             languages: [],
//             skills: [],
//             location: '',
//             experience_years: 0,
//             availability_status: 'available',
//           },
//         };

//         setOffreEncour(formattedOffer);
//         setIsLoading(false);
        
//       } else {
//         console.error('Erreur: non trouvé');
//         setIsLoading(false);
//       }
//     } catch (err) {
//       console.error('Erreur:', err);
//       setIsLoading(false);
//     } finally {
//       setIsLoading(false);
//     }
//   };

// useEffect(() => {
//     fetchOfferById();
//   });

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  // Calculate days remaining
  const getDaysRemaining = (dateString: string) => {
    const deadline = new Date(dateString);
    const now = new Date();
    const diffTime = deadline.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return 'Délai dépassé';
    } else if (diffDays === 0) {
      return 'Dernier jour';
    } else {
      return `${diffDays} jour${diffDays > 1 ? 's' : ''} restant${diffDays > 1 ? 's' : ''}`;
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'open':
        return 'success';
      case 'in_progress':
        return 'primary';
      case 'completed':
        return 'neutral';
      case 'closed':
        return 'danger';
      case 'invited':
        return 'info';
      default:
        return 'neutral';
    }
  };

  // Get status label
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'En attente';
      case 'open':
        return 'Ouvert';
      case 'in_progress':
        return 'En cours';
      case 'completed':
        return 'Terminé';
      case 'closed':
        return 'Fermé';
      case 'invited':
        return 'Invité';
      default:
        return 'Inconnu';
    }
  };

  const hasAlreadyApplied = () => {
        // return offer.applications.some((application: any) => application.freelance_profile?.user?.id === user.id);
        return offerss.applications.some((application: any) => {
            return (
                application.freelance_profile?.user?.id === user.id &&
                application.status !== 'invited'
            );
        });
    };

  if (isLoading) {
    return (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
    );
  }

  return (
    <DashboardLayout
      title={isProfessional ? "Détails de l'offre" : "Détails de l'offre"}
      subtitle={isProfessional
        ? "Explorez les détails des offres disponibles"
        : "Gérez vos offres ouvertes et trouvez des professionnels qualifiés"
      }
    >

    
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Main content */}
      <div className="lg:col-span-2">
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-neutral-200 flex justify-between items-center">
            <h2 className="text-xl font-semibold text-neutral-900">Détails de l'offre</h2>
            <Badge color={getStatusBadgeColor(offreEncours?.status)}>
              {getStatusLabel(offreEncours?.status)}
            </Badge>
          </div>

          <div className="p-6">
            <h1 className="text-2xl font-bold text-neutral-900 mb-4">{offreEncours?.title}</h1>

            {/* Categories */}
            {offreEncours?.categories.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-6">
                {offreEncours?.categories.map((category:string, index:number) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-neutral-100 text-neutral-800"
                  >
                    {category}
                  </span>
                ))}
              </div>
            )}

            {/* Description */}
            <div className="prose prose-neutral max-w-none mb-6">
              <p className="whitespace-pre-line">{offreEncours?.description}</p>
            </div>

            {/* Key details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              {/* Budget */}
              <div className="flex items-center">
                <DollarSign className="h-5 w-5 text-neutral-500 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-neutral-700">Budget</h3>
                  <p className="text-neutral-900">{offreEncours?.budget}</p>
                </div>
              </div>

              {/* Deadline */}
              <div className="flex items-center">
                <Calendar className="h-5 w-5 text-neutral-500 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-neutral-700">Date limite</h3>
                  <p className="text-neutral-900">{formatDate(offreEncours?.deadline)}</p>
                </div>
              </div>

              {/* Days remaining */}
              <div className="flex items-center">
                <Clock className="h-5 w-5 text-neutral-500 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-neutral-700">Temps restant</h3>
                  <p className="text-neutral-900">{getDaysRemaining(offreEncours?.deadline)}</p>
                </div>
              </div>

              {/* Company */}
              {offreEncours?.company && (
                <div className="flex items-center">
                  <Building className="h-5 w-5 text-neutral-500 mr-3" />
                  <div>
                    <h3 className="text-sm font-medium text-neutral-700">Entreprise</h3>
                    <p className="text-neutral-900">{offreEncours?.company}</p>
                  </div>
                </div>
              )}

              {/* Website */}
              {offreEncours?.website && (
                <div className="flex items-center">
                  <Globe className="h-5 w-5 text-neutral-500 mr-3" />
                  <div>
                    <h3 className="text-sm font-medium text-neutral-700">Site web</h3>
                    <a
                      href={offreEncours?.website.startsWith('http') ? offreEncours?.website : `https://${offreEncours?.website}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary-600 hover:text-primary-700"
                    >
                      {offreEncours?.website}
                    </a>
                  </div>
                </div>
              )}

              {/* Views count */}
              <div className="flex items-center">
                <Eye className="h-5 w-5 text-neutral-500 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-neutral-700">Vues</h3>
                  <p className="text-neutral-900">{offreEncours?.views_count}</p>
                </div>
              </div>

              
            </div>

            {/* Client info (for professionals only) */}
            {isProfessional && offreEncours?.client && (
              <div className="bg-neutral-50 rounded-lg p-4 border border-neutral-200 mb-6">
                <div className="flex items-center">
                  <Avatar
                    src={offreEncours?.client.avatar}
                    fallback={offreEncours?.client.name.charAt(0)}
                    size="md"
                    className="mr-3"
                  />
                  <div>
                    <h3 className="text-sm font-medium text-neutral-700">Client</h3>
                    <p className="text-neutral-900">{offreEncours?.client.name}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Required skills and languages (if specified) */}
            {(offreEncours?.filters.skills?.length || offreEncours?.filters.languages?.length || offreEncours?.filters.location) && (
              <div className="bg-neutral-50 rounded-lg p-4 border border-neutral-200 mb-6">
                <h3 className="text-sm font-medium text-neutral-700 mb-3">Critères recherchés</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Skills */}
                  {offreEncours?.filters.skills && offreEncours?.filters.skills.length > 0 && (
                    <div>
                      <h4 className="text-xs font-medium text-neutral-500 mb-2">Compétences</h4>
                      <div className="flex flex-wrap gap-2">
                        {offreEncours?.filters.skills.map((skill:string, index:number) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-neutral-200 text-neutral-800"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Languages */}
                  {offreEncours?.filters.languages && offreEncours?.filters.languages.length > 0 && (
                    <div>
                      <h4 className="text-xs font-medium text-neutral-500 mb-2">Langues</h4>
                      <div className="flex flex-wrap gap-2">
                        {offreEncours?.filters.languages.map((language:string, index:number) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-neutral-200 text-neutral-800"
                          >
                            {language}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Location */}
                  {offreEncours?.filters.location && (
                    <div>
                      <h4 className="text-xs font-medium text-neutral-500 mb-2">Localisation</h4>
                      <span className="text-sm text-neutral-700">{offreEncours?.filters.location}</span>
                    </div>
                  )}

                  {/* Experience years */}
                  {offreEncours?.filters.experience_years && offreEncours?.filters.experience_years > 0 && (
                    <div>
                      <h4 className="text-xs font-medium text-neutral-500 mb-2">Expérience minimale</h4>
                      <span className="text-sm text-neutral-700">
                        {offreEncours?.filters.experience_years} an{offreEncours?.filters.experience_years > 1 ? 's' : ''}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Metadata */}
            <div className="text-xs text-neutral-500 mt-6 pt-6 border-t border-neutral-200">
              <p>Créé le {formatDate(offreEncours?.created_at)}</p>
              <p>Dernière mise à jour le {formatDate(offreEncours?.updated_at)}</p>
              <p>
                Type de recrutement: {offreEncours?.recruitment_type === 'company' ? 'Entreprise' : 'Personnel'}
              </p>
              <p>
                {offreEncours?.open_to_applications
                  ? 'Ouvert aux candidatures'
                  : 'Fermé aux candidatures (sur invitation uniquement)'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Sidebar */}
      <div className="lg:col-span-1">
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden sticky top-6">
          <div className="px-6 py-4 border-b border-neutral-200">
            <h3 className="text-lg font-semibold text-neutral-900">Actions</h3>
          </div>

          <div className="p-6 space-y-4">
            

            {/* Actions for professionals */}
            {isProfessional && (
              <>
                {offreEncours?.status === 'open' && (
                  <Button
                    variant="primary"
                    // onClick={() => onApply(id)}
                    fullWidth
                    style={{ backgroundColor: '#2980b9', color: 'black' }}
                  >
                    Postuler à cette offre
                  </Button>
                )}

                {offreEncours?.client && (
                  <Button
                    variant="outline"
                    leftIcon={<MessageSquare className="h-5 w-5" />}
                    // onClick={() => onContact(client.id)}
                    fullWidth
                  >
                    Contacter le client
                  </Button>
                )}
              </>
            )}

            {/* Share action (for everyone) */}
            
              <Button
                variant="ghost"
                leftIcon={<Share2 className="h-5 w-5" />}
                // onClick={() => onShare(id)}
                fullWidth
              >
                Partager l'offre
              </Button>
           
          </div>
        </div>
      </div>
    </div>
    </DashboardLayout>
  );
};

export default OpenOfferDetailsPro;
