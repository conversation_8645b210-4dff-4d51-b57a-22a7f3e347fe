import React from 'react';
import { Youtube, Facebook, Twitter, Instagram, Linkedin } from 'lucide-react';

function Footer() {
  return (
    <footer className="bg-gray-900 text-gray-400 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-2 md:grid-cols-5 gap-8 mb-8">
          <div className="col-span-2">
            <div className="text-xl font-bold text-white mb-4">Hi 3D Artiste</div>
          </div>
          <div>
            <h3 className="font-semibold text-white mb-4">Qui sommes nous ?</h3>
            <ul className="space-y-2">
              <li><a href="#" className="hover:text-white">Vendez vos services</a></li>
              <li><a href="#" className="hover:text-white">Glossaire</a></li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold text-white mb-4">Contact</h3>
            <ul className="space-y-2">
              <li><a href="#" className="hover:text-white">CGV</a></li>
              <li><a href="#" className="hover:text-white">CGU</a></li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold text-white mb-4">Légal</h3>
            <ul className="space-y-2">
              <li><a href="#" className="hover:text-white">Contact</a></li>
            </ul>
          </div>
        </div>
        <div className="flex flex-col md:flex-row justify-between items-center pt-8 border-t border-gray-800">
          <p>© Hi Artist © 2024. All rights reserved</p>
          <div className="flex space-x-4 mt-4 md:mt-0">
            <Youtube className="h-5 w-5 hover:text-white cursor-pointer" />
            <Facebook className="h-5 w-5 hover:text-white cursor-pointer" />
            <Twitter className="h-5 w-5 hover:text-white cursor-pointer" />
            <Instagram className="h-5 w-5 hover:text-white cursor-pointer" />
            <Linkedin className="h-5 w-5 hover:text-white cursor-pointer" />
          </div>
        </div>
      </div>
    </footer>
  );
}

export default Footer;