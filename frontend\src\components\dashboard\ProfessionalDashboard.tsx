import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Award,
  Briefcase,
  DollarSign,
  Star,
  Clock,
  Users,
  MessageSquare,
  Plus,
  FileText,
  Bell,
  CheckCircle,
  XCircle,
  Calendar,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from './DashboardLayout';
import StatCard from './StatCard';
import ActivityFeed from './ActivityFeed';
import ProjectCard, { ProjectCardProps } from './ProjectCard';
import OfferInvitationCard from './OfferInvitationCard';
import UnifiedProfileLink from './UnifiedProfileLink';
import AvailabilitySettings from './AvailabilitySettings';
import Button from '../ui/Button';
import Avatar from '../ui/Avatar';

interface ProfessionalDashboardProps {
  completionPercentage: number;
}

const ProfessionalDashboard: React.FC<ProfessionalDashboardProps> = ({ completionPercentage }) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState({
    activeProjects: 0,
    earnings: '0',
    rating: 0,
    completionRate: 0,
  });
  const [projects, setProjects] = useState<ProjectCardProps[]>([]);
  const [activities, setActivities] = useState<any[]>([]);
  const [profile, setProfile] = useState<any>(null);
  const [receivedOffers, setReceivedOffers] = useState<any[]>([]);
  const [showAvailabilityModal, setShowAvailabilityModal] = useState(false);

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');

  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        // Fetch dashboard data from API
        const dashboardResponse = await fetch(`${API_BASE_URL}/api/dashboard`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        // Fetch received offers
        const receivedOffersResponse = await fetch(`${API_BASE_URL}/api/offer-applications/received`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!dashboardResponse.ok) {
          // Si l'API renvoie une erreur, utiliser des données statiques
          console.warn('Fallback to static data due to API error:', dashboardResponse.status);
          loadStaticData();
          return;
        }

        const dashboardData = await dashboardResponse.json();

        // Traiter les données des offres reçues
        if (receivedOffersResponse.ok) {
          const receivedOffersData = await receivedOffersResponse.json();
          console.log('Received offers data:', receivedOffersData);

          if (receivedOffersData.offers && receivedOffersData.offers.length > 0) {
            setReceivedOffers(receivedOffersData.offers.map((offer: any) => ({
              id: offer.id,
              id_offer: offer.id_offer,
              title: offer.title,
              description: offer.description,
              budget: offer.budget,
              deadline: offer.deadline,
              createdAt: offer.created_at,
              isInvited: offer.is_invited || false,
              status: offer.status,
              client: {
                id: offer.client.id,
                name: offer.client.name,
                avatar: offer.client.avatar,
              },
            })));
          }
        } else {
          console.warn('Failed to fetch received offers:', receivedOffersResponse.status);
          // Utiliser des données de secours pour les offres reçues
          setReceivedOffers([]);
        }

        // Set profile data
        setProfile(dashboardData.profile);
        localStorage.setItem('userProfile', JSON.stringify(dashboardData.profile));

        // Set stats
        setStats({
          activeProjects: dashboardData.stats.activeProjects,
          earnings: dashboardData.stats.earnings,
          rating: dashboardData.stats.rating,
          completionRate: dashboardData.stats.completionRate,
        });

        // Set projects
        setProjects(dashboardData.projects.map((project: any) => ({
          id: project.id,
          title: project.title,
          description: project.description,
          budget: project.budget,
          deadline: project.deadline,
          status: project.status,
          client: project.client,
        })));

        // Set activities with proper icons
        setActivities(dashboardData.activities.map((activity: any) => {
          // Déterminer l'icône appropriée en fonction du type d'activité
          let icon;
          switch(activity.icon) {
            case 'MessageSquare':
              icon = <MessageSquare className="h-5 w-5" />;
              break;
            case 'CheckCircle':
              icon = <CheckCircle className="h-5 w-5" />;
              break;
            case 'Star':
              icon = <Star className="h-5 w-5" />;
              break;
            case 'DollarSign':
              icon = <DollarSign className="h-5 w-5" />;
              break;
            case 'Briefcase':
              icon = <Briefcase className="h-5 w-5" />;
              break;
            default:
              icon = <MessageSquare className="h-5 w-5" />;
          }

          return {
            ...activity,
            icon,
          };
        }));

        setError(null);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        // En cas d'erreur, utiliser des données statiques
        loadStaticData();
      } finally {
        setLoading(false);
      }
    };

    // Fonction pour charger des données statiques en cas d'erreur
    const loadStaticData = () => {
      // Mock stats
      setStats({
        activeProjects: 3,
        earnings: '2 500 €',
        rating: 4.8,
        completionRate: 95,
      });

      // Mock projects
      setProjects([
        {
          id: 1,
          title: 'Création d\'un personnage 3D pour jeu vidéo',
          description: 'Modélisation et animation d\'un personnage principal pour un jeu d\'aventure.',
          budget: '1 200 €',
          deadline: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(), // 15 days from now
          status: 'in_progress',
          client: {
            id: 101,
            name: 'Studio GameArt',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
          },
        },
        {
          id: 2,
          title: 'Animation d\'une scène d\'introduction',
          description: 'Création d\'une animation 3D de 30 secondes pour l\'introduction d\'une application mobile.',
          budget: '800 €',
          deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
          status: 'in_progress',
          client: {
            id: 102,
            name: 'AppTech Solutions',
            avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
          },
        },
        {
          id: 3,
          title: 'Modélisation d\'objets pour environnement virtuel',
          description: 'Création de 10 objets 3D pour un environnement de réalité virtuelle.',
          budget: '500 €',
          deadline: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
          status: 'completed',
          client: {
            id: 103,
            name: 'VR Experiences',
            avatar: 'https://randomuser.me/api/portraits/men/67.jpg',
          },
        },
      ] as ProjectCardProps[]);

      // Mock activities
      setActivities([
        {
          id: 1,
          title: 'Nouveau message de Studio GameArt',
          description: 'Pouvez-vous partager une mise à jour sur le personnage 3D ?',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
          icon: <MessageSquare className="h-5 w-5" />,
          user: {
            name: 'Studio GameArt',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
          },
        },
        {
          id: 2,
          title: 'Projet terminé',
          description: 'Vous avez marqué "Modélisation d\'objets pour environnement virtuel" comme terminé',
          timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
          icon: <CheckCircle className="h-5 w-5" />,
        },
        {
          id: 3,
          title: 'Nouvelle évaluation reçue',
          description: 'VR Experiences vous a donné 5 étoiles pour votre travail',
          timestamp: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(), // 4 days ago
          icon: <Star className="h-5 w-5" />,
          iconBackground: 'bg-yellow-100',
          iconColor: 'text-yellow-600',
        },
        {
          id: 4,
          title: 'Paiement reçu',
          description: '500 € reçus pour "Modélisation d\'objets pour environnement virtuel"',
          timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
          icon: <DollarSign className="h-5 w-5" />,
          iconBackground: 'bg-green-100',
          iconColor: 'text-green-600',
        },
      ]);

      // Mock received offers
      setReceivedOffers([
        {
          id: 201,
          title: 'Création d\'un environnement 3D pour jeu mobile',
          description: 'Nous recherchons un artiste 3D pour créer un environnement complet pour notre jeu mobile d\'aventure.',
          budget: '1 500 €',
          deadline: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString(), // 20 days from now
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
          isInvited: true,
          client: {
            id: 101,
            name: 'MobileGames Studio',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
          },
        },
        {
          id: 202,
          title: 'Modélisation de personnages pour série animée',
          description: 'Projet de modélisation de 5 personnages principaux pour une série animée en 3D.',
          budget: '2 000 €',
          deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
          createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
          isInvited: false,
          client: {
            id: 102,
            name: 'Animation Productions',
            avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
          },
        },
      ]);

      // Afficher un message d'erreur discret
      setError('Utilisation de données de démonstration - La connexion à la base de données a échoué');
    };

    fetchDashboardData();
  }, [token]);

  const handleProjectClick = (projectId: number) => {
    navigate(`/dashboard/offers/${projectId}`);
  };

  const handleViewAllProjects = () => {
    // navigate('/all-projects');
    navigate('/dashboard/open-offers');
  };

  const handleViewAllActivities = () => {
    navigate('/all-activities');
  };

  const handleViewOffer = (offerId: number) => {
    navigate(`/dashboard/offers/${offerId}`);
  };

  const handleAcceptOffer = async (offerId: number) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/offer-applications/${offerId}/accept`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de l\'acceptation de l\'offre');
      }

      // Mettre à jour la liste des offres reçues
      // setReceivedOffers(receivedOffers.filter(offer => offer.id !== offerId));
      window.location.reload();

      // Ajouter un message de succès (à implémenter)
      console.log('Offre acceptée avec succès');
    } catch (err) {
      console.error('Erreur:', err);
      // Afficher un message d'erreur (à implémenter)
    }
  };

  const handleDeclineOffer = async (offerId: number) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/offer-applications/${offerId}/decline`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors du refus de l\'offre');
      }

      // Mettre à jour la liste des offres reçues
      // setReceivedOffers(receivedOffers.filter(offer => offer.id !== offerId));
      window.location.reload();


      // Ajouter un message de succès (à implémenter)
      console.log('Offre refusée avec succès');
    } catch (err) {
      console.error('Erreur:', err);
      // Afficher un message d'erreur (à implémenter)
    }
  };

  const handleViewAllOffers = () => {
    navigate('/dashboard/received-offers');
  };

  const handleViewOfferStats = () => {
    navigate('/dashboard/offer-stats');
  };

  const handleViewServices = () => {
    // Rediriger vers la page des services du professionnel connecté dans le dashboard
    console.log("Navigating to my services in dashboard with user ID:", user.id);

    // Utiliser navigate pour rediriger vers /dashboard/services avec l'ID de l'utilisateur
    navigate(`/dashboard/services?user=${user.id}`);
  };

  const handleCreateService = () => {
    // Utiliser navigate au lieu de window.location.href
    console.log("Navigating to create service page with React Router");
    console.log("Current user from component state:", user);

    // Puisque nous sommes déjà dans le dashboard professionnel, nous pouvons naviguer directement
    // sans vérifier à nouveau le rôle
    navigate('/dashboard/services/create');
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-lg font-semibold text-red-700 mb-2">Erreur</h2>
          <p className="text-red-600">{error}</p>
          <Button
            variant="primary"
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Réessayer
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  // Utiliser le pourcentage de complétion passé en prop au lieu de celui du profil
  // Accéder directement au profil car il est déjà formaté par le contrôleur
  const profileData = profile || {};

  // Afficher les données du profil dans la console pour le débogage
  console.log('Profile data from API:', profile);

  // Afficher le chemin de l'avatar pour le débogage
  console.log('Avatar path:', profileData.avatar);

  // Construire l'URL complète de l'avatar
  const avatarUrl = profileData.avatar
    ? (profileData.avatar.startsWith('http')
        ? profileData.avatar
        : profileData.avatar.startsWith('/')
          ? `${API_BASE_URL}${profileData.avatar}`
          : `${API_BASE_URL}/${profileData.avatar}`)
    : null;

  console.log('Complete avatar URL:', avatarUrl);

  return (
    <DashboardLayout
      title={`Bonjour, ${user.first_name || 'Professionnel'}`}
      subtitle="Bienvenue sur votre tableau de bord"
      actions={
        <div style={{ display: 'flex', gap: '0.75rem' }}>
          <Button
            variant="outline"
            leftIcon={<Bell className="h-5 w-5" />}
            onClick={() => console.log('Notifications')}
          >
            Notifications
          </Button>
          <div style={{ display: 'flex', gap: '0.75rem' }}>
            <Button
              variant="outline"
              leftIcon={<FileText className="h-5 w-5" />}
              onClick={handleViewOfferStats}
            >
              Statistiques
            </Button>
            <Button
              variant="outline"
              leftIcon={<Briefcase className="h-5 w-5" />}
              onClick={handleViewServices}
            >
              Mes Services
            </Button>
            <Button
              variant="outline"
              leftIcon={<Award className="h-5 w-5" />}
              onClick={() => navigate('/dashboard/achievements')}
            >
              Mes Réalisations
            </Button>
            <Button
              variant="outline"
              leftIcon={<Plus className="h-5 w-5" />}
              onClick={handleCreateService}
            >
              Créer un service
            </Button>
            <Button
              variant="primary"
              leftIcon={<Plus className="h-5 w-5" />}
              onClick={() => navigate('/dashboard/open-offers')}
              style={{ backgroundColor: '#2980b9', color: 'white' }}
            >
              Trouver des offres
            </Button>
          </div>
        </div>
      }
    >
      {/* New Unified Profile Link */}
      <UnifiedProfileLink />

      {/* Profile completion alert if needed - only show if below 80% */}
      {completionPercentage < 80 && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6 flex items-center justify-between">
          <div className="flex items-center">
            <Bell className="h-5 w-5 text-amber-500 mr-3" />
            <div>
              <p className="text-amber-800 font-medium">Votre profil est incomplet</p>
              <p className="text-amber-700 text-sm">Complétez votre profil pour augmenter vos chances d'être sélectionné pour des projets.</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => navigate('/dashboard/profile/edit')}
              className="px-4 py-2 font-medium border-primary-500 text-primary-600"
            >
              Compléter mon profil
            </Button>
            <Button
              variant="primary"
              onClick={() => navigate('/dashboard/profile')}
              className="px-4 py-2 font-medium"
            >
              Voir mon profil
            </Button>
          </div>
        </div>
      )}

      {/* Stats row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <StatCard
          title="Projets actifs"
          value={stats.activeProjects}
          icon={<Briefcase className="h-6 w-6" />}
        />
        <StatCard
          title="Revenus ce mois-ci"
          value={stats.earnings}
          icon={<DollarSign className="h-6 w-6" />}
          change={{ value: 12, isPositive: true }}
        />
        <StatCard
          title="Évaluation moyenne"
          value={stats.rating}
          icon={<Star className="h-6 w-6" />}
          description="Basé sur 15 évaluations"
        />
        <StatCard
          title="Taux de complétion"
          value={`${stats.completionRate}%`}
          icon={<CheckCircle className="h-6 w-6" />}
        />
      </div>

      {/* Offres reçues */}
      {receivedOffers && receivedOffers.length > 0 && (
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-neutral-900">Appels d'offre reçus</h2>
            <Button
              variant="outline"
              size="sm"
              onClick={handleViewAllOffers}
            >
              Voir tous
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {receivedOffers.slice(0, 2).map(offer => (
              <OfferInvitationCard
                key={offer.id}
                {...offer}
                onAccept={handleAcceptOffer}
                onDecline={handleDeclineOffer}
                onView={handleViewOffer}
              />
            ))}
          </div>
        </div>
      )}

      {/* Main content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Projects section */}
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-neutral-900">Projets en cours</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleViewAllProjects}
              >
                Voir tous
              </Button>
            </div>

            <div className="p-6 grid grid-cols-1 gap-6">
              {projects.filter(p => p.status === 'in_progress').length === 0 ? (
                <div className="text-center py-8">
                  <Briefcase className="h-12 w-12 text-neutral-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-neutral-700 mb-1">Aucun projet en cours</h3>
                  <p className="text-neutral-500 mb-4">Commencez à chercher des projets qui correspondent à vos compétences.</p>
                  <Button
                    variant="primary"
                    onClick={() => navigate('/dashboard/open-offers')}
                  >
                    Trouver des projets
                  </Button>
                </div>
              ) : (
                projects
                  .filter(project => project.status === 'in_progress')
                  .map(project => (
                    <ProjectCard
                      key={project.id}
                      {...project}
                      onClick={() => handleProjectClick(project.id)}
                    />
                  ))
              )}
            </div>
          </div>

          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-neutral-900">Projets terminés récemment</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleViewAllProjects}
              >
                Voir tous
              </Button>
            </div>

            <div className="p-6 grid grid-cols-1 gap-6">
              {projects.filter(p => p.status === 'completed').length === 0 ? (
                <div className="text-center py-6 text-neutral-500">
                  Aucun projet terminé récemment
                </div>
              ) : (
                projects
                  .filter(project => project.status === 'completed')
                  .map(project => (
                    <ProjectCard
                      key={project.id}
                      {...project}
                      onClick={() => handleProjectClick(project.id)}
                    />
                  ))
              )}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Profile card */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="p-6 text-center">
              <Avatar
                size="xl"
                src={avatarUrl}
                fallback={(user.first_name || 'U').charAt(0)}
                className="mx-auto mb-4"
              />
              <h3 className="text-lg font-semibold text-neutral-900">
                {user.first_name} {user.last_name}
              </h3>
              <p className="text-neutral-600 mb-4">
                {profileData.title || 'Artiste 3D'}
              </p>

              <div className="flex justify-center space-x-2 mb-4">
                <div className="flex items-center text-sm text-neutral-600">
                  <Star className="h-4 w-4 text-yellow-500 mr-1" />
                  <span>{stats.rating}</span>
                </div>
                <div className="flex items-center text-sm text-neutral-600">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                  <span>{stats.completionRate}%</span>
                </div>
                <div className="flex items-center text-sm text-neutral-600">
                  <Clock className="h-4 w-4 text-blue-500 mr-1" />
                  <span>{profileData.experience || 3} ans</span>
                </div>
              </div>

              {/* Indicateur de disponibilité */}
              <div className="mb-4 flex justify-center">
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                  profileData.availability_status === 'available' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  <div className={`w-2 h-2 rounded-full mr-2 ${
                    profileData.availability_status === 'available' ? 'bg-green-500' : 'bg-red-500'
                  }`}></div>
                  {profileData.availability_status === 'available' ? 'Disponible' : 'Indisponible'}
                </div>
              </div>

              <div className="flex justify-center space-x-2 mb-3">
                <Button
                  variant="outline"
                  onClick={() => navigate('/dashboard/profile/edit')}
                >
                  Modifier mon profil
                </Button>
                <Button
                  variant="primary"
                  onClick={() => navigate('/dashboard/profile')}
                >
                  Voir mon profil
                </Button>
              </div>

              <div className="flex justify-center">
                <Button
                  variant="outline"
                  leftIcon={<Clock className="h-4 w-4" />}
                  onClick={() => setShowAvailabilityModal(true)}
                  className="text-sm"
                >
                  Gérer mes disponibilités
                </Button>
              </div>
            </div>

            <div className="px-6 py-4 bg-neutral-50 border-t border-neutral-200">
              <div className="flex justify-between text-sm">
                <span className="text-neutral-600">Complétion du profil</span>
                <span className="font-medium text-neutral-900">{completionPercentage}%</span>
              </div>
              <div className="w-full bg-neutral-200 rounded-full h-2 mt-2">
                <div
                  className="bg-primary-600 h-2 rounded-full"
                  style={{ width: `${completionPercentage}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* Activity feed */}
          <ActivityFeed
            activities={activities}
            maxItems={5}
            showViewAll
            onViewAll={handleViewAllActivities}
          />

          {/* Actions rapides */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h3 className="text-lg font-semibold text-neutral-900">Actions rapides</h3>
            </div>

            <div className="p-4 space-y-3">
              <Button
                variant="primary"
                fullWidth
                leftIcon={<Plus className="h-4 w-4" />}
                onClick={handleCreateService}
                style={{ backgroundColor: '#2980b9', color: 'white', fontWeight: 'bold' }}
              >
                Créer un nouveau service
              </Button>

              <Button
                variant="outline"
                fullWidth
                leftIcon={<MessageSquare className="h-4 w-4" />}
                onClick={() => navigate(`/discussions/${user.id}`)}
              >
                Voir mes messages
              </Button>

              <Button
                variant="outline"
                fullWidth
                leftIcon={<FileText className="h-4 w-4" />}
                onClick={() => navigate('/invoices')}
              >
                Voir mes factures
              </Button>

              <Button
                variant="outline"
                fullWidth
                leftIcon={<Briefcase className="h-4 w-4" />}
                onClick={handleViewServices}
              >
                Gérer mes services
              </Button>

              <Button
                variant="outline"
                fullWidth
                leftIcon={<Award className="h-4 w-4" />}
                onClick={() => navigate('/dashboard/achievements')}
              >
                Gérer mes réalisations
              </Button>
            </div>
          </div>

          {/* Calendar preview */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-neutral-900">Calendrier</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => console.log('View calendar')}
              >
                Voir tout
              </Button>
            </div>

            <div className="p-6">
              <div className="space-y-3">
                <div className="flex items-start">
                  <div className="flex-shrink-0 bg-primary-100 text-primary-800 rounded-md p-2 mr-3">
                    <Calendar className="h-5 w-5" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-neutral-900">Livraison du projet GameArt</p>
                    <p className="text-xs text-neutral-500">Demain, 18:00</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 bg-blue-100 text-blue-800 rounded-md p-2 mr-3">
                    <MessageSquare className="h-5 w-5" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-neutral-900">Appel avec AppTech Solutions</p>
                    <p className="text-xs text-neutral-500">Jeudi, 14:30</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modal de gestion des disponibilités */}
      {showAvailabilityModal && (
        <AvailabilitySettings onClose={() => setShowAvailabilityModal(false)} />
      )}
    </DashboardLayout>
  );
};

export default ProfessionalDashboard;
