import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Briefcase, User, Calendar, DollarSign, Clock, CheckCircle, XCircle, Users, Search, UserPlus } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import OfferDiscussionPanel from '../messaging/OfferDiscussionPanel';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import Alert from '../ui/Alert';
import Avatar from '../ui/Avatar';
import Modal from '../ui/Modal';
import { useNotifications } from '../notifications/NotificationContext';

interface OfferDetails {
  id: number;
  title: string;
  description: string;
  budget: string;
  deadline: string;
  status: 'pending' | 'open' | 'closed' | 'in_progress' | 'completed' | 'invited';
  created_at: string;
  client: {
    id: number;
    name: string;
    avatar?: string;
  };
  professional?: {
    id: number;
    name: string;
    avatar?: string;
  };
  applications?: Array<{
    id: number;
    professional_id: number;
    professional_name: string;
    professional_avatar?: string;
    proposal?: string;
    status: string;
    created_at: string;
  }>;
  views_count?: number;
  applications_count?: number;
}

export interface OpenOffer {
  id: number;
  user_id: number;
  title: string;
  categories: string[];
  budget: string;
  deadline: string;
  company: string;
  website: string;
  description: string;
  files: string | null;
  recruitment_type: 'company' | 'freelance' | string;
  open_to_applications: boolean;
  auto_invite: boolean;
  status: 'open' | 'closed' | 'pending' | 'completed' | string;
  views_count: number;
  created_at: string;
  updated_at: string;
  user: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    email_verified_at: string;
    is_professional: boolean;
    created_at: string;
    updated_at: string;
    profile_completed: boolean;
  };
  applications: Application[];
}

export interface Application {
  id: number;
  open_offer_id: number;
  proposal: string | null;
  status: 'pending' | 'accepted' | 'rejected'| 'invited' | string;
  created_at: string;
  updated_at: string;
  professional_profile_id: number;
  freelance_profile: FreelanceProfile;
}

export interface FreelanceProfile {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  avatar: string;
  portfolio_items: any[] | null;
  phone: string;
  address: string;
  city: string;
  country: string;
  bio: string;
  title: string | null;
  expertise: string | null;
  completion_percentage: number;
  profession: string;
  years_of_experience: number;
  hourly_rate: string;
  description: string | null;
  availability_status: 'available' | 'unavailable' | string;
  estimated_response_time: string | null;
  rating: string;
  skills: string[];
  languages: string[];
  services_offered: any[];
  portfolio: PortfolioItem[];
  social_links: any[];
  created_at: string;
  updated_at: string;
  user: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    email_verified_at: string;
    is_professional: boolean;
    created_at: string;
    updated_at: string;
    profile_completed: boolean;
  };
}

export interface PortfolioItem {
  id: string;
  path: string;
  name: string;
  type: string;
  created_at: string;
}

const ClientOfferDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [offer, setOffer] = useState<OfferDetails | null>(null);
  const[offerDetail, setOfferDetail] = useState<OpenOffer | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [showApplications, setShowApplications] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [professionals, setProfessionals] = useState<any[]>([]);
  const [loadingProfessionals, setLoadingProfessionals] = useState(false);
  const [selectedProfessionalId, setSelectedProfessionalId] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [inviteError, setInviteError] = useState<string | null>(null);
  const { addOfferNotification } = useNotifications();

  const token = localStorage.getItem('token');
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  const isClient = currentUser.role === 'client' || !currentUser.is_professional;

  // Rediriger si l'utilisateur n'est pas un client
  useEffect(() => {
    if (!isClient) {
      navigate(`/dashboard/offers/${id}`);
    }
  }, [isClient, id, navigate]);

  useEffect(() => {
    const fetchOfferDetails = async () => {
      if (!token || !id) return;

      setLoading(true);
      try {
        console.log("Récupération des détails de l'offre avec ID:", id);
        const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des détails de l\'appel d\'offre');
        }

        const data = await response.json();
        console.log("Réponse API pour les détails de l'offre:", data);

        // Adapter la structure de l'offre pour correspondre à notre interface
        if (data.open_offer) {
          setOfferDetail(data.open_offer);
          // Si l'API renvoie user au lieu de client, adapter la structure
          if (data.open_offer.user && !data.open_offer.client) {
            data.open_offer.client = {
              id: data.open_offer.user.id,
              name: data.open_offer.user.first_name + ' ' + data.open_offer.user.last_name,
              avatar: data.open_offer.user.avatar,
            };
          }

          setOffer(data.open_offer);
          console.log("Statut de l'offre:", data.open_offer.status);
        } else {
          throw new Error('Structure de réponse API invalide');
        }
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger les détails de l\'appel d\'offre');

        // Utiliser des données de secours
        setOffer({
          id: parseInt(id || '0'),
          title: 'Création d\'un environnement 3D pour jeu mobile',
          description: 'Nous recherchons un artiste 3D pour créer un environnement complet pour notre jeu mobile d\'aventure. Le projet comprend la modélisation, le texturing et l\'optimisation pour mobile.',
          budget: '1 500 €',
          deadline: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString(), // 20 days from now
          status: 'open',
          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
          client: {
            id: currentUser.id,
            name: currentUser.name || 'Client',
            avatar: currentUser.avatar,
          },
          views_count: 15,
          applications_count: 3,
          applications: [
            {
              id: 1,
              professional_id: 201,
              professional_name: 'Jean Dupont',
              professional_avatar: 'https://randomuser.me/api/portraits/men/41.jpg',
              proposal: 'Je suis très intéressé par votre projet. J\'ai 5 ans d\'expérience en modélisation 3D pour jeux mobiles.',
              status: 'pending',
              created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
            },
            {
              id: 2,
              professional_id: 202,
              professional_name: 'Marie Martin',
              professional_avatar: 'https://randomuser.me/api/portraits/women/22.jpg',
              proposal: 'Bonjour, je suis spécialisée dans l\'optimisation 3D pour mobile. Votre projet m\'intéresse beaucoup.',
              status: 'pending',
              created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
            },
          ]
        });
      } finally {
        setLoading(false);
      }
    };

    fetchOfferDetails();
  }, [id, token, currentUser.id, currentUser.name, currentUser.avatar]);

  // Formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  const getStatusBadge = (status: string) => {
        switch (status) {
            case 'pending':
                return <span className="px-3 py-1 rounded-full text-white bg-yellow-500">En attente</span>;
            case 'accepted':
                return <span className="px-3 py-1 rounded-full text-white bg-green-500">Accepté</span>;
            case 'rejected':
                return <span className="px-3 py-1 rounded-full text-white bg-red-500">Refusé</span>;
            case 'invited':
                return <span className="px-3 py-1 rounded-full text-white bg-blue-500">Invité</span>;
            default:
                return <span className="px-3 py-1 rounded-full text-white bg-gray-500">Inconnu</span>;
        }
    };

  // Calculer les jours restants
  const getDaysRemaining = (dateString: string) => {
    const deadline = new Date(dateString);
    const now = new Date();
    const diffTime = deadline.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return 'Délai dépassé';
    } else if (diffDays === 0) {
      return 'Dernier jour';
    } else {
      return `${diffDays} jour${diffDays > 1 ? 's' : ''} restant${diffDays > 1 ? 's' : ''}`;
    }
  };

  // Gérer la clôture de l'appel d'offre
  const handleCloseOffer = async () => {
    if (!token || !id) return;

    try {
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}/close`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la clôture de l\'appel d\'offre');
      }

      window.location.reload();
      // Mettre à jour l'état local
      // setOffer(prev => prev ? { ...prev, status: 'closed' } : null);

      // // Afficher un message de succès
      // setSuccessMessage('Vous avez clôturé cet appel d\'offre.');

      // // Rediriger vers le tableau de bord après un court délai
      // setTimeout(() => {
      //   navigate('/dashboard');
      // }, 2000);
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de clôturer l\'appel d\'offre. Veuillez réessayer plus tard.');
    }
  };

  const handleTerminateOffer = async () => {
    if (!token || !id) return;

    try {
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}/complete`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la clôture de l\'appel d\'offre');
      }

      window.location.reload();
      // Mettre à jour l'état local
      // setOffer(prev => prev ? { ...prev, status: 'closed' } : null);

      // // Afficher un message de succès
      // setSuccessMessage('Vous avez clôturé cet appel d\'offre.');

      // // Rediriger vers le tableau de bord après un court délai
      // setTimeout(() => {
      //   navigate('/dashboard');
      // }, 2000);
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de clôturer l\'appel d\'offre. Veuillez réessayer plus tard.');
    }
  };

  const handleAcceptApplication = async (applicationId: number) => {
        const token = localStorage.getItem("token");
        try {
            const response = await fetch(`${API_BASE_URL}/api/offer-applications/${applicationId}/status`, {
                method: 'PATCH',
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ status: 'accepted' }),
            });
            if (!response.ok) throw new Error("Erreur lors de l'acceptation");
            window.location.reload();
        } catch (error) {
            alert("Échec de l'acceptation de la candidature.");
        }
    };

  // Gérer la sélection d'un professionnel
  const handleSelectProfessional = async (applicationId: number) => {
    if (!token || !id) return;
    console.log("Contenu : ",JSON.stringify({ application_id: applicationId }))
    try {
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}/assign`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ application_id: applicationId }),
      });

      if (!response.ok) {
        console.log("Retour : ",response)
        throw new Error('Erreur lors de la sélection du professionnel');
      }
      // Afficher un message de succès
      setSuccessMessage('Vous avez sélectionné ce professionnel pour votre projet. Il en sera informé.');
      window.location.reload();
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de sélectionner le professionnel. Veuillez réessayer plus tard.');
    }
  };

  // Gérer la modification de l'offre
  const handleEditOffer = () => {
    navigate(`/dashboard/open-offers/${id}?edit=true`);
  };

  // Basculer l'affichage des candidatures
  const toggleApplications = () => {
    setShowApplications(!showApplications);
  };

  // Ouvrir la modal d'invitation
  const handleOpenInviteModal = async () => {
    console.log("Ouverture de la modal d'invitation");
    // alert("Ouverture de la modal d'invitation"); // Ajouter une alerte pour vérifier si la fonction est appelée
    setInviteError(null);
    setSelectedProfessionalId(null);
    setSearchQuery('');
    setShowInviteModal(true);
    await fetchProfessionals();
  };

  // Fermer la modal d'invitation
  const handleCloseInviteModal = () => {
    setShowInviteModal(false);
  };

  // Récupérer la liste des professionnels
  const fetchProfessionals = async () => {
    if (!token) return;

    setLoadingProfessionals(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/professionals`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des professionnels');
      }

      const data = await response.json();
      console.log('Professionnels récupérés:', data);

      // Adapter la structure des données selon la réponse de l'API
      if (data.users) {
        setProfessionals(data.users);
      } else if (data.professionals) {
        setProfessionals(data.professionals);
      } else if (Array.isArray(data)) {
        setProfessionals(data);
      } else {
        setProfessionals([]);
      }
    } catch (err) {
      console.error('Erreur:', err);
      setInviteError('Impossible de récupérer la liste des professionnels');
      // Utiliser des données de secours
      setProfessionals([
        { id: 1, first_name: 'Jean', last_name: 'Dupont', avatar: 'https://randomuser.me/api/portraits/men/41.jpg' },
        { id: 2, first_name: 'Marie', last_name: 'Martin', avatar: 'https://randomuser.me/api/portraits/women/22.jpg' },
        { id: 3, first_name: 'Lucas', last_name: 'Bernard', avatar: 'https://randomuser.me/api/portraits/men/67.jpg' },
      ]);
    } finally {
      setLoadingProfessionals(false);
    }
  };

  const getUrlProlfil = (path : string)  => {
      return path? `${API_BASE_URL}${path}`:'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
  };

  // Filtrer les professionnels en fonction de la recherche
  const filteredProfessionals = professionals.filter(pro => {
    const fullName = `${pro.first_name || ''} ${pro.last_name || ''}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  // Envoyer une invitation à un professionnel
  const handleInviteProfessional = async () => {
    if (!token || !id || !selectedProfessionalId) {
      setInviteError('Veuillez sélectionner un professionnel');
      return;
    }

    try {
       console.log('Liste Pro', filteredProfessionals);
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}/invite`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ professional_id: selectedProfessionalId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de l\'invitation du professionnel');
      }

      const data = await response.json();
      console.log('Réponse de l\'invitation:', data);

      // Fermer la modal et afficher un message de succès
      setShowInviteModal(false);
      setSuccessMessage('Invitation envoyée avec succès au professionnel');

      // Mettre à jour l'état local si nécessaire
      // Par exemple, ajouter l'invitation à la liste des candidatures
      if (data.invitation) {
        setOffer(prev => {
          if (!prev) return null;

          const selectedPro = professionals.find(p => p.id === selectedProfessionalId);
          const newApplication = {
            id: data.invitation.id,
            professional_id: selectedProfessionalId,
            professional_name: selectedPro ? `${selectedPro.first_name} ${selectedPro.last_name}` : 'Professionnel invité',
            professional_avatar: selectedPro?.avatar,
            status: 'invited',
            created_at: new Date().toISOString(),
          };

          return {
            ...prev,
            applications: [...(prev.applications || []), newApplication],
            applications_count: (prev.applications_count || 0) + 1,
          };
        });
      }
    } catch (err) {
      console.error('Erreur:', err);
      setInviteError(err instanceof Error ? err.message : 'Erreur lors de l\'invitation du professionnel');
    }
  };

  return (
    <DashboardLayout
      title="Détails de l'appel d'offre"
      subtitle="Gérez votre appel d'offre et les candidatures reçues"
      actions={
        offer && (
          <div className="flex space-x-3">
            {offer.status === 'open' && (
              <>
                <Button
                  variant="outline"
                  onClick={handleCloseOffer}
                >
                  Clôturer l'appel d'offre
                </Button>
                <Button
                  variant="primary"
                  onClick={handleEditOffer}
                >
                  Modifier l'offre
                </Button>
              </>
            )}
            {offer.status === 'in_progress' && (
              <Button
                variant="primary"
                // onClick={() => navigate(`/dashboard/offers/${id}/complete`)}
                onClick={handleTerminateOffer}
                style={{ backgroundColor: '#28a745', color: 'white' }}
              >
                Marquer comme terminé
              </Button>
            )}
          </div>
        )
      }
    >
      {error && (
        <Alert
          type="error"
          title="Erreur"
          className="mb-6"
        >
          {error}
        </Alert>
      )}

      {successMessage && (
        <Alert
          type="success"
          title="Succès"
          className="mb-6"
        >
          {successMessage}
        </Alert>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      ) : !offer ? (
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-12 text-center">
          <h3 className="text-lg font-medium text-neutral-700 mb-4">Appel d'offre non trouvé</h3>
          <p className="text-neutral-500 mb-6">Cet appel d'offre n'existe pas ou vous n'avez pas les permissions nécessaires pour y accéder.</p>
          <Button
            variant="primary"
            onClick={() => navigate('/dashboard')}
          >
            Retour au tableau de bord
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Détails de l'appel d'offre */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-neutral-200">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-neutral-900">Détails de l'appel d'offre</h3>
                  <Badge
                    color={
                      offer.status === 'pending' ? 'warning' :
                      offer.status === 'open' ? 'info' :
                      offer.status === 'in_progress' ? 'success' :
                      offer.status === 'completed' ? 'success' :
                      'neutral'
                    }
                  >
                    {offer.status === 'pending' ? 'En attente' :
                     offer.status === 'open' ? 'Ouvert' :
                     offer.status === 'in_progress' ? 'En cours' :
                     offer.status === 'completed' ? 'Terminé' :
                     offer.status === 'closed' ? 'Clôturé' :
                     offer.status === 'invited' ? 'Invitation' :
                     'Inconnu'}
                  </Badge>
                </div>
              </div>

              <div className="p-6">
                <h2 className="text-xl font-semibold text-neutral-900 mb-4">{offer.title}</h2>
                <p className="text-neutral-700 mb-6 whitespace-pre-wrap">{offer.description}</p>

                <div className="space-y-4 mb-6">
                  <div className="flex items-center">
                    <DollarSign className="h-5 w-5 text-neutral-500 mr-2" />
                    <span className="text-neutral-700 font-medium">{offer.budget}</span>
                  </div>

                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-neutral-500 mr-2" />
                    <span className="text-neutral-700">Date limite: {formatDate(offer.deadline)}</span>
                  </div>

                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-neutral-500 mr-2" />
                    <span className="text-neutral-700">{getDaysRemaining(offer.deadline)}</span>
                  </div>

                  {offer.professional && (
                    <div className="flex items-center">
                      <User className="h-5 w-5 text-neutral-500 mr-2" />
                      <span className="text-neutral-700">
                        Professionnel sélectionné: {offer.professional.name}
                      </span>
                    </div>
                  )}

                  <div className="flex items-center">
                    <Briefcase className="h-5 w-5 text-neutral-500 mr-2" />
                    <span className="text-neutral-700">Créé le: {formatDate(offer.created_at)}</span>
                  </div>

                  {offer.views_count !== undefined && (
                    <div className="flex items-center">
                      <Users className="h-5 w-5 text-neutral-500 mr-2" />
                      <span className="text-neutral-700">Vues: {offer.views_count}</span>
                    </div>
                  )}

                  {offer.applications_count !== undefined && (
                    <div className="flex items-center">
                      <Users className="h-5 w-5 text-neutral-500 mr-2" />
                      <span className="text-neutral-700">Candidatures: {offer.applications_count}</span>
                    </div>
                  )}
                </div>

                {/* Bouton pour inviter un professionnel */}
                {offer.status === 'open' && (
                  <Button
                    variant="primary"
                    fullWidth
                    onClick={handleOpenInviteModal}
                    className="mb-4"
                  >
                    Inviter un professionnel
                  </Button>
                )}

                {/* Bouton pour voir les candidatures */}
                {offer.status === 'open' && offer.applications && offer.applications.length > 0 && (
                  <Button
                    variant="outline"
                    fullWidth
                    onClick={toggleApplications}
                    className="mb-4"
                  >
                    {showApplications ? 'Masquer les candidatures' : 'Voir les candidatures'}
                  </Button>
                )}
              </div>
            </div>

            {/* Liste des candidatures */}
            {showApplications && offerDetail?.applications && offerDetail.applications.length > 0 && (
              <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden mt-6">
                <div className="px-6 py-4 border-b border-neutral-200">
                  <h3 className="text-lg font-semibold text-neutral-900">Candidatures reçues</h3>
                </div>
                <div className="p-6">
                  <div className="space-y-6">
                    {offerDetail.applications.map((application) => (
                      <div key={application.id} className="border border-neutral-200 rounded-lg p-4">
                        <div className="flex items-center mb-3">
                          <Avatar
                            src={getUrlProlfil(application.freelance_profile.avatar)}
                            fallback={application.freelance_profile.first_name.charAt(0)}
                            size="md"
                            className="mr-3"
                          />
                          <div>
                            <h4 className="font-medium text-neutral-900">{application.freelance_profile.first_name} {application.freelance_profile.last_name} </h4>
                            <p className="text-sm text-neutral-500">Candidature reçue le {formatDate(application.created_at)}</p>
                            {getStatusBadge(application.status)}
                          </div>
                          {/* <div className="mb-4">
                              {getStatusBadge(application.status)}
                            </div> */}
                        </div>
                        {application.proposal && (
                          <div className="mb-4">
                            <p className="text-neutral-700 whitespace-pre-wrap">{application.proposal}</p>
                          </div>
                        )}
                        <div className="flex justify-end space-x-3">
                          
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/professionals/${application.freelance_profile.id}`)}
                          >
                            Voir le profil
                          </Button>

                          {application.status==='pending'&& offerDetail.status === 'open'  &&(
                            <Button
                              variant="primary"
                              size="sm"
                              onClick={()=> handleAcceptApplication(application.id)}
                              // onClick={() => handleSelectProfessional(application.freelance_profile.id)}
                            >
                              Accepter la demande
                            </Button>
                            
                          )}

                          {application.status==='accepted' && offerDetail.status === 'open'  &&(
                            <Button
                              variant="primary"
                              size="sm"
                              onClick={()=> handleSelectProfessional(application.id)}
                              // onClick={() => handleSelectProfessional(application.freelance_profile.id)}
                            >
                              Attribuer
                            </Button>
                          )}
                          
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Panel de discussion */}
          <div className="lg:col-span-2">
            {offer.professional ? (
              <OfferDiscussionPanel
                offerId={offer.id}
                offerTitle={offer.title}
                clientId={offer.client?.id}
                clientName={offer.client?.name || "Client"}
                clientAvatar={offer.client?.avatar}
                professionalId={offer.professional?.id}
                professionalName={offer.professional?.name || "Professionnel"}
                professionalAvatar={offer.professional?.avatar}
                isClient={true}
                onBack={() => navigate(-1)}
              />
            ) : (
              <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-12 text-center h-full flex flex-col justify-center items-center">
                <User className="h-16 w-16 text-neutral-300 mb-4" />
                <h3 className="text-lg font-medium text-neutral-700 mb-2">Aucun professionnel sélectionné</h3>
                <p className="text-neutral-500 mb-6">
                  Sélectionnez un professionnel parmi les candidatures pour commencer la discussion.
                </p>
                {offer.applications && offer.applications.length > 0 ? (
                  <>
                  <Button
                    variant="outline"
                    className="mb-4"
                    onClick={toggleApplications}
                  >
                    Voir les candidatures
                  </Button>
                  
                  <Button
                    variant="outline"
                    className="mb-4"
                    onClick={()=>navigate(`/discussions/${offer.id}`,
                       {
                    state: { offreEncours : offerDetail }
                  })
                }
                  >
                    Mes Conversations
                  </Button>
                  </>
                ) : (
                  <p className="text-neutral-500">
                    Aucune candidature reçue pour le moment.
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Modal d'invitation de professionnels */}
      <Modal
        isOpen={showInviteModal}
        onClose={handleCloseInviteModal}
        title="Inviter un professionnel"
      >
        <div className="p-4">
          {inviteError && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              {inviteError}
            </div>
          )}

          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Rechercher un professionnel..."
                className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="max-h-60 overflow-y-auto mb-4">
            {loadingProfessionals ? (
              <div className="flex justify-center items-center h-20">
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary-600"></div>
              </div>
            ) : filteredProfessionals.length === 0 ? (
              <div className="text-center py-4 text-neutral-500">
                Aucun professionnel trouvé
              </div>
            ) : (
              <div className="space-y-2">
                {filteredProfessionals.map((professional) => (
                  <div
                    key={professional.id}
                    className={`flex items-center p-3 rounded-lg cursor-pointer ${
                      selectedProfessionalId === professional.user_id
                        ? 'bg-primary-50 border border-primary-200'
                        : 'hover:bg-neutral-50 border border-transparent'
                    }`}
                    // onClick={()=> handleAcceptApplication()}
                    onClick={() => setSelectedProfessionalId(professional.user_id)}
                  >
                    <Avatar
                      src={getUrlProlfil(professional.avatar)}
                      fallback={(professional.first_name?.[0] || '') + (professional.last_name?.[0] || '')}
                      size="md"
                      className="mr-3"
                    />
                    <div>
                      <h4 className="font-medium text-neutral-900">
                        {professional.first_name} {professional.last_name}
                      </h4>
                      {professional.title && (
                        <p className="text-sm text-neutral-500">{professional.title}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={handleCloseInviteModal}
            >
              Annuler
            </Button>
            <Button
              variant="primary"
              onClick={handleInviteProfessional}
              disabled={!selectedProfessionalId || loadingProfessionals}
            >
              Inviter
            </Button>
          </div>
        </div>
      </Modal>
    </DashboardLayout>
  );
};

export default ClientOfferDetailsPage;
