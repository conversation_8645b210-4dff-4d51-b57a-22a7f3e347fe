import React, { useState } from 'react';
import { Plus, X } from 'lucide-react';
import type { ProfileFormData } from '../types';

interface SkillsFormProps {
  data: ProfileFormData;
  onChange: (data: Partial<ProfileFormData>) => void;
}

export default function SkillsForm({ data, onChange }: SkillsFormProps) {
  const [newSkill, setNewSkill] = useState({
    name: '',
    level: 5,
  });

  // const handleAddSkill = () => {
  //   if (newSkill.name) {
  //     onChange({
  //       skills: [...data.skills, newSkill],
  //     });
  //     setNewSkill({
  //       name: '',
  //       level: 5,
  //     });
  //   }
  // };

  const handleRemoveSkill = (index: number) => {
    onChange({
      skills: data.skills.filter((_, i) => i !== index),
    });
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">Compétences</h3>
        <div className="space-y-4">
          <div className="flex gap-4">
            <input
              type="text"
              placeholder="Nom de la compétence"
              value={newSkill.name}
              onChange={(e) => setNewSkill({ ...newSkill, name: e.target.value })}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Niveau:</span>
              <input
                type="range"
                min="1"
                max="10"
                value={newSkill.level}
                onChange={(e) => setNewSkill({ ...newSkill, level: parseInt(e.target.value) })}
                className="w-32"
              />
              <span className="text-sm font-medium w-6">{newSkill.level}</span>
            </div>
            <button
              // onClick={handleAddSkill}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* <div className="mt-4 space-y-2">
          {data.skills.map((skill, index) => (
            <div
              key={index}
              className="flex items-center justify-between px-4 py-3 bg-gray-50 rounded-lg"
            >
              <span className="font-medium">{skill.name}</span>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-blue-500"
                      style={{ width: `${(skill.level / 10) * 100}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium w-6">{skill.level}</span>
                </div>
                <button
                  onClick={() => handleRemoveSkill(index)}
                  className="p-1 text-gray-500 hover:text-red-500"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div> */}
      </div>
    </div>
  );
}