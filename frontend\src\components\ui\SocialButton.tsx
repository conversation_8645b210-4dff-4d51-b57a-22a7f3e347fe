import React, { ButtonHTMLAttributes } from 'react';

interface SocialButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  icon: React.ReactNode;
  provider: string;
  fullWidth?: boolean;
}

const SocialButton: React.FC<SocialButtonProps> = ({
  icon,
  provider,
  fullWidth = false,
  className = '',
  ...props
}) => {
  return (
    <button
      type="button"
      className={`
        flex items-center justify-center gap-2
        px-4 py-2 border border-neutral-300 rounded-md
        bg-white text-neutral-800 font-semibold
        hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500
        transition-colors
        ${fullWidth ? 'w-full' : ''}
        ${className}
      `}
      {...props}
    >
      {icon}
      <span>Continue with {provider}</span>
    </button>
  );
};

export default SocialButton;
