import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Swal from 'sweetalert2';
import { API_BASE_URL } from '../config';

const ProfilClient: React.FC = () => {
  const navigate = useNavigate();
  const { user_id } = useParams<{ user_id?: string }>(); // Récupérer l'ID de l'utilisateur depuis l'URL
  const [type, setType] = useState<'particulier' | 'entreprise'>('particulier');
  const [companyName, setCompanyName] = useState('');
  const [industry, setIndustry] = useState('');
  const [description, setDescription] = useState('');

  // Récupérer les données du profil client existant
  useEffect(() => {
    console.log("useEffect déclenché"); // Étape 1
    if (user_id) {
      console.log("user_id===========", user_id); // Étape 2

      const fetchProfile = async () => {
        try {
          const token = localStorage.getItem('token');
          console.log("token===========", token); // Étape 3

          if (!token) {
            throw new Error('Token non trouvé.');
          }

          const response = await fetch(`${API_BASE_URL}/api/profile/client/show/${user_id}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });

          console.log("response===========", response); // Étape 4

          if (!response.ok) {
            throw new Error('Erreur lors de la récupération du profil.');
          }

          const data = await response.json();
          console.log("data===========", data); // Étape 5

          // Pré-remplir les champs du formulaire
          setType(data.profile.type === 'entreprise' ? 'entreprise' : 'particulier');
          setCompanyName(data.profile.company_name || '');
          setIndustry(data.profile.industry || '');
          setDescription(data.profile.description || '');

        } catch (error) {
          console.error('Erreur:', error); // Étape 6
        }
      };

      fetchProfile();
    }
  }, [user_id]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const profileData = {
      type,
      company_name: type === 'entreprise' ? companyName : null,
      industry: type === 'entreprise' ? industry : null,
      description,
    };

    try {
      const url = `${API_BASE_URL}/api/profile/client`; // Utiliser la même URL pour la création et la mise à jour
      const method = 'POST'; // Toujours utiliser POST

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(profileData),
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la sauvegarde du profil.');
      }

      await response.json();

      Swal.fire({
        title: 'Succès!',
        text: user_id ? 'Profil client mis à jour avec succès.' : 'Profil client Enregistrer avec succès.',
        icon: 'success',
        timer: 2000, // L'alerte disparaît après 2 secondes
        showConfirmButton: false, // Supprime le bouton OK
      });

      // Rediriger après 2 secondes
      setTimeout(() => {
        navigate('/lists-independants');
      }, 2000);

    } catch (error) {
      Swal.fire({
        title: 'Erreur!',
        text: user_id ? 'Erreur lors de la mise à jour du profil.' : 'Erreur lors de la création du profil.',
        icon: 'error',
        confirmButtonText: 'OK',
      });

      console.error('Erreur:', error);
    }
  };

  return (
    <>
      <div className="w-full h-full p-6 bg-white rounded-lg">
        <h1 className="text-2xl font-bold mb-4">
          {user_id ? 'Modifier le profil client' : 'Enregistrer un profil client'}
        </h1>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block font-medium">Type de profil</label>
            <select
              value={type}
              onChange={(e) => setType(e.target.value as 'particulier' | 'entreprise')}
              className="w-full p-2 border rounded"
            >
              <option value="particulier">Particulier</option>
              <option value="entreprise">Entreprise</option>
            </select>
          </div>

          {/* Champs spécifiques aux entreprises */}
          {type === 'entreprise' && (
            <>
              <div>
                <label className="block font-medium">Nom de l'entreprise</label>
                <input
                  type="text"
                  value={companyName}
                  onChange={(e) => setCompanyName(e.target.value)}
                  className="w-full p-2 border rounded"
                  placeholder="Nom de l'entreprise"
                />
              </div>

              <div>
                <label className="block font-medium">Secteur d'activité</label>
                <input
                  type="text"
                  value={industry}
                  onChange={(e) => setIndustry(e.target.value)}
                  className="w-full p-2 border rounded"
                  placeholder="Secteur d'activité"
                />
              </div>
            </>
          )}

          {/* Champ Description */}
          <div>
            <label className="block font-medium">Description</label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full p-2 border rounded"
              placeholder="Description du profil"
              rows={4}
            />
          </div>

          {/* Bouton de soumission */}
          <button
            type="submit"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition"
          >
            {user_id ? 'Mettre à jour le profil' : 'Enregistrer le profil'}
          </button>
        </form>
      </div>
    </>
  );
};

export default ProfilClient;