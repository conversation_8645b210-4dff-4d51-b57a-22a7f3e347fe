// import React, { useState, useEffect } from 'react';

// interface Contact {
//   id: number;
//   user_id: number; 
//   name: string; 
//   email: string; 
//   phone: string; 
//   notes: string; 
// //   created_at: string; 
// //   updated_at: string;
// }

// interface CreateContactModalProps {
//   onClose: () => void;
//   onAddContact: (contact: Contact) => void;
//   existingContact?: Contact | null;
// }

// const CreateContactModal: React.FC<CreateContactModalProps> = ({ onClose, onAddContact, existingContact }) => {
//   const [name, setContactName] = useState('');
//   const [email, setContactEmail] = useState('');
//   const [phone, setContactPhone] = useState('');
//   const [notes, setContactNotes] = useState('');
//   const [currentStep, setCurrentStep] = useState(1);

//   useEffect(() => {
//     if (existingContact) {
//       setContactName(existingContact.name);
//       setContactEmail(existingContact.email);
//       setContactPhone(existingContact.phone);
//       setContactNotes(existingContact.notes);
//     }
//   }, [existingContact]);

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault();
//     const newContact: Contact = {
//       id: existingContact ? existingContact.id : Date.now(),
//       user_id: existingContact? existingContact.user_id : Date.now(),
//       name,
//       email, 
//       phone,
//       notes,
//     //   created_at:existingContact ? existingContact.created_at : Date.now(),
//     //   updated_at:existingContact ? existingContact.updated_at : Date.now(),
      
//     };
//     onAddContact(newContact);
//     onClose();
//   };

//     const nextStep = () => {
//     setCurrentStep(prev => prev + 1);
//   };

//   const prevStep = () => {
//     setCurrentStep(prev => prev - 1);
//   };

//   return (
//     <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//       <div className="bg-white p-6 rounded-lg w-full max-w-2xl shadow-lg relative">
//         <button className="absolute top-2 right-2 text-gray-600" onClick={onClose}>
//           ✖
//         </button>
//         <h2 className="text-2xl font-bold mb-4">{existingContact ? 'Modifier le Contact' : 'Nouveau Contact'}</h2>
//         <form onSubmit={handleSubmit} className="space-y-4">
//             {currentStep === 1 && (
//             <>
//           <div>
//             <label className="block mb-1 font-semibold">Nom du Contact</label>
//             <input
//               type="text"
//               value={name}
//               onChange={(e) => setContactName(e.target.value)}
//               className="w-full border px-4 py-2 rounded-lg"
//               placeholder="Entrez le nom du Contact"
//               required
//             />
//           </div>
//           <div>
//             <label className="block mb-1 font-semibold">Email</label>
//             <input
//               type="email"
//               value={email}
//               onChange={(e) => setContactEmail(e.target.value)}
//               className="w-full border px-4 py-2 rounded-lg"
//               placeholder="Email du Contact"
//               required
//             />
//           </div>
//           <div>
//             <label className="block mb-1 font-semibold">Téléphone</label>
//             <input
//               type="phone"
//               value={phone}
//               onChange={(e) => setContactPhone(e.target.value)}
//               className="w-full border px-4 py-2 rounded-lg"
//               placeholder="Téléphone du Contact"
//               required
//             />
//           </div>
          
//           </>
//         )}
//         {currentStep === 2 && (
//         <>
//           <div>
//             <label className="block mb-1 font-semibold">Notes</label>
//             <textarea
//               value={notes}
//               onChange={(e) => setContactNotes(e.target.value)}
//               className="w-full border px-4 py-2 rounded-lg"
//               placeholder="Décrivez votre contact"
//               rows={4}
//               required
//             ></textarea>
//           </div>
//           </>
//         )}
//         <div className="flex justify-between">
//             {currentStep > 1 && (
//                 <button
//                     type="button"
//                     className="bg-gray-500 text-white py-2 px-4 rounded-lg"
//                     onClick={prevStep}
//                 >
//                     Précédent
//                 </button>
//             )}
//             {currentStep < 2 && (
//                 <button
//                     type="button"
//                     className="bg-blue-600 text-white py-2 px-4 rounded-lg"
//                     onClick={nextStep}
//                 >
//                     Suivant
//                 </button>
//             )}
//             {currentStep === 2 && (
//                 <button
//                     type="submit"
//                     className="bg-blue-600 text-white py-2 px-4 rounded-lg"
//                     onClick={() => console.log('Contact Créer')}
//                 >
//                     {existingContact ? 'Mettre à jour le Contact' : 'Créer un Contact'}
//                 </button>
//             )}
//         </div>
          
//         </form>
//       </div>
//     </div>
//   );
// };

// export default CreateContactModal;

import React, { useState, useEffect } from 'react';
import { API_BASE_URL } from '../config';

interface Contact {
  id: number;
  user_id: number; 
  name: string; 
  email: string; 
  phone: string; 
  notes: string; 
}

interface CreateContactModalProps {
  onClose: () => void;
  onAddContact: (contact: Contact) => void;
  existingContact?: Contact | null;
}

const CreateContactModal: React.FC<CreateContactModalProps> = ({ onClose, onAddContact, existingContact }) => {
  const [name, setContactName] = useState('');
  const [email, setContactEmail] = useState('');
  const [phone, setContactPhone] = useState('');
  const [notes, setContactNotes] = useState('');
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (existingContact) {
      setContactName(existingContact.name);
      setContactEmail(existingContact.email);
      setContactPhone(existingContact.phone);
      setContactNotes(existingContact.notes);
    }
  }, [existingContact]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const token = localStorage.getItem('token');
    if (!token) {
      console.error('Token d\'authentification manquant.');
      return;
    }

    const url = existingContact
      ? `${API_BASE_URL}/api/contacts/${existingContact.id}`
      : `${API_BASE_URL}/api/contacts`;

    const method = existingContact ? 'PATCH' : 'POST';

    const payload = {
      name,
      email,
      phone,
      notes,
    };

    setIsLoading(true);
    try {
      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (response.ok) {
        console.log(existingContact ? 'Contact mis à jour avec succès' : 'Contact créé avec succès');
        onAddContact(data.contact);
        onClose();
      } else {
        console.error('Erreur lors de la sauvegarde du contact', data);
      }
    } catch (error) {
      console.error('Erreur lors de la communication avec l\'API', error);
    } finally {
      setIsLoading(false);
    }
  };

  const nextStep = () => {
    setCurrentStep((prev) => prev + 1);
  };

  const prevStep = () => {
    setCurrentStep((prev) => prev - 1);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg w-full max-w-2xl shadow-lg relative">
        <button className="absolute top-2 right-2 text-gray-600" onClick={onClose}>
          ✖
        </button>
        <h2 className="text-2xl font-bold mb-4">{existingContact ? 'Modifier le Contact' : 'Nouveau Contact'}</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          {currentStep === 1 && (
            <>
              <div>
                <label className="block mb-1 font-semibold">Nom du Contact</label>
                <input
                  type="text"
                  value={name}
                  onChange={(e) => setContactName(e.target.value)}
                  className="w-full border px-4 py-2 rounded-lg"
                  placeholder="Entrez le nom du Contact"
                  required
                />
              </div>
              <div>
                <label className="block mb-1 font-semibold">Email</label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setContactEmail(e.target.value)}
                  className="w-full border px-4 py-2 rounded-lg"
                  placeholder="Email du Contact"
                  required
                />
              </div>
              <div>
                <label className="block mb-1 font-semibold">Téléphone</label>
                <input
                  type="phone"
                  value={phone}
                  onChange={(e) => setContactPhone(e.target.value)}
                  className="w-full border px-4 py-2 rounded-lg"
                  placeholder="Téléphone du Contact"
                  required
                />
              </div>
            </>
          )}
          {currentStep === 2 && (
            <>
              <div>
                <label className="block mb-1 font-semibold">Notes</label>
                <textarea
                  value={notes}
                  onChange={(e) => setContactNotes(e.target.value)}
                  className="w-full border px-4 py-2 rounded-lg"
                  placeholder="Décrivez votre contact"
                  rows={4}
                  required
                ></textarea>
              </div>
            </>
          )}
          <div className="flex justify-between">
            {currentStep > 1 && (
              <button
                type="button"
                className="bg-gray-500 text-white py-2 px-4 rounded-lg"
                onClick={prevStep}
              >
                Précédent
              </button>
            )}
            {currentStep < 2 && (
              <button
                type="button"
                className="bg-blue-600 text-white py-2 px-4 rounded-lg"
                onClick={nextStep}
              >
                Suivant
              </button>
            )}
            {currentStep === 2 && (
              <button
                type="submit"
                className="bg-blue-600 text-white py-2 px-4 rounded-lg"
                disabled={isLoading}
              >
                {isLoading ? 'Enregistrement...' : existingContact ? 'Mettre à jour le Contact' : 'Créer un Contact'}
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateContactModal;


