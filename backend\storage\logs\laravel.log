[2025-06-23 06:35:11] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) (Connection: mysql, SQL: select * from `service_offers` order by `service_offers`.`id` asc limit 500) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) (Connection: mysql, SQL: select * from `service_offers` order by `service_offers`.`id` asc limit 500) at /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#2 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#3 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(739): Illuminate\\Database\\Query\\Builder->get()
#7 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(162): Illuminate\\Database\\Eloquent\\Builder->get()
#9 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(116): Illuminate\\Database\\Eloquent\\Builder->orderedChunkById()
#10 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/scout/src/SearchableScope.php(37): Illuminate\\Database\\Eloquent\\Builder->chunkById()
#11 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1964): Laravel\\Scout\\SearchableScope->Laravel\\Scout\\{closure}()
#12 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/scout/src/Searchable.php(189): Illuminate\\Database\\Eloquent\\Builder->__call()
#13 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/scout/src/Console/ImportCommand.php(47): App\\Models\\ServiceOffer::makeAllSearchable()
#14 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Scout\\Console\\ImportCommand->handle()
#15 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#17 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#18 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#19 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#20 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#21 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#22 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#23 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#24 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#25 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#26 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle()
#27 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) at /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct()
#1 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func()
#6 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect()
#9 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#10 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#11 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#12 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#13 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#16 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(739): Illuminate\\Database\\Query\\Builder->get()
#17 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels()
#18 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(162): Illuminate\\Database\\Eloquent\\Builder->get()
#19 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(116): Illuminate\\Database\\Eloquent\\Builder->orderedChunkById()
#20 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/scout/src/SearchableScope.php(37): Illuminate\\Database\\Eloquent\\Builder->chunkById()
#21 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1964): Laravel\\Scout\\SearchableScope->Laravel\\Scout\\{closure}()
#22 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/scout/src/Searchable.php(189): Illuminate\\Database\\Eloquent\\Builder->__call()
#23 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/scout/src/Console/ImportCommand.php(47): App\\Models\\ServiceOffer::makeAllSearchable()
#24 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Scout\\Console\\ImportCommand->handle()
#25 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#27 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#28 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#29 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#30 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#31 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#32 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#33 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#34 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#35 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#36 /home/<USER>/Documents/AgenceWeb/Projets/Projet Wassim/Développement/23juin/hi3d/backend/artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle()
#37 {main}
"} 
[2025-06-28 16:21:29] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails (Connection: mysql, SQL: drop table if exists `users`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails (Connection: mysql, SQL: drop table if exists `users`) at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('drop table if e...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('drop table if e...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('drop table if e...')
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(484): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->dropIfExists('users')
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\database\\migrations\\2014_10_12_000000_create_users_table.php(34): Illuminate\\Support\\Facades\\Facade::__callStatic('dropIfExists', Array)
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->down()
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(393): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2014_10_12_0000...', Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(393): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2014_10_12_0000...', Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(312): Illuminate\\Database\\Migrations\\Migrator->runDown('C:\\\\Users\\\\<USER>\\\\...', Object(stdClass), false)
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(367): Illuminate\\Database\\Migrations\\Migrator->rollbackMigrations(Array, Array, Array)
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(343): Illuminate\\Database\\Migrations\\Migrator->resetMigrations(Array, Array, false)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(66): Illuminate\\Database\\Migrations\\Migrator->reset(Array, false)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\ResetCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(58): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\ResetCommand->handle()
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate:reset', Array, Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(109): Illuminate\\Console\\Command->call('migrate:reset', Array)
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(55): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->runReset(NULL, Array)
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->handle()
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('drop table if e...', Array)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('drop table if e...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('drop table if e...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('drop table if e...')
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(484): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->dropIfExists('users')
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\database\\migrations\\2014_10_12_000000_create_users_table.php(34): Illuminate\\Support\\Facades\\Facade::__callStatic('dropIfExists', Array)
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->down()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(393): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2014_10_12_0000...', Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(393): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2014_10_12_0000...', Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(312): Illuminate\\Database\\Migrations\\Migrator->runDown('C:\\\\Users\\\\<USER>\\\\...', Object(stdClass), false)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(367): Illuminate\\Database\\Migrations\\Migrator->rollbackMigrations(Array, Array, Array)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(343): Illuminate\\Database\\Migrations\\Migrator->resetMigrations(Array, Array, false)
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(66): Illuminate\\Database\\Migrations\\Migrator->reset(Array, false)
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\ResetCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(58): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\ResetCommand->handle()
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate:reset', Array, Object(Illuminate\\Console\\OutputStyle))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(109): Illuminate\\Console\\Command->call('migrate:reset', Array)
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(55): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->runReset(NULL, Array)
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->handle()
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 {main}
"} 
[2025-06-28 16:26:10] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'open_offers' already exists (Connection: mysql, SQL: create table `open_offers` (`id` bigint unsigned not null auto_increment primary key, `user_id` bigint unsigned not null, `title` varchar(255) not null, `categories` json null, `filters` json null, `budget` decimal(10, 2) null, `deadline` date null, `company` varchar(255) null, `website` varchar(255) null, `description` text not null, `files` json null, `recruitment_type` varchar(255) null, `open_to_applications` tinyint(1) not null default '1', `auto_invite` tinyint(1) not null default '0', `status` varchar(255) not null default 'active', `views_count` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'open_offers' already exists (Connection: mysql, SQL: create table `open_offers` (`id` bigint unsigned not null auto_increment primary key, `user_id` bigint unsigned not null, `title` varchar(255) not null, `categories` json null, `filters` json null, `budget` decimal(10, 2) null, `deadline` date null, `company` varchar(255) null, `website` varchar(255) null, `description` text not null, `files` json null, `recruitment_type` varchar(255) null, `open_to_applications` tinyint(1) not null default '1', `auto_invite` tinyint(1) not null default '0', `status` varchar(255) not null default 'active', `views_count` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create table `o...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('create table `o...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table `o...')
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('open_offers', Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\database\\migrations\\2024_01_01_000005_create_open_offers_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2024_01_01_0000...', Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_01_0000...', Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\...', 2, false)
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(61): Illuminate\\Console\\Command->call('migrate', Array)
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->handle()
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'open_offers' already exists at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `o...', Array)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create table `o...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('create table `o...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table `o...')
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('open_offers', Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\database\\migrations\\2024_01_01_000005_create_open_offers_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2024_01_01_0000...', Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_01_0000...', Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\...', 2, false)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(61): Illuminate\\Console\\Command->call('migrate', Array)
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->handle()
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 {main}
"} 
[2025-06-28 16:28:24] local.ERROR: PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR, expecting T_VARIABLE on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR, expecting T_VARIABLE on line 1 at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php echo impl...', false)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('echo implode('\\\\...', true)
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('echo implode('\\\\...', true)
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('echo implode('\\\\...')
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 {main}
"} 
[2025-06-28 16:32:11] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails (Connection: mysql, SQL: drop table if exists `users`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails (Connection: mysql, SQL: drop table if exists `users`) at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('drop table if e...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('drop table if e...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('drop table if e...')
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(484): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->dropIfExists('users')
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\database\\migrations\\2014_10_12_000000_create_users_table.php(34): Illuminate\\Support\\Facades\\Facade::__callStatic('dropIfExists', Array)
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->down()
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(393): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2014_10_12_0000...', Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(393): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2014_10_12_0000...', Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(312): Illuminate\\Database\\Migrations\\Migrator->runDown('C:\\\\Users\\\\<USER>\\\\...', Object(stdClass), false)
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(367): Illuminate\\Database\\Migrations\\Migrator->rollbackMigrations(Array, Array, Array)
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(343): Illuminate\\Database\\Migrations\\Migrator->resetMigrations(Array, Array, false)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(66): Illuminate\\Database\\Migrations\\Migrator->reset(Array, false)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\ResetCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(58): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\ResetCommand->handle()
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\ResetCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('drop table if e...', Array)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('drop table if e...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('drop table if e...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('drop table if e...')
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(484): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->dropIfExists('users')
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\database\\migrations\\2014_10_12_000000_create_users_table.php(34): Illuminate\\Support\\Facades\\Facade::__callStatic('dropIfExists', Array)
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->down()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(393): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2014_10_12_0000...', Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(393): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2014_10_12_0000...', Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(312): Illuminate\\Database\\Migrations\\Migrator->runDown('C:\\\\Users\\\\<USER>\\\\...', Object(stdClass), false)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(367): Illuminate\\Database\\Migrations\\Migrator->rollbackMigrations(Array, Array, Array)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(343): Illuminate\\Database\\Migrations\\Migrator->resetMigrations(Array, Array, false)
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(66): Illuminate\\Database\\Migrations\\Migrator->reset(Array, false)
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\ResetCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(58): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\ResetCommand->handle()
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\ResetCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 {main}
"} 
[2025-06-28 16:34:05] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'open_offers' already exists (Connection: mysql, SQL: create table `open_offers` (`id` bigint unsigned not null auto_increment primary key, `user_id` bigint unsigned not null, `title` varchar(255) not null, `categories` json null, `filters` json null, `budget` decimal(10, 2) null, `deadline` date null, `company` varchar(255) null, `website` varchar(255) null, `description` text not null, `files` json null, `recruitment_type` varchar(255) null, `open_to_applications` tinyint(1) not null default '1', `auto_invite` tinyint(1) not null default '0', `status` varchar(255) not null default 'active', `views_count` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'open_offers' already exists (Connection: mysql, SQL: create table `open_offers` (`id` bigint unsigned not null auto_increment primary key, `user_id` bigint unsigned not null, `title` varchar(255) not null, `categories` json null, `filters` json null, `budget` decimal(10, 2) null, `deadline` date null, `company` varchar(255) null, `website` varchar(255) null, `description` text not null, `files` json null, `recruitment_type` varchar(255) null, `open_to_applications` tinyint(1) not null default '1', `auto_invite` tinyint(1) not null default '0', `status` varchar(255) not null default 'active', `views_count` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create table `o...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('create table `o...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table `o...')
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('open_offers', Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\database\\migrations\\2024_01_01_000005_create_open_offers_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2024_01_01_0000...', Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_01_0000...', Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\...', 2, false)
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(53): Illuminate\\Console\\Command->call('migrate', Array)
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'open_offers' already exists at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `o...', Array)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create table `o...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('create table `o...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table `o...')
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('open_offers', Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\database\\migrations\\2024_01_01_000005_create_open_offers_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2024_01_01_0000...', Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_01_0000...', Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\...', 2, false)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(53): Illuminate\\Console\\Command->call('migrate', Array)
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 {main}
"} 
[2025-06-28 16:50:07] local.ERROR: could not find driver (Connection: pgsql, SQL: select * from information_schema.tables where table_catalog = postgres and table_schema = public and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: pgsql, SQL: select * from information_schema.tables where table_catalog = postgres and table_schema = public and table_name = migrations and table_type = 'BASE TABLE') at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select * from i...', Array, false)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(51): Illuminate\\Database\\Connection->selectFromWriteConnection('select * from i...', Array)
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(52): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('pgsql:host=loca...', 'postgres', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=loca...', 'postgres', '123456789', Array)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(35): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=loca...', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select * from i...', Array, false)
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(51): Illuminate\\Database\\Connection->selectFromWriteConnection('select * from i...', Array)
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(52): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 {main}
"} 
[2025-06-28 17:03:51] local.ERROR: could not find driver (Connection: pgsql, SQL: select * from information_schema.tables where table_catalog = postgres and table_schema = public and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: pgsql, SQL: select * from information_schema.tables where table_catalog = postgres and table_schema = public and table_name = migrations and table_type = 'BASE TABLE') at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select * from i...', Array, false)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(51): Illuminate\\Database\\Connection->selectFromWriteConnection('select * from i...', Array)
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(52): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('pgsql:host=loca...', 'postgres', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=loca...', 'postgres', '123456789', Array)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(35): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=loca...', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select * from i...', Array, false)
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(51): Illuminate\\Database\\Connection->selectFromWriteConnection('select * from i...', Array)
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(52): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 {main}
"} 
[2025-06-28 17:18:59] local.ERROR: could not find driver (Connection: pgsql, SQL: select * from information_schema.tables where table_catalog = postgres and table_schema = public and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: pgsql, SQL: select * from information_schema.tables where table_catalog = postgres and table_schema = public and table_name = migrations and table_type = 'BASE TABLE') at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select * from i...', Array, false)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(51): Illuminate\\Database\\Connection->selectFromWriteConnection('select * from i...', Array)
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): retry(1, Object(Closure), 0, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('pgsql:host=loca...', 'postgres', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=loca...', 'postgres', '123456789', Array)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(35): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=loca...', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select * from i...', Array, false)
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(51): Illuminate\\Database\\Connection->selectFromWriteConnection('select * from i...', Array)
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): retry(1, Object(Closure), 0, Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 {main}
"} 
[2025-06-28 18:04:57] local.ERROR: could not find driver (Connection: pgsql, SQL: select * from information_schema.tables where table_catalog = postgres and table_schema = public and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: pgsql, SQL: select * from information_schema.tables where table_catalog = postgres and table_schema = public and table_name = migrations and table_type = 'BASE TABLE') at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select * from i...', Array, false)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(51): Illuminate\\Database\\Connection->selectFromWriteConnection('select * from i...', Array)
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(52): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('pgsql:host=loca...', 'postgres', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=loca...', 'postgres', '123456789', Array)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(35): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=loca...', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select * from i...', Array, false)
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(51): Illuminate\\Database\\Connection->selectFromWriteConnection('select * from i...', Array)
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(52): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 {main}
"} 
[2025-06-28 18:17:56] local.ERROR: Unexpected end of input {"exception":"[object] (InvalidArgumentException(code: 0): Unexpected end of input at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\psy\\psysh\\src\\Shell.php:891)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('echo 'Tables: '...', true)
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('echo 'Tables: '...')
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}
"} 
[2025-06-28 18:43:12] local.ERROR: Erreur lors du filtrage des professionnels: SQLSTATE[42883]: Undefined function: 7 ERREUR:  la fonction lower(json) n'existe pas
LINE 1: ...t like $2 or "title"::text like $3 or JSON_SEARCH(LOWER(skil...
                                                             ^
HINT:  Aucune fonction ne correspond au nom donné et aux types d'arguments.
Vous devez ajouter des conversions explicites de type. (Connection: pgsql, SQL: select * from "professional_profiles" where ("first_name"::text like %test% or "last_name"::text like %test% or "title"::text like %test% or JSON_SEARCH(LOWER(skills), 'one', LOWER(%test%)) IS NOT NULL) order by "created_at" desc)  
[2025-06-28 18:43:14] local.ERROR: Erreur lors du filtrage des professionnels: SQLSTATE[42883]: Undefined function: 7 ERREUR:  la fonction lower(json) n'existe pas
LINE 1: ...t like $2 or "title"::text like $3 or JSON_SEARCH(LOWER(skil...
                                                             ^
HINT:  Aucune fonction ne correspond au nom donné et aux types d'arguments.
Vous devez ajouter des conversions explicites de type. (Connection: pgsql, SQL: select * from "professional_profiles" where ("first_name"::text like %test% or "last_name"::text like %test% or "title"::text like %test% or JSON_SEARCH(LOWER(skills), 'one', LOWER(%test%)) IS NOT NULL) order by "created_at" desc)  
[2025-06-28 18:43:16] local.ERROR: Erreur lors du filtrage des professionnels: SQLSTATE[42883]: Undefined function: 7 ERREUR:  la fonction lower(json) n'existe pas
LINE 1: ...t like $2 or "title"::text like $3 or JSON_SEARCH(LOWER(skil...
                                                             ^
HINT:  Aucune fonction ne correspond au nom donné et aux types d'arguments.
Vous devez ajouter des conversions explicites de type. (Connection: pgsql, SQL: select * from "professional_profiles" where ("first_name"::text like %test% or "last_name"::text like %test% or "title"::text like %test% or JSON_SEARCH(LOWER(skills), 'one', LOWER(%test%)) IS NOT NULL) order by "created_at" desc)  
[2025-06-28 18:43:57] local.INFO: setSkillsAttribute appelé avec: "[]" (type: string)  
[2025-06-28 18:43:57] local.INFO: Skills encodé après décodage: []  
[2025-06-28 18:43:57] local.INFO: setLanguagesAttribute appelé avec: "[]" (type: string)  
[2025-06-28 18:43:57] local.INFO: Languages encodé après décodage: []  
[2025-06-28 18:43:57] local.INFO: setServicesOfferedAttribute appelé avec: "[]" (type: string)  
[2025-06-28 18:43:57] local.INFO: Services_offered encodé après décodage: []  
[2025-06-28 18:43:58] local.INFO: Tentative d'envoi d'e-mail de vérification à <EMAIL>  
[2025-06-28 18:44:15] local.DEBUG: From: Laravel <<EMAIL>>
To: <EMAIL>
Subject: =?utf-8?Q?V=C3=A9rification?= de votre adresse e-mail
MIME-Version: 1.0
Date: Sat, 28 Jun 2025 18:44:14 +0000
Message-ID: <<EMAIL>>
Content-Type: multipart/alternative; boundary=YcPjF0X2

--YcPjF0X2
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: quoted-printable

Laravel: http://localhost

# Vérifiez votre adresse e-mail

Cliquez sur le bouton ci-dessous pour vérifier votre adresse e-mail.

Vérifier l'e-mail: http://127.0.0.1:8000/api/email/verify/1/2aa3d2fe661baa28ae3d9b8813e27f679cade1a0?expires=1751139838&redirect=http%253A%252F%252Flocalhost%253A3000%252Flogin%253Fverified%253Dtrue&signature=49af518ab477170e6cd2753312400942b97b3c849a68d4a9943facae615fab93

Si vous n'avez pas créé de compte, aucune action n'est requise.

Merci,
Laravel

© 2025 Laravel. All rights reserved.

--YcPjF0X2
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>Laravel</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="color-scheme" content="light">
<meta name="supported-color-schemes" content="light">
<style>
@media only screen and (max-width: 600px) {
.inner-body {
width: 100% !important;
}

.footer {
width: 100% !important;
}
}

@media only screen and (max-width: 500px) {
.button {
width: 100% !important;
}
}
</style>
</head>
<body style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -webkit-text-size-adjust: none; background-color: #ffffff; color: #718096; height: 100%; line-height: 1.4; margin: 0; padding: 0; width: 100% !important;">

<table class="wrapper" width="100%" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%; background-color: #edf2f7; margin: 0; padding: 0; width: 100%;">
<tr>
<td align="center" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<table class="content" width="100%" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%; margin: 0; padding: 0; width: 100%;">
<tr>
<td class="header" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; padding: 25px 0; text-align: center;">
<a href="http://localhost" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; color: #3d4852; font-size: 19px; font-weight: bold; text-decoration: none; display: inline-block;">
<img src="https://laravel.com/img/notification-logo.png" class="logo" alt="Laravel Logo" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; max-width: 100%; border: none; height: 75px; max-height: 75px; width: 75px;">
</a>
</td>
</tr>

<!-- Email Body -->
<tr>
<td class="body" width="100%" cellpadding="0" cellspacing="0" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%; background-color: #edf2f7; border-bottom: 1px solid #edf2f7; border-top: 1px solid #edf2f7; margin: 0; padding: 0; width: 100%; border: hidden !important;">
<table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 570px; background-color: #ffffff; border-color: #e8e5ef; border-radius: 2px; border-width: 1px; box-shadow: 0 2px 0 rgba(0, 0, 150, 0.025), 2px 4px 0 rgba(0, 0, 150, 0.015); margin: 0 auto; padding: 0; width: 570px;">
<!-- Body content -->
<tr>
<td class="content-cell" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; max-width: 100vw; padding: 32px;">
<h1 style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; color: #3d4852; font-size: 18px; font-weight: bold; margin-top: 0; text-align: left;">Vérifiez votre adresse e-mail</h1>
<p style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; font-size: 16px; line-height: 1.5em; margin-top: 0; text-align: left;">Cliquez sur le bouton ci-dessous pour vérifier votre adresse e-mail.</p>
<table class="action" align="center" width="100%" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%; margin: 30px auto; padding: 0; text-align: center; width: 100%;">
<tr>
<td align="center" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<tr>
<td align="center" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<table border="0" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<tr>
<td style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<a href="http://127.0.0.1:8000/api/email/verify/1/2aa3d2fe661baa28ae3d9b8813e27f679cade1a0?expires=1751139838&amp;redirect=http%253A%252F%252Flocalhost%253A3000%252Flogin%253Fverified%253Dtrue&amp;signature=49af518ab477170e6cd2753312400942b97b3c849a68d4a9943facae615fab93" class="button button-primary" target="_blank" rel="noopener" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -webkit-text-size-adjust: none; border-radius: 4px; color: #fff; display: inline-block; overflow: hidden; text-decoration: none; background-color: #2d3748; border-bottom: 8px solid #2d3748; border-left: 18px solid #2d3748; border-right: 18px solid #2d3748; border-top: 8px solid #2d3748;">Vérifier l'e-mail</a>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
<p style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; font-size: 16px; line-height: 1.5em; margin-top: 0; text-align: left;">Si vous n'avez pas créé de compte, aucune action n'est requise.</p>
<p style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; font-size: 16px; line-height: 1.5em; margin-top: 0; text-align: left;">Merci,
Laravel</p>



</td>
</tr>
</table>
</td>
</tr>

<tr>
<td style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<table class="footer" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 570px; margin: 0 auto; padding: 0; text-align: center; width: 570px;">
<tr>
<td class="content-cell" align="center" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; max-width: 100vw; padding: 32px;">
<p style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; line-height: 1.5em; margin-top: 0; color: #b0adc5; font-size: 12px; text-align: center;">© 2025 Laravel. All rights reserved.</p>

</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</body>
</html>
--YcPjF0X2--
  
[2025-06-28 18:44:15] local.INFO: E-mail de vérification envoyé avec succès à <EMAIL>  
[2025-06-28 18:48:13] local.INFO: Tentative de connexion pour l'utilisateur: <EMAIL>  
[2025-06-28 18:48:14] local.INFO: Tentative de connexion avec un email non vérifié: <EMAIL>  
[2025-07-01 04:40:16] local.INFO: setSkillsAttribute appelé avec: "[]" (type: string)  
[2025-07-01 04:40:16] local.INFO: Skills encodé après décodage: []  
[2025-07-01 04:40:16] local.INFO: setLanguagesAttribute appelé avec: "[]" (type: string)  
[2025-07-01 04:40:16] local.INFO: Languages encodé après décodage: []  
[2025-07-01 04:40:16] local.INFO: setServicesOfferedAttribute appelé avec: "[]" (type: string)  
[2025-07-01 04:40:16] local.INFO: Services_offered encodé après décodage: []  
[2025-07-01 04:40:16] local.INFO: Tentative d'envoi d'e-mail de vérification à <EMAIL>  
[2025-07-01 04:41:10] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php:81)
[stacktrace]
#0 {main}
"} 
[2025-07-01 06:01:07] local.ERROR: SQLSTATE[42501]: Insufficient privilege: 7 ERREUR:  droit refusé pour le schéma public at character 14 (Connection: pgsql, SQL: create table "migrations" ("id" serial not null primary key, "migration" varchar(255) not null, "batch" integer not null)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42501): SQLSTATE[42501]: Insufficient privilege: 7 ERREUR:  droit refusé pour le schéma public at character 14 (Connection: pgsql, SQL: create table \"migrations\" (\"id\" serial not null primary key, \"migration\" varchar(255) not null, \"batch\" integer not null)) at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create table \"m...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('create table \"m...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table \"m...')
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Schema\\Grammars\\PostgresGrammar))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(165): Illuminate\\Database\\Schema\\Builder->create('migrations', Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\InstallCommand.php(54): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->createRepository()
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\InstallCommand->handle()
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Symfony\\Component\\Console\\Output\\NullOutput))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(40): Illuminate\\Console\\Command->runCommand('migrate:install', Array, Object(Symfony\\Component\\Console\\Output\\NullOutput))
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(120): Illuminate\\Console\\Command->callSilent('migrate:install', Array)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render('Creating migrat...', Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(119): Illuminate\\Console\\View\\Components\\Factory->__call('task', Array)
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 {main}

[previous exception] [object] (PDOException(code: 42501): SQLSTATE[42501]: Insufficient privilege: 7 ERREUR:  droit refusé pour le schéma public at character 14 at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table \"m...', Array)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create table \"m...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('create table \"m...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table \"m...')
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Schema\\Grammars\\PostgresGrammar))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(165): Illuminate\\Database\\Schema\\Builder->create('migrations', Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\InstallCommand.php(54): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->createRepository()
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\InstallCommand->handle()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Symfony\\Component\\Console\\Output\\NullOutput))
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(40): Illuminate\\Console\\Command->runCommand('migrate:install', Array, Object(Symfony\\Component\\Console\\Output\\NullOutput))
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(120): Illuminate\\Console\\Command->callSilent('migrate:install', Array)
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render('Creating migrat...', Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(119): Illuminate\\Console\\View\\Components\\Factory->__call('task', Array)
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 {main}
"} 
[2025-07-01 06:12:28] local.INFO: setSkillsAttribute appelé avec: "[]" (type: string)  
[2025-07-01 06:12:28] local.INFO: Skills encodé après décodage: []  
[2025-07-01 06:12:28] local.INFO: setLanguagesAttribute appelé avec: "[]" (type: string)  
[2025-07-01 06:12:28] local.INFO: Languages encodé après décodage: []  
[2025-07-01 06:12:28] local.INFO: setServicesOfferedAttribute appelé avec: "[]" (type: string)  
[2025-07-01 06:12:28] local.INFO: Services_offered encodé après décodage: []  
[2025-07-01 06:12:28] local.INFO: Tentative d'envoi d'e-mail de vérification à <EMAIL>  
[2025-07-01 06:12:34] local.INFO: E-mail de vérification envoyé avec succès à <EMAIL>  
[2025-07-01 06:13:14] local.INFO: Tentative de connexion pour l'utilisateur: <EMAIL>  
[2025-07-01 06:13:15] local.INFO: Connexion réussie pour l'utilisateur: <EMAIL>  
[2025-07-01 06:13:18] local.INFO: getLanguagesAttribute appelé avec: [] (type: string)  
[2025-07-01 06:13:18] local.INFO: getServicesOfferedAttribute appelé avec: [] (type: string)  
[2025-07-01 06:13:18] local.INFO: getLanguagesAttribute appelé avec: [] (type: string)  
[2025-07-01 06:13:18] local.INFO: getServicesOfferedAttribute appelé avec: [] (type: string)  
[2025-07-01 06:13:19] local.INFO: getLanguagesAttribute appelé avec: [] (type: string)  
[2025-07-01 06:13:19] local.INFO: getServicesOfferedAttribute appelé avec: [] (type: string)  
[2025-07-01 06:13:19] local.INFO: getLanguagesAttribute appelé avec: [] (type: string)  
[2025-07-01 06:13:19] local.INFO: getServicesOfferedAttribute appelé avec: [] (type: string)  
[2025-07-01 06:13:20] local.INFO: getLanguagesAttribute appelé avec: [] (type: string)  
[2025-07-01 06:13:20] local.INFO: getServicesOfferedAttribute appelé avec: [] (type: string)  
[2025-07-01 06:13:20] local.INFO: getLanguagesAttribute appelé avec: [] (type: string)  
[2025-07-01 06:13:20] local.INFO: getServicesOfferedAttribute appelé avec: [] (type: string)  
[2025-07-01 06:15:30] local.INFO: Début de la complétion du profil utilisateur  
[2025-07-01 06:15:30] local.INFO: Données reçues: {"first_name":"ricky","last_name":"treha","phone":"0329591929","address":"Lot 05 E cité ouvrière","city":"Paris","country":"France","bio":"My bio","skills":"[\"3ds Max\",\"A-Frame\"]","is_completion":"true"} 
[2025-07-01 06:15:30] local.INFO: Utilisateur authentifié ID: 1, Type: Professionnel  
[2025-07-01 06:15:30] local.INFO: Profil professionnel existant trouvé avec ID: 1  
[2025-07-01 06:15:30] local.INFO: Compétences décodées depuis JSON: ["3ds Max","A-Frame"] 
[2025-07-01 06:15:31] local.INFO: Portfolio mis à jour avec 1 nouveaux éléments  
[2025-07-01 06:15:31] local.INFO: getLanguagesAttribute appelé avec: [] (type: string)  
[2025-07-01 06:15:31] local.INFO: getServicesOfferedAttribute appelé avec: [] (type: string)  
[2025-07-01 06:15:31] local.INFO: Pourcentage de complétion calculé: 71  
[2025-07-01 06:15:31] local.INFO: Données à mettre à jour: {"first_name":"ricky","last_name":"treha","phone":"0329591929","address":"Lot 05 E cité ouvrière","city":"Paris","country":"France","bio":"My bio","skills":["3ds Max","A-Frame"],"portfolio":[{"id":"68637d0392a37","path":"/storage/portfolio/1751350530_Ajouter un Service.png","name":"Ajouter un Service.png","type":"image/png","created_at":"2025-07-01 06:15:31"}],"completion_percentage":71} 
[2025-07-01 06:15:31] local.INFO: setSkillsAttribute appelé avec: ["3ds Max","A-Frame"] (type: array)  
[2025-07-01 06:15:31] local.INFO: Skills encodé directement: ["3ds Max","A-Frame"]  
[2025-07-01 06:15:31] local.INFO: Informations de base de l'utilisateur mises à jour  
[2025-07-01 06:15:31] local.INFO: Profil marqué comme complété  
[2025-07-01 06:15:31] local.INFO: getLanguagesAttribute appelé avec: [] (type: string)  
[2025-07-01 06:15:31] local.INFO: getServicesOfferedAttribute appelé avec: [] (type: string)  
[2025-07-01 06:15:34] local.INFO: getLanguagesAttribute appelé avec: [] (type: string)  
[2025-07-01 06:15:34] local.INFO: getServicesOfferedAttribute appelé avec: [] (type: string)  
[2025-07-01 06:15:42] local.INFO: getLanguagesAttribute appelé avec: [] (type: string)  
[2025-07-01 06:15:42] local.INFO: getServicesOfferedAttribute appelé avec: [] (type: string)  
[2025-07-01 06:15:42] local.INFO: getLanguagesAttribute appelé avec: [] (type: string)  
[2025-07-01 06:15:42] local.INFO: getServicesOfferedAttribute appelé avec: [] (type: string)  
[2025-07-01 06:15:49] local.INFO: getLanguagesAttribute appelé avec: [] (type: string)  
[2025-07-01 06:15:49] local.INFO: getServicesOfferedAttribute appelé avec: [] (type: string)  
[2025-07-01 06:15:50] local.INFO: getLanguagesAttribute appelé avec: [] (type: string)  
[2025-07-01 06:15:50] local.INFO: getServicesOfferedAttribute appelé avec: [] (type: string)  
[2025-07-01 06:42:23] local.ERROR: $config must be a string or an array {"exception":"[object] (Stripe\\Exception\\InvalidArgumentException(code: 0): $config must be a string or an array at C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\stripe\\stripe-php\\lib\\BaseStripeClient.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\app\\Http\\Controllers\\Api\\SubscriptionController.php(18): Stripe\\BaseStripeClient->__construct(NULL)
#1 [internal function]: App\\Http\\Controllers\\Api\\SubscriptionController->__construct()
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): ReflectionClass->newInstanceArgs(Array)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(278): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1104): Illuminate\\Routing\\Route->getController()
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1035): Illuminate\\Routing\\Route->controllerMiddleware()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(818): Illuminate\\Routing\\Route->gatherMiddleware()
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(201): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(146): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#14 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 124)
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\28juin\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}
"} 
