import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  Calendar,
  Clock,
  DollarSign,
  Download,
  Edit,
  FileText,
  MessageSquare,
  Paperclip,
  Trash2,
  User,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import Avatar from '../ui/Avatar';
import { API_BASE_URL } from '../../config';

interface Attachment {
  name: string;
  path: string;
  size: number;
  type: string;
}

interface Professional {
  id: number;
  name: string;
  avatar?: string;
  title?: string;
  rating?: number;
  hourlyRate?: string;
}

interface ProjectDetails {
  id: number;
  title: string;
  description: string;
  category: string;
  budget: string;
  deadline: string;
  status: 'draft' | 'open' | 'in_progress' | 'completed' | 'cancelled';
  skills: string[];
  attachments: Attachment[];
  professional?: Professional;
  createdAt: string;
  updatedAt: string;
}

const ProjectDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const token = localStorage.getItem('token');
  const [project, setProject] = useState<ProjectDetails | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProjectDetails = async () => {
      setLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/dashboard/projects/${id}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Error fetching project details: ${response.status}`);
        }

        const data = await response.json();
        setProject(data.project);
        setError(null);
      } catch (err) {
        console.error('Error fetching project details:', err);
        setError('Failed to load project details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (id && token) {
      fetchProjectDetails();
    }
  }, [id, token]);

  const handleEditProject = () => {
    navigate(`/dashboard/projects/edit/${id}`);
  };

  const handleDeleteProject = async () => {
    if (!window.confirm('Are you sure you want to delete this project?')) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/api/dashboard/projects/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Error deleting project: ${response.status}`);
      }

      navigate('/dashboard/projects');
    } catch (err) {
      console.error('Error deleting project:', err);
      setError('Failed to delete project. Please try again later.');
    }
  };

  const handleDownloadAttachment = (attachment: Attachment) => {
    window.open(`${API_BASE_URL}/storage/${attachment.path}`, '_blank');
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { label: string, color: string }> = {
      draft: { label: 'Brouillon', color: 'warning' },
      open: { label: 'Ouvert', color: 'success' },
      in_progress: { label: 'En cours', color: 'primary' },
      completed: { label: 'Terminé', color: 'neutral' },
      cancelled: { label: 'Annulé', color: 'danger' },
    };

    const config = statusConfig[status] || { label: status, color: 'neutral' };
    return <Badge color={config.color as any}>{config.label}</Badge>;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      case 'cancelled':
        return <XCircle className="h-6 w-6 text-red-500" />;
      case 'draft':
        return <FileText className="h-6 w-6 text-amber-500" />;
      case 'open':
        return <AlertTriangle className="h-6 w-6 text-green-500" />;
      case 'in_progress':
        return <Clock className="h-6 w-6 text-blue-500" />;
      default:
        return <FileText className="h-6 w-6 text-neutral-500" />;
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !project) {
    return (
      <DashboardLayout>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-lg font-semibold text-red-700 mb-2">Erreur</h2>
          <p className="text-red-600">{error || 'Project not found'}</p>
          <Button
            variant="primary"
            className="mt-4"
            onClick={() => navigate('/dashboard/projects')}
          >
            Retour aux projets
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Détails du projet"
      subtitle="Consultez et gérez les informations de votre projet"
      actions={
        <Button
          variant="outline"
          leftIcon={<ArrowLeft className="h-4 w-4" />}
          onClick={() => navigate('/dashboard/projects')}
        >
          Retour aux projets
        </Button>
      }
    >
      {/* Project header */}
      <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden mb-6">
        <div className="p-6 border-b border-neutral-200">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex items-center">
              <div className="mr-4 p-3 rounded-full bg-neutral-100">
                {getStatusIcon(project.status)}
              </div>
              <div>
                <h1 className="text-2xl font-bold text-neutral-900">{project.title}</h1>
                <div className="flex items-center mt-2">
                  {getStatusBadge(project.status)}
                  <span className="text-sm text-neutral-500 ml-4">
                    Créé le {formatDate(project.createdAt)}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                leftIcon={<Edit className="h-4 w-4" />}
                onClick={handleEditProject}
              >
                Modifier
              </Button>
              <Button
                variant="danger"
                leftIcon={<Trash2 className="h-4 w-4" />}
                onClick={handleDeleteProject}
              >
                Supprimer
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Project content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Project details */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h2 className="text-lg font-semibold text-neutral-900">Informations du projet</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <h3 className="text-sm font-medium text-neutral-500 mb-1">Catégorie</h3>
                  <p className="text-neutral-900">{project.category}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-neutral-500 mb-1">Budget</h3>
                  <div className="flex items-center">
                    <DollarSign className="h-5 w-5 text-neutral-400 mr-1" />
                    <p className="text-neutral-900 font-medium">{project.budget}</p>
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-neutral-500 mb-1">Date limite</h3>
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-neutral-400 mr-1" />
                    <p className="text-neutral-900">{formatDate(project.deadline)}</p>
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-neutral-500 mb-1">Dernière mise à jour</h3>
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-neutral-400 mr-1" />
                    <p className="text-neutral-900">{formatDate(project.updatedAt)}</p>
                  </div>
                </div>
              </div>

              <h3 className="text-sm font-medium text-neutral-500 mb-2">Description</h3>
              <div className="bg-neutral-50 rounded-lg p-4 mb-6">
                <p className="text-neutral-700 whitespace-pre-line">{project.description}</p>
              </div>

              {project.skills && project.skills.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-neutral-500 mb-2">Compétences requises</h3>
                  <div className="flex flex-wrap gap-2">
                    {project.skills.map((skill, index) => (
                      <Badge key={index} color="info">{skill}</Badge>
                    ))}
                  </div>
                </div>
              )}

              {project.attachments && project.attachments.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-neutral-500 mb-2">Pièces jointes</h3>
                  <div className="space-y-2">
                    {project.attachments.map((attachment, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between bg-neutral-50 rounded-lg p-3 hover:bg-neutral-100 transition-colors"
                      >
                        <div className="flex items-center">
                          <Paperclip className="h-5 w-5 text-neutral-400 mr-2" />
                          <div>
                            <p className="text-sm font-medium text-neutral-900">{attachment.name}</p>
                            <p className="text-xs text-neutral-500">
                              {(attachment.size / 1024).toFixed(2)} KB
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          leftIcon={<Download className="h-4 w-4" />}
                          onClick={() => handleDownloadAttachment(attachment)}
                        >
                          Télécharger
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Discussion section */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200 flex justify-between items-center">
              <h2 className="text-lg font-semibold text-neutral-900">Discussion</h2>
              <Button
                variant="primary"
                leftIcon={<MessageSquare className="h-4 w-4" />}
                style={{ backgroundColor: '#2980b9', color: 'white', fontWeight: 'bold' }}
              >
                Envoyer un message
              </Button>
            </div>
            <div className="p-6 text-center text-neutral-500">
              <MessageSquare className="h-12 w-12 text-neutral-300 mx-auto mb-4" />
              <p>Aucun message pour le moment</p>
              <p className="text-sm">Commencez la conversation en envoyant un message.</p>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Professional assigned */}
          {project.professional ? (
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-neutral-200">
                <h2 className="text-lg font-semibold text-neutral-900">Professionnel assigné</h2>
              </div>
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <Avatar
                    src={project.professional.avatar}
                    fallback={project.professional.name.charAt(0)}
                    size="lg"
                    className="mr-4"
                  />
                  <div>
                    <h3 className="text-lg font-medium text-neutral-900">{project.professional.name}</h3>
                    {project.professional.title && (
                      <p className="text-neutral-500">{project.professional.title}</p>
                    )}
                    {project.professional.rating && (
                      <div className="flex items-center mt-1">
                        <div className="flex items-center text-yellow-500 mr-2">
                          ★ <span className="ml-1 text-xs text-neutral-700">{project.professional.rating}</span>
                        </div>
                        {project.professional.hourlyRate && (
                          <div className="text-xs text-neutral-700">
                            {project.professional.hourlyRate}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                <Button
                  variant="outline"
                  fullWidth
                  leftIcon={<MessageSquare className="h-4 w-4" />}
                >
                  Contacter
                </Button>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-neutral-200">
                <h2 className="text-lg font-semibold text-neutral-900">Professionnel</h2>
              </div>
              <div className="p-6 text-center">
                <User className="h-12 w-12 text-neutral-300 mx-auto mb-4" />
                <p className="text-neutral-700 mb-4">Aucun professionnel assigné à ce projet</p>
                <Button
                  variant="primary"
                  fullWidth
                  style={{ backgroundColor: '#2980b9', color: 'white', fontWeight: 'bold' }}
                >
                  Trouver un professionnel
                </Button>
              </div>
            </div>
          )}

          {/* Project actions */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h2 className="text-lg font-semibold text-neutral-900">Actions</h2>
            </div>
            <div className="p-4 space-y-3">
              <Button
                variant="outline"
                fullWidth
                leftIcon={<Edit className="h-4 w-4" />}
                onClick={handleEditProject}
              >
                Modifier le projet
              </Button>

              {project.status === 'draft' && (
                <Button
                  variant="primary"
                  fullWidth
                  leftIcon={<AlertTriangle className="h-4 w-4" />}
                  style={{ backgroundColor: '#2980b9', color: 'white', fontWeight: 'bold' }}
                >
                  Publier le projet
                </Button>
              )}

              {project.status === 'open' && (
                <Button
                  variant="primary"
                  fullWidth
                  leftIcon={<User className="h-4 w-4" />}
                  style={{ backgroundColor: '#2980b9', color: 'white', fontWeight: 'bold' }}
                >
                  Trouver un professionnel
                </Button>
              )}

              {project.status === 'in_progress' && (
                <Button
                  variant="primary"
                  fullWidth
                  leftIcon={<CheckCircle className="h-4 w-4" />}
                  style={{ backgroundColor: '#10b981', color: 'white', fontWeight: 'bold' }}
                >
                  Marquer comme terminé
                </Button>
              )}

              {project.status !== 'cancelled' && project.status !== 'completed' && (
                <Button
                  variant="danger"
                  fullWidth
                  leftIcon={<XCircle className="h-4 w-4" />}
                >
                  Annuler le projet
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ProjectDetailsPage;
