import React, { ReactNode } from 'react';
import { AlertCircle, CheckCircle } from 'lucide-react';

interface AuthFormContainerProps {
  title: string;
  subtitle: string;
  icon: ReactNode;
  children: ReactNode;
  error?: string;
  successMessage?: string;
}

/**
 * Conteneur réutilisable pour les formulaires d'authentification
 * avec une meilleure accessibilité
 */
const AuthFormContainer: React.FC<AuthFormContainerProps> = ({
  title,
  subtitle,
  icon,
  children,
  error,
  successMessage,
}) => {
  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-8">
        <div className="mx-auto w-16 h-16 text-primary-600 mb-4">
          {icon}
        </div>
        <h1 className="text-2xl font-bold text-neutral-900">{title}</h1>
        <p className="text-neutral-600 mt-2">{subtitle}</p>
      </div>

      {/* Success message */}
      {successMessage && (
        <div
          className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-start"
          role="alert"
          aria-live="polite"
        >
          <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" aria-hidden="true" />
          <p className="text-green-700">{successMessage}</p>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div
          className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start"
          role="alert"
          aria-live="assertive"
        >
          <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" aria-hidden="true" />
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {children}
    </div>
  );
};

export default AuthFormContainer;
