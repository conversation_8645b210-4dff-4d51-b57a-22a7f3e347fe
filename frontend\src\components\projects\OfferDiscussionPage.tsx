import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Briefcase, User, Calendar, DollarSign, Clock, ThumbsUp, ThumbsDown } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import OfferDiscussionPanel from '../messaging/OfferDiscussionPanel';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import Alert from '../ui/Alert';
import { useNotifications } from '../notifications/NotificationContext';

interface OfferDetails {
  id: number;
  title: string;
  description: string;
  budget: string;
  deadline: string;
  status: 'pending' | 'open' | 'closed' | 'in_progress' | 'completed' | 'invited';
  created_at: string;
  client: {
    id: number;
    name: string;
    avatar?: string;
  };
  professional?: {
    id: number;
    name: string;
    avatar?: string;
  };
  is_interested?: boolean;
  is_invited?: boolean;
}

export interface OpenOffer {
  id: number;
  user_id: number;
  title: string;
  categories: string[];
  budget: string;
  deadline: string;
  company: string;
  website: string;
  description: string;
  files: string | null;
  recruitment_type: 'company' | 'freelance' | string;
  open_to_applications: boolean;
  auto_invite: boolean;
  status: 'open' | 'closed' | 'pending' | 'completed' | string;
  views_count: number;
  created_at: string;
  updated_at: string;
  user: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    email_verified_at: string;
    is_professional: boolean;
    created_at: string;
    updated_at: string;
    profile_completed: boolean;
  };
  applications: Application[];
}

export interface Application {
  id: number;
  open_offer_id: number;
  proposal: string | null;
  status: 'pending' | 'accepted' | 'rejected'| 'invited' | string;
  created_at: string;
  updated_at: string;
  professional_profile_id: number;
  freelance_profile: FreelanceProfile;
}

export interface FreelanceProfile {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  avatar: string;
  portfolio_items: any[] | null;
  phone: string;
  address: string;
  city: string;
  country: string;
  bio: string;
  title: string | null;
  expertise: string | null;
  completion_percentage: number;
  profession: string;
  years_of_experience: number;
  hourly_rate: string;
  description: string | null;
  availability_status: 'available' | 'unavailable' | string;
  estimated_response_time: string | null;
  rating: string;
  skills: string[];
  languages: string[];
  services_offered: any[];
  portfolio: PortfolioItem[];
  social_links: any[];
  created_at: string;
  updated_at: string;
  user: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    email_verified_at: string;
    is_professional: boolean;
    created_at: string;
    updated_at: string;
    profile_completed: boolean;
  };
}

export interface PortfolioItem {
  id: string;
  path: string;
  name: string;
  type: string;
  created_at: string;
}

const OfferDiscussionPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [offer, setOffer] = useState<OfferDetails | null>(null);
  const[offerDetail, setOfferDetail] = useState<OpenOffer | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const { addOfferNotification } = useNotifications();

  const token = localStorage.getItem('token');
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  const isClient = currentUser.role === 'client' || !currentUser.is_professional;

  // Rediriger les clients vers la page de détails client
  useEffect(() => {
    if (isClient) {
      navigate(`/dashboard/client/offers/${id}`);
    }
  }, [isClient, id, navigate]);

  useEffect(() => {
    const fetchOfferDetails = async () => {
      if (!token || !id) return;

      setLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des détails de l\'appel d\'offre');
        }

        const data = await response.json();
        console.log("Réponse API pour les détails de l'offre:", data);

        // Adapter la structure de l'offre pour correspondre à notre interface
        if (data.open_offer) {
          setOfferDetail(data.open_offer);
          // Si l'API renvoie user au lieu de client, adapter la structure
          if (data.open_offer.user && !data.open_offer.client) {
            data.open_offer.client = {
              id: data.open_offer.user.id,
              name: data.open_offer.user.first_name + ' ' + data.open_offer.user.last_name,
              avatar: data.open_offer.user.avatar,
            };
          }

          setOffer(data.open_offer);
        } else {
          throw new Error('Structure de réponse API invalide');
        }
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger les détails de l\'appel d\'offre');

        // Utiliser des données de secours
        setOffer({
          id: parseInt(id || '0'),
          title: 'Création d\'un environnement 3D pour jeu mobile',
          description: 'Nous recherchons un artiste 3D pour créer un environnement complet pour notre jeu mobile d\'aventure. Le projet comprend la modélisation, le texturing et l\'optimisation pour mobile.',
          budget: '1 500 €',
          deadline: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString(), // 20 days from now
          status: 'open',
          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
          client: {
            id: 101,
            name: 'MobileGames Studio',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
          },
          professional: isClient ? {
            id: currentUser.id,
            name: currentUser.name || 'Professionnel',
            avatar: currentUser.avatar,
          } : undefined,
          is_interested: !isClient,
          is_invited: !isClient,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchOfferDetails();
  }, [id, token, isClient, currentUser.id, currentUser.name, currentUser.avatar]);

  // Formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  // Calculer les jours restants
  const getDaysRemaining = (dateString: string) => {
    const deadline = new Date(dateString);
    const now = new Date();
    const diffTime = deadline.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return 'Délai dépassé';
    } else if (diffDays === 0) {
      return 'Dernier jour';
    } else {
      return `${diffDays} jour${diffDays > 1 ? 's' : ''} restant${diffDays > 1 ? 's' : ''}`;
    }
  };

  // const hasAlreadyApplied = () => {
  //       // return offer.applications.some((application: any) => application.freelance_profile?.user?.id === user.id);
  //       return offerDetail?.applications.some((application: any) => {
  //           return (
  //               application.freelance_profile?.user?.id === currentUser.id &&
  //               application.status !== 'invited' && application.status !== 'rejected' && application.status !== 'pending'
  //           );
  //       });
  //   };

  const hasAlreadyApplied = () => {
        // return offer.applications.some((application: any) => application.freelance_profile?.user?.id === user.id);
        return offerDetail?.applications.some((application: any) => {
            return (
                application.freelance_profile?.user?.id === currentUser.id &&
                application.status === 'accepted'
            );
        });
    };

  const hasInvited = () => {
        // return offer.applications.some((application: any) => application.freelance_profile?.user?.id === user.id);
        return offerDetail?.applications.some((application: any) => {
            return (
                application.freelance_profile?.user?.id === currentUser.id &&
                application.status === 'invited'
            );
        });
    };

  const hasAlreadyRefused = () => {
        // return offer.applications.some((application: any) => application.freelance_profile?.user?.id === user.id);
        return offerDetail?.applications.some((application: any) => {
            return (
                application.freelance_profile?.user?.id === currentUser.id &&
                application.status === 'rejected'
            );
        });
    };

  const hasAlreadyPostule = () => {
        // return offer.applications.some((application: any) => application.freelance_profile?.user?.id === user.id);
        return offerDetail?.applications.some((application: any) => {
            return (
                application.freelance_profile?.user?.id === currentUser.id &&
                application.status === 'pending'
            );
        });
    };

    
  // const handleAcceptOffer = async (offerId: number) => {
  //     try {
  //       const response = await fetch(`${API_BASE_URL}/api/offer-applications/${offerId}/accept`, {
  //         method: 'PUT',
  //         headers: {
  //           'Authorization': `Bearer ${token}`,
  //           'Content-Type': 'application/json',
  //         },
  //       });
  
  //       if (!response.ok) {
  //         throw new Error('Erreur lors de l\'acceptation de l\'offre');
  //       }
  
  //       // Mettre à jour la liste des offres reçues
  //       setReceivedOffers(receivedOffers.filter(offer => offer.id !== offerId));
  
  //       // Ajouter un message de succès (à implémenter)
  //       console.log('Offre acceptée avec succès');
  //     } catch (err) {
  //       console.error('Erreur:', err);
  //       // Afficher un message d'erreur (à implémenter)
  //     }
  //   };

  // Gérer l'acceptation de l'offre
  const handleAcceptOffer = async () => {
    // if (!token || !id) return;
    if (!token || !offerDetail || !offerDetail.applications) {
      setError("Données insuffisantes pour accepter l'offre. Il faut être invité pour pouvoir accépté sinon vous pouvez postuler. Merci");
      return;
    }

    // Chercher l'application liée à l'utilisateur connecté
    const application = offerDetail.applications.find(app =>
      app.freelance_profile?.user?.id === currentUser.id
    );

    if (!application) {
      setError("Aucune candidature ou invitation trouvée pour vous. Il faut être invité pour pouvoir accépté sinon vous pouvez postuler. Merci");
      return;
    }

    const applicationId = application.id;

    try {
      const response = await fetch(`${API_BASE_URL}/api/offer-applications/${applicationId}/accept`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });


      if (!response.ok) {
        const errorBody = await response.json();
        console.error('Erreur API:', errorBody);
        throw new Error(errorBody.message || 'Erreur lors de l\'acceptation de l\'offre');
      }

      console.log("Reponse : ",response)
      alert("L'offre accepter avec succès");

      window.location.reload();
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible d\'accepter l\'offre. Veuillez réessayer plus tard.');
    }
  };

  const handleApplyToProject = async(projectId: number) => {
      // navigate(`/dashboard/open-offers/${projectId}/apply`);
      const token = localStorage.getItem("token");
          try {
              const response = await fetch(`${API_BASE_URL}/api/open-offers/${projectId}/apply`, {
                  method: 'POST',
                  headers: {
                      Authorization: `Bearer ${token}`,
                      'Content-Type': 'application/json',
                  },
              });
  
              if (!response.ok) {
                  const errorData = await response.json();
                  throw new Error(errorData.message || 'Une erreur est survenue');
              }
              alert(`Vous avez marqué l'offre comme intéressé.`);
              window.location.reload();
          } catch (err) {
              alert(err instanceof Error ? err.message : "Une erreur est survenue.");
          }
    };
  // const handleApply = async () => {
  //       setSubmitError(null);
  //       const token = localStorage.getItem("token");

  //       try {
  //           const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}/apply`, {
  //               method: 'POST',
  //               headers: {
  //                   Authorization: `Bearer ${token}`,
  //                   'Content-Type': 'application/json',
  //               },
  //           });

  //           if (!response.ok) {
  //               const errorData = await response.json();
  //               throw new Error(errorData.message || 'Une erreur est survenue');
  //           }
  //           alert("Offre accepté avec succès");
  //           navigate('/pro/offres');
  //       } catch (err) {
  //           if (err instanceof Error) {
  //               setSubmitError(err.message);
  //               alert(err.message);
  //           } else {
  //               setSubmitError("Une erreur inconnue est survenue.");
  //               alert("Une erreur inconnue est survenue.");
  //           }
  //       }
  //   };

  const hasApplication = () => {
    return offerDetail?.applications.some(app =>
      app.freelance_profile?.user?.id === currentUser.id
    );
  };
  // Gérer le refus de l'offre
  const handleDeclineOffer = async () => {
    // if (!token || !id) return;
    if (!token || !offerDetail || !offerDetail.applications) {
      setError("Données insuffisantes pour refuser l'offre. Il faut être invité pour pouvoir refuser. Merci");
      return;
    }

    // Chercher l'application liée à l'utilisateur connecté
    const application = offerDetail.applications.find(app =>
      app.freelance_profile?.user?.id === currentUser.id
    );

    if (!application) {
      setError("Aucune candidature ou invitation trouvée pour vous. Il faut être invité pour pouvoir refuser. Merci");
      return;
    }

    const applicationId = application.id;

    try {
      const response = await fetch(`${API_BASE_URL}/api/offer-applications/${applicationId}/decline`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors du refus de l\'offre');
      }

      // Afficher un message de succès
      setSuccessMessage('Vous avez indiqué que vous n\'êtes pas disponible pour cette offre.');

      // Rediriger vers le tableau de bord après un court délai
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);

      // Envoyer une notification au client
      if (offer) {
        addOfferNotification('offer_not_available', {
          offer_id: offer.id,
          offer_title: offer.title,
          professional_id: currentUser.id,
          professional_name: currentUser.name || 'Professionnel',
          professional_avatar: currentUser.avatar,
          client_id: offer.client?.id || 0,
          client_name: offer.client?.name || 'Client',
          client_avatar: offer.client?.avatar,
        });
      }
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de refuser l\'offre. Veuillez réessayer plus tard.');
    }
  };

  // Gérer la sélection d'un professionnel (pour les clients)
  const handleSelectProfessional = async () => {
    if (!token || !id || !offer?.professional?.id) return;

    try {
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}/select-professional`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          professional_id: offer.professional.id,
        }),
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la sélection du professionnel');
      }

      // Mettre à jour l'état local
      setOffer(prev => prev ? { ...prev, status: 'in_progress' } : null);

      // Afficher un message de succès
      setSuccessMessage('Vous avez sélectionné ce professionnel pour votre projet. Il en sera informé.');

      // Envoyer une notification au professionnel
      if (offer) {
        addOfferNotification('offer_selected', {
          offer_id: offer.id,
          offer_title: offer.title,
          professional_id: offer.professional.id,
          professional_name: offer.professional.name,
          professional_avatar: offer.professional.avatar,
          client_id: currentUser.id,
          client_name: currentUser.name || 'Client',
          client_avatar: currentUser.avatar,
        });
      }
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de sélectionner le professionnel. Veuillez réessayer plus tard.');
    }
  };

  // Gérer la clôture de l'appel d'offre (pour les clients)
  const handleCloseOffer = async () => {
    if (!token || !id) return;

    try {
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}/close`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la clôture de l\'appel d\'offre');
      }

      // Mettre à jour l'état local
      setOffer(prev => prev ? { ...prev, status: 'closed' } : null);

      // Afficher un message de succès
      setSuccessMessage('Vous avez clôturé cet appel d\'offre.');

      // Rediriger vers le tableau de bord après un court délai
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);

      // Envoyer une notification
      if (offer) {
        addOfferNotification('offer_closed', {
          offer_id: offer.id,
          offer_title: offer.title,
          client_id: currentUser.id,
          client_name: currentUser.name || 'Client',
          client_avatar: currentUser.avatar,
        });
      }
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de clôturer l\'appel d\'offre. Veuillez réessayer plus tard.');
    }
  };

  return (
    <DashboardLayout
      title="Discussion d'appel d'offre"
      subtitle="Échangez avec l'autre partie concernant cet appel d'offre"
      actions={
        !isClient && offer && !offer.is_interested ? (
          <div className="flex space-x-3">
            {hasAlreadyApplied() ? (
                  <p className="text-green-700 font-semibold">✅ Vous êtes déjà intéressé par cette offre.</p>
              ) : hasAlreadyPostule () ?(
                <p className="text-yellow-600 font-medium bg-yellow-100 px-3 py-2 rounded-md shadow-sm w-fit">
                ⏳ Votre demande est en attente d’acceptation du client.
                </p>
              ): hasAlreadyRefused () ? (
                <p className="text-red-700 font-semibold bg-red-100 px-3 py-2 rounded-md shadow-sm w-fit">
                ❌ Vous avez déjà refusé cette offre.
                </p>
              ) : hasInvited () ? (
                  <>
                    <Button
                      variant="danger"
                      leftIcon={<ThumbsDown className="h-5 w-5" />}
                      onClick={handleDeclineOffer}
                    >
                      Pas disponible
                    </Button>
                    <Button
                      variant="primary"
                      leftIcon={<ThumbsUp className="h-5 w-5" />}
                      onClick={handleAcceptOffer}
                      style={{ backgroundColor: '#28a745', color: 'black' }}
                    >
                      Intéressé
                    </Button>
                  </>
              ) : (
              <>
              {/* {hasApplication()?(
                <>
                <Button
                  variant="danger"
                  leftIcon={<ThumbsDown className="h-5 w-5" />}
                  onClick={handleDeclineOffer}
                >
                  Pas disponible
                </Button>
                <Button
                  variant="primary"
                  leftIcon={<ThumbsUp className="h-5 w-5" />}
                  onClick={handleAcceptOffer}
                  style={{ backgroundColor: '#28a745', color: 'black' }}
                >
                  Intéressé
                </Button>
                </>
              ):(
                <></>
              )} */}
              
              <Button
                variant="primary"
                onClick={() => handleApplyToProject(Number(id))}
              >
                Postuler
              </Button>
              </>
            )}
          </div>
        ) : isClient && offer && offer.status === 'open' ? (
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleCloseOffer}
            >
              Clôturer l'appel d'offre
            </Button>
            {offer.professional && (
              <Button
                variant="primary"
                onClick={handleSelectProfessional}
                style={{ backgroundColor: '#2980b9', color: 'black' }}
              >
                Sélectionner ce professionnel
              </Button>
            )}
          </div>
        ) : null
      }
    >
      {/* Success Alert */}
      {successMessage && (
        <Alert
          type="success"
          title="Succès"
          onClose={() => setSuccessMessage(null)}
          className="mb-6"
        >
          {successMessage}
        </Alert>
      )}

      {/* Error Alert */}
      {error && (
        <Alert
          type="error"
          title="Erreur"
          onClose={() => setError(null)}
          className="mb-6"
        >
          {error}
        </Alert>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      ) : !offer ? (
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-12 text-center">
          <h3 className="text-lg font-medium text-neutral-700 mb-4">Appel d'offre non trouvé</h3>
          <p className="text-neutral-500 mb-6">Cet appel d'offre n'existe pas ou vous n'avez pas les permissions nécessaires pour y accéder.</p>
          <Button
            variant="primary"
            onClick={() => navigate('/dashboard')}
            style={{ backgroundColor: '#2980b9', color: 'black' }}
          >
            Retour au tableau de bord
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Détails de l'appel d'offre */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-neutral-200">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-neutral-900">Détails de l'appel d'offre</h3>
                  <Badge
                    color={
                      offer.status === 'pending' ? 'warning' :
                      offer.status === 'open' ? 'info' :
                      offer.status === 'in_progress' ? 'success' :
                      offer.status === 'completed' ? 'success' :
                      'neutral'
                    }
                  >
                    {offer.status === 'pending' ? 'En attente' :
                     offer.status === 'open' ? 'Ouvert' :
                     offer.status === 'in_progress' ? 'En cours' :
                     offer.status === 'completed' ? 'Terminé' :
                     offer.status === 'closed' ? 'Clôturé' :
                     offer.status === 'invited' ? 'Invitation' :
                     'Inconnu'}
                  </Badge>
                </div>
              </div>

              <div className="p-6">
                <h2 className="text-xl font-semibold text-neutral-900 mb-4">{offer.title}</h2>
                <p className="text-neutral-700 mb-6 whitespace-pre-wrap">{offer.description}</p>

                <div className="space-y-4 mb-6">
                  <div className="flex items-center">
                    <DollarSign className="h-5 w-5 text-neutral-500 mr-2" />
                    <span className="text-neutral-700 font-medium">{offer.budget}</span>
                  </div>

                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-neutral-500 mr-2" />
                    <span className="text-neutral-700">Date limite: {formatDate(offer.deadline)}</span>
                  </div>

                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-neutral-500 mr-2" />
                    <span className="text-neutral-700">{getDaysRemaining(offer.deadline)}</span>
                  </div>

                  <div className="flex items-center">
                    <User className="h-5 w-5 text-neutral-500 mr-2" />
                    <span className="text-neutral-700">
                      {isClient ? 'Professionnel' : 'Client'}: {isClient ? (offer.professional?.name || 'Non sélectionné') : (offer.client?.name || 'Client')}
                    </span>
                  </div>

                  <div className="flex items-center">
                    <Briefcase className="h-5 w-5 text-neutral-500 mr-2" />
                    <span className="text-neutral-700">Créé le: {formatDate(offer.created_at)}</span>
                  </div>
                </div>

                {!isClient && offer.is_invited && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <p className="text-blue-800 text-sm">
                      Vous avez reçu une invitation personnalisée pour cet appel d'offre.
                    </p>
                  </div>
                )}

                {!isClient && offer.is_interested && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <p className="text-green-800 text-sm">
                      Vous avez indiqué votre intérêt pour cet appel d'offre.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Panel de discussion */}
          <div className="lg:col-span-2">
            {(() => {
              const isApplicationAccepted = offerDetail?.applications?.some(app => 
                app.freelance_profile?.user?.id === currentUser.id && 
                app.status === 'accepted'
              );

              const isApplicationRefuser = offerDetail?.applications?.some(app => 
                app.freelance_profile?.user?.id === currentUser.id && 
                app.status === 'rejected'
              );

              const isApplicationPostuler = offerDetail?.applications?.some(app => 
                app.freelance_profile?.user?.id === currentUser.id && 
                app.status === 'pending'
              );

              const isApplicationInviter = offerDetail?.applications?.some(app => 
                app.freelance_profile?.user?.id === currentUser.id && 
                app.status === 'invited'
              );

              if (isApplicationRefuser) {
                return (
                  <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-6 text-center">
                    <h3 className="text-lg font-medium text-neutral-700 mb-4">Discussion non disponible</h3>
                    <p className="text-neutral-500">
                      🚫 Vous avez refusé cette offre. Vous ne pouvez pas entamer une discussion avec le client.
                    </p>
                  </div>
                );
              }

              if (isApplicationPostuler) {
                return (
                  <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-6 text-center">
                    <h3 className="text-lg font-medium text-neutral-700 mb-4">Discussion non disponible</h3>
                    <p className="text-neutral-500">
                      En attente d’acceptation du client pour pouvoir commencer la discussion.
                    </p>
                  </div>
                );
              }

              if (isApplicationInviter) {
                return (
                  <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-6 text-center">
                    <h3 className="text-lg font-medium text-neutral-700 mb-4">Discussion non disponible</h3>
                    <p className="text-neutral-500">
                      Cliquer sur le bouton “Intéresser“ ou “Pas disponible“ pour accepter ou refuser l'offre
                    </p>
                  </div>
                );
              }

              if (!isApplicationAccepted) {
                return (
                  <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-6 text-center">
                    <h3 className="text-lg font-medium text-neutral-700 mb-4">Discussion non disponible</h3>
                    <p className="text-neutral-500">
                      👉 Cliquez sur le bouton "Postuler" pour montrer votre intérêt à cette offre.
                    </p>
                  </div>
                );
              }

              return (
                <OfferDiscussionPanel
                  offerId={offer.id}
                  offerTitle={offer.title}
                  clientId={offer.client?.id}
                  clientName={offer.client?.name || "Client"}
                  clientAvatar={offer.client?.avatar}
                  professionalId={offer.professional?.id}
                  professionalName={offer.professional?.name || "Professionnel"}
                  professionalAvatar={offer.professional?.avatar}
                  isClient={isClient}
                  onBack={() => navigate(-1)}
                />
              );
            })()}
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default OfferDiscussionPage;
