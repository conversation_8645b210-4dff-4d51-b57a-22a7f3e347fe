import React, { useState, useEffect } from "react";
import {
  X,
  CheckCircle,
  User,
  Briefcase,
  MapPin,
  Phone,
  Image,
  FileText,
  AlertTriangle,
  ChevronDown,
  Search,
} from "lucide-react";
import Button from "../ui/Button";
import { API_BASE_URL } from "../../config";
import { profileService } from "../../services/profileService";
import {
  MAIN_CATEGORIES,
  getAllCategories,
  Category,
} from "../../data/categories";
import LocationSelector from "../ui/LocationSelector";

interface ProfileCompletionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
  userType: "professional" | "client";
}

const ProfileCompletionModal: React.FC<ProfileCompletionModalProps> = ({
  isOpen,
  onClose,
  onComplete,
  userType,
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    bio: "",
    skills: [] as string[],
    address: "",
    city: "",
    country: "",
    phone: "",
    firstName: "",
    lastName: "",
    profilePicture: null as File | null,
    portfolio: [] as File[],
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [skillInput, setSkillInput] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [filteredSkills, setFilteredSkills] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [addressSuggestions, setAddressSuggestions] = useState<Array<{
    display_name: string;
    lat: string;
    lon: string;
  }>>([]);

  // Liste des compétences par catégorie
  const skillsByCategory: Record<string, string[]> = {
    modeling: [
      "Blender",
      "Maya",
      "3ds Max",
      "ZBrush",
      "Substance Painter",
      "Hard Surface Modeling",
      "Organic Modeling",
    ],
    animation: [
      "Animation de personnages",
      "Animation d'objets",
      "Motion Capture",
      "Rigging",
      "Facial Animation",
    ],
    architectural: [
      "SketchUp",
      "Revit",
      "ArchiCAD",
      "Lumion",
      "V-Ray",
      "Rendu architectural",
      "Modélisation BIM",
    ],
    product: [
      "Fusion 360",
      "SolidWorks",
      "Rhino 3D",
      "KeyShot",
      "Prototypage 3D",
      "Design industriel",
    ],
    character: [
      "Character Design",
      "Character Modeling",
      "Character Rigging",
      "Facial Rigging",
      "Sculpting",
    ],
    environment: [
      "Environment Design",
      "Landscape Modeling",
      "Terrain Generation",
      "World Building",
      "Level Design",
    ],
    vr_ar: [
      "Unity",
      "Unreal Engine",
      "WebXR",
      "A-Frame",
      "ARKit",
      "ARCore",
      "Oculus SDK",
    ],
    game_art: [
      "Game Asset Creation",
      "Low Poly Modeling",
      "Texture Baking",
      "UV Mapping",
      "PBR Texturing",
    ],
  };

  // Liste complète de toutes les compétences
  const [allSkills, setAllSkills] = useState<string[]>([]);

  // Nombre total d'étapes selon le type d'utilisateur
  const totalSteps = userType === "professional" ? 4 : 3;

  const handleNextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Initialiser la liste complète des compétences
  useEffect(() => {
    const skills: string[] = [];
    Object.values(skillsByCategory).forEach((categorySkills) => {
      categorySkills.forEach((skill) => {
        if (!skills.includes(skill)) {
          skills.push(skill);
        }
      });
    });
    setAllSkills(skills.sort());
    setFilteredSkills(skills.sort());
  }, []);

  // Filtrer les compétences en fonction de la catégorie sélectionnée
  useEffect(() => {
    if (selectedCategory && skillsByCategory[selectedCategory]) {
      setFilteredSkills(skillsByCategory[selectedCategory]);
    } else if (selectedCategory === "") {
      setFilteredSkills(allSkills);
    }
  }, [selectedCategory, allSkills]);

  // Filtrer les compétences en fonction du terme de recherche
  useEffect(() => {
    if (searchTerm) {
      const baseSkills = selectedCategory
        ? skillsByCategory[selectedCategory]
        : allSkills;
      const filtered = baseSkills.filter((skill) =>
        skill.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredSkills(filtered);
    } else {
      if (selectedCategory && skillsByCategory[selectedCategory]) {
        setFilteredSkills(skillsByCategory[selectedCategory]);
      } else {
        setFilteredSkills(allSkills);
      }
    }
  }, [searchTerm, selectedCategory, allSkills]);

  // Charger les données utilisateur existantes
  useEffect(() => {
    if (isOpen) {
      const user = JSON.parse(localStorage.getItem("user") || "{}");
      setFormData((prevData) => ({
        ...prevData,
        firstName: user.first_name || "",
        lastName: user.last_name || "",
        phone: user.phone || "",
      }));
    }
  }, [isOpen]);

  const handleComplete = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Vérifier que tous les champs obligatoires sont remplis
      const requiredFields = {
        first_name: formData.firstName,
        last_name: formData.lastName,
        phone: formData.phone,
        address: formData.address,
        city: formData.city,
        country: formData.country,
      };

      const emptyFields = Object.entries(requiredFields)
        .filter(([_, value]) => !value || value.trim() === "")
        .map(([field, _]) => field);

      if (emptyFields.length > 0) {
        const errorMessages = emptyFields
          .map((field) => {
            const fieldNames: Record<string, string> = {
              first_name: "Prénom",
              last_name: "Nom",
              phone: "Téléphone",
              address: "Adresse",
              city: "Ville",
              country: "Pays",
            };
            return `${fieldNames[field] || field}: Ce champ est requis.`;
          })
          .join("\n");

        throw new Error(`Erreurs de validation:\n${errorMessages}`);
      }

      // Créer un FormData pour l'envoi
      const formDataToSend = new FormData();

      // Ajouter les champs de base
      formDataToSend.append("first_name", formData.firstName);
      formDataToSend.append("last_name", formData.lastName);
      formDataToSend.append("phone", formData.phone);
      formDataToSend.append("address", formData.address);
      formDataToSend.append("city", formData.city);
      formDataToSend.append("country", formData.country);
      formDataToSend.append("bio", formData.bio);

      // Ajouter les compétences
      if (formData.skills.length > 0) {
        formDataToSend.append("skills", JSON.stringify(formData.skills));
      }

      // Ajouter la photo de profil
      if (formData.profilePicture) {
        formDataToSend.append("avatar", formData.profilePicture);
      }

      // Ajouter les éléments du portfolio
      if (formData.portfolio.length > 0) {
        formData.portfolio.forEach((file, index) => {
          formDataToSend.append(`portfolio_items[${index}]`, file);
        });
      }

      // Ajouter un indicateur que c'est une complétion de profil
      formDataToSend.append("is_completion", "true");

      console.log("Envoi des données de complétion de profil au serveur...");
      console.log("Données à envoyer:", {
        first_name: formData.firstName,
        last_name: formData.lastName,
        phone: formData.phone,
        bio: formData.bio,
        address: formData.address,
        city: formData.city,
        country: formData.country,
        skills: formData.skills,
        avatar: formData.profilePicture ? "Fichier image" : null,
        portfolio:
          formData.portfolio.length > 0
            ? `${formData.portfolio.length} fichiers`
            : null,
      });

      // Envoyer les données au serveur
      const result = await profileService.completeProfile(formDataToSend);
      console.log("Profil complété avec succès:", result);

      // Mettre à jour les données utilisateur dans le localStorage
      const user = JSON.parse(localStorage.getItem("user") || "{}");
      user.first_name = formData.firstName;
      user.last_name = formData.lastName;
      user.phone = formData.phone;
      user.completion_percentage = result.profile?.completion_percentage || 100;
      localStorage.setItem("user", JSON.stringify(user));

      // Marquer que le profil a été complété
      localStorage.setItem("profile_completed", "true");
      localStorage.setItem("first_login", "false");

      setSuccess("Votre profil a été mis à jour avec succès!");

      // Attendre un peu pour montrer le message de succès
      setTimeout(() => {
        onComplete();
      }, 1500);
    } catch (err) {
      console.error("Erreur lors de la mise à jour du profil:", err);
      setError(err instanceof Error ? err.message : "Une erreur est survenue");
    } finally {
      setLoading(false);
    }
  };

  const handleSkillAdd = () => {
    if (skillInput.trim() && !formData.skills.includes(skillInput.trim())) {
      setFormData({
        ...formData,
        skills: [...formData.skills, skillInput.trim()],
      });
      setSkillInput("");
    }
  };

  // Ajouter une compétence depuis la liste
  const handleAddSkillFromList = (skill: string) => {
    if (!formData.skills.includes(skill)) {
      setFormData({
        ...formData,
        skills: [...formData.skills, skill],
      });
    }
  };

  const handleSkillRemove = (skill: string) => {
    setFormData({
      ...formData,
      skills: formData.skills.filter((s) => s !== skill),
    });
  };

  // Gestion de la recherche
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleFileChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: "profilePicture" | "portfolio"
  ) => {
    if (!e.target.files || e.target.files.length === 0) return;

    if (field === "profilePicture") {
      setFormData({ ...formData, profilePicture: e.target.files[0] });
    } else if (field === "portfolio") {
      const newFiles = Array.from(e.target.files);
      setFormData({
        ...formData,
        portfolio: [...formData.portfolio, ...newFiles],
      });
    }
  };

  const handleRemovePortfolioItem = (index: number) => {
    const newPortfolio = [...formData.portfolio];
    newPortfolio.splice(index, 1);
    setFormData({ ...formData, portfolio: newPortfolio });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh]">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-neutral-200">
          <h2 className="text-2xl font-bold text-neutral-900">
            Complétez votre profil
          </h2>
          <button
            type="button"
            onClick={onClose}
            className="text-neutral-500 hover:text-neutral-700 transition-colors"
            title="Fermer"
            aria-label="Fermer"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Progress bar */}
        <div className="px-6 pt-4">
          <div className="w-full bg-neutral-100 rounded-full h-2.5">
            <div
              className="bg-primary-600 h-2.5 rounded-full transition-all duration-300"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-sm text-neutral-500 mt-2">
            <span>
              Étape {currentStep} sur {totalSteps}
            </span>
            <span>
              {Math.round((currentStep / totalSteps) * 100)}% complété
            </span>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 200px)' }}>
          {/* Step 1: Informations de base */}
          {/* Messages d'erreur ou de succès */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start">
              <AlertTriangle className="h-5 w-5 text-red-500 mr-3 flex-shrink-0 mt-0.5" />
              <p className="text-red-700">{error}</p>
            </div>
          )}

          {success && (
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-start">
              <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
              <p className="text-green-700">{success}</p>
            </div>
          )}

          {currentStep === 1 && (
            <div className="space-y-6">
              <h3 className="text-xl font-semibold text-neutral-900 flex items-center">
                <User className="mr-2 h-5 w-5 text-primary-600" />
                Informations de base
              </h3>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="firstName"
                    className="block text-sm font-medium text-neutral-700 mb-1"
                  >
                    Prénom *
                  </label>
                  <input
                    id="firstName"
                    type="text"
                    value={formData.firstName}
                    onChange={(e) =>
                      setFormData({ ...formData, firstName: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Votre prénom"
                    required
                  />
                </div>

                <div>
                  <label
                    htmlFor="lastName"
                    className="block text-sm font-medium text-neutral-700 mb-1"
                  >
                    Nom *
                  </label>
                  <input
                    id="lastName"
                    type="text"
                    value={formData.lastName}
                    onChange={(e) =>
                      setFormData({ ...formData, lastName: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Votre nom"
                    required
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="phone"
                  className="block text-sm font-medium text-neutral-700 mb-1"
                >
                  Téléphone *
                </label>
                <input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) =>
                    setFormData({ ...formData, phone: e.target.value })
                  }
                  className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="Votre numéro de téléphone"
                  required
                />
              </div>

              <div className="space-y-4">
                <LocationSelector
                  country={formData.country}
                  city={formData.city}
                  address={formData.address}
                  onCountryChange={(country) =>
                    setFormData({ ...formData, country })
                  }
                  onCityChange={(city) => setFormData({ ...formData, city })}
                  onAddressChange={(address) =>
                    setFormData({ ...formData, address })
                  }
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="bio"
                  className="block text-sm font-medium text-neutral-700 mb-1"
                >
                  Biographie
                </label>
                <textarea
                  id="bio"
                  rows={4}
                  className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Parlez-nous un peu de vous..."
                  value={formData.bio}
                  onChange={(e) =>
                    setFormData({ ...formData, bio: e.target.value })
                  }
                ></textarea>
                <p className="text-xs text-neutral-500 mt-1">
                  {formData.bio.length}/500 caractères
                </p>
              </div>
            </div>
          )}

          {/* Step 2: Compétences (pour les professionnels) ou Préférences (pour les clients) */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <h3 className="text-xl font-semibold text-neutral-900 flex items-center">
                <Briefcase className="mr-2 h-5 w-5 text-primary-600" />
                {userType === "professional" ? "Compétences" : "Préférences"}
              </h3>

              {userType === "professional" && (
                <>
                  {/* Sélection de catégorie */}
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">
                      Catégorie de compétences
                    </label>
                    <div className="relative">
                      <button
                        type="button"
                        onClick={() =>
                          setShowCategoryDropdown(!showCategoryDropdown)
                        }
                        className="w-full flex items-center justify-between px-4 py-2 border border-neutral-300 rounded-lg bg-white text-left focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      >
                        <span>
                          {selectedCategory
                            ? MAIN_CATEGORIES.find(
                                (cat) => cat.value === selectedCategory
                              )?.label || selectedCategory
                            : "Toutes les catégories"}
                        </span>
                        <ChevronDown
                          size={20}
                          className={`transition-transform ${
                            showCategoryDropdown ? "rotate-180" : ""
                          }`}
                        />
                      </button>

                      {showCategoryDropdown && (
                        <div className="absolute z-10 mt-1 w-full bg-white border border-neutral-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                          <div
                            className="px-4 py-2 hover:bg-neutral-100 cursor-pointer"
                            onClick={() => {
                              setSelectedCategory("");
                              setShowCategoryDropdown(false);
                            }}
                          >
                            Toutes les catégories
                          </div>
                          {MAIN_CATEGORIES.map((category) => (
                            <div
                              key={category.id}
                              className="px-4 py-2 hover:bg-neutral-100 cursor-pointer"
                              onClick={() => {
                                setSelectedCategory(category.value);
                                setShowCategoryDropdown(false);
                              }}
                            >
                              {category.label}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Recherche de compétences */}
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">
                      Compétences suggérées
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Search size={18} className="text-neutral-400" />
                      </div>
                      <input
                        type="text"
                        placeholder="Rechercher une compétence..."
                        value={searchTerm}
                        onChange={handleSearchChange}
                        className="pl-10 w-full px-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      />
                    </div>

                    {/* Liste des compétences suggérées */}
                    <div className="grid grid-cols-2 gap-2 mt-2 max-h-40 overflow-y-auto">
                      {filteredSkills.map((skill, index) => (
                        <button
                          key={index}
                          type="button"
                          onClick={() => handleAddSkillFromList(skill)}
                          disabled={formData.skills.includes(skill)}
                          className={`text-left px-3 py-2 rounded-lg text-sm ${
                            formData.skills.includes(skill)
                              ? "bg-neutral-100 text-neutral-500 cursor-not-allowed"
                              : "bg-primary-50 text-primary-700 hover:bg-primary-100"
                          }`}
                          title={
                            formData.skills.includes(skill)
                              ? "Déjà ajouté"
                              : "Ajouter cette compétence"
                          }
                        >
                          {skill}
                        </button>
                      ))}
                    </div>
                  </div>
                </>
              )}

              {/* Ajout manuel de compétence */}
              <div>
                <label
                  htmlFor="skills"
                  className="block text-sm font-medium text-neutral-700 mb-1"
                >
                  {userType === "professional"
                    ? "Ajouter une compétence personnalisée"
                    : "Vos centres d'intérêt"}
                </label>
                <div className="flex">
                  <input
                    type="text"
                    id="skills"
                    className="flex-1 px-3 py-2 border border-neutral-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder={
                      userType === "professional"
                        ? "Saisir une compétence personnalisée"
                        : "Ex: Design d'intérieur, Jeux vidéo..."
                    }
                    value={skillInput}
                    onChange={(e) => setSkillInput(e.target.value)}
                    onKeyPress={(e) =>
                      e.key === "Enter" &&
                      (e.preventDefault(), handleSkillAdd())
                    }
                  />
                  <button
                    type="button"
                    onClick={handleSkillAdd}
                    className="px-4 py-2 bg-primary-600 text-white rounded-r-md hover:bg-primary-700 transition-colors"
                  >
                    Ajouter
                  </button>
                </div>
              </div>

              {/* Liste des compétences ajoutées */}
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">
                  {userType === "professional"
                    ? `Mes compétences (${formData.skills.length})`
                    : `Mes centres d'intérêt (${formData.skills.length})`}
                </label>
                <div className="p-4 border border-neutral-200 rounded-lg bg-neutral-50 min-h-[100px]">
                  {formData.skills.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {formData.skills.map((skill, index) => (
                        <div
                          key={index}
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white border border-neutral-300 shadow-sm"
                        >
                          {skill}
                          <button
                            type="button"
                            onClick={() => handleSkillRemove(skill)}
                            className="ml-1.5 text-neutral-500 hover:text-red-500"
                            title={`Supprimer ${skill}`}
                            aria-label={`Supprimer ${skill}`}
                          >
                            <X className="h-3.5 w-3.5" />
                          </button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full text-neutral-500">
                      {userType === "professional"
                        ? "Aucune compétence ajoutée"
                        : "Aucun centre d'intérêt ajouté"}
                    </div>
                  )}
                </div>
              </div>

              {userType === "professional" && (
                <div className="bg-neutral-50 p-4 rounded-lg border border-neutral-200">
                  <h4 className="font-medium text-neutral-900 mb-2">Conseil</h4>
                  <p className="text-sm text-neutral-600">
                    Ajoutez des compétences pertinentes pour votre domaine. Les
                    clients recherchent souvent des professionnels par
                    compétences spécifiques.
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Step 3: Portfolio (pour les professionnels uniquement) */}
          {currentStep === 3 && userType === "professional" && (
            <div className="space-y-6">
              <h3 className="text-xl font-semibold text-neutral-900 flex items-center">
                <FileText className="mr-2 h-5 w-5 text-primary-600" />
                Portfolio
              </h3>

              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">
                  Ajoutez des exemples de vos travaux
                </label>

                <div className="mt-2 border-2 border-dashed border-neutral-300 rounded-lg p-6 text-center">
                  <div className="space-y-2">
                    <div className="mx-auto h-12 w-12 text-neutral-400 flex items-center justify-center rounded-full bg-neutral-100">
                      <Image className="h-6 w-6" />
                    </div>
                    <div className="text-neutral-600">
                      <label className="cursor-pointer text-primary-600 hover:text-primary-700 font-medium">
                        Cliquez pour télécharger
                        <input
                          type="file"
                          multiple
                          accept="image/*,.pdf"
                          className="hidden"
                          onChange={(e) => handleFileChange(e, "portfolio")}
                        />
                      </label>
                      <p className="text-xs text-neutral-500 mt-1">
                        PNG, JPG, GIF ou PDF jusqu'à 10 Mo
                      </p>
                    </div>
                  </div>
                </div>

                {formData.portfolio.length > 0 && (
                  <div className="mt-4 grid grid-cols-2 sm:grid-cols-3 gap-4">
                    {formData.portfolio.map((file, index) => (
                      <div key={index} className="relative group">
                        <div className="aspect-square rounded-lg overflow-hidden border border-neutral-200 bg-neutral-50">
                          {file.type.startsWith("image/") ? (
                            <img
                              src={URL.createObjectURL(file)}
                              alt={`Portfolio item ${index + 1}`}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <FileText className="h-8 w-8 text-neutral-400" />
                              <span className="text-xs text-neutral-500 mt-1 ml-1">
                                {file.name}
                              </span>
                            </div>
                          )}
                        </div>
                        <button
                          type="button"
                          onClick={() => handleRemovePortfolioItem(index)}
                          className="absolute top-1 right-1 bg-white rounded-full p-1 shadow-sm opacity-0 group-hover:opacity-100 transition-opacity"
                          title="Supprimer cet élément du portfolio"
                          aria-label="Supprimer cet élément du portfolio"
                        >
                          <X className="h-4 w-4 text-neutral-500" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Step 3 (clients) or Step 4 (professionals): Confirmation */}
          {((userType === "client" && currentStep === 3) ||
            (userType === "professional" && currentStep === 4)) && (
            <div className="space-y-6">
              <div className="text-center">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-neutral-900">
                  Presque terminé !
                </h3>
                <p className="mt-2 text-neutral-600">
                  Votre profil est presque complet. Cliquez sur "Terminer" pour
                  enregistrer vos informations.
                </p>
              </div>

              <div className="bg-neutral-50 p-4 rounded-lg border border-neutral-200">
                <h4 className="font-medium text-neutral-900 mb-2">Rappel</h4>
                <p className="text-sm text-neutral-600">
                  Vous pourrez toujours modifier ces informations plus tard dans
                  les paramètres de votre profil.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-neutral-200 flex justify-between">
          <Button
            variant="ghost"
            onClick={currentStep === 1 ? onClose : handlePrevStep}
            style={{ color: "#3399FF", backgroundColor: "transparent" }}
          >
            {currentStep === 1 ? "Ignorer" : "Précédent"}
          </Button>

          <Button
            variant="primary"
            onClick={
              currentStep === totalSteps ? handleComplete : handleNextStep
            }
            style={{ backgroundColor: "#3399FF", color: "white" }}
            disabled={loading}
          >
            {loading
              ? "Chargement..."
              : currentStep === totalSteps
              ? "Terminer"
              : "Suivant"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProfileCompletionModal;
