import React from 'react';
import { Clock, DollarSign, Eye, ThumbsUp, Tag } from 'lucide-react';
import type { ServiceOffer } from './types';

interface ServiceOfferCardProps {
  service: ServiceOffer;
  onClick: () => void;
}

const ServiceOfferCard: React.FC<ServiceOfferCardProps> = ({
  service,
  onClick,
}) => {
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'archived':
        return 'bg-neutral-100 text-neutral-800';
      default:
        return 'bg-neutral-100 text-neutral-800';
    }
  };

  // Get status label
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'draft':
        return 'Brouillon';
      case 'published':
        return 'Publié';
      case 'archived':
        return 'Archivé';
      default:
        return 'Inconnu';
    }
  };

  return (
    <div
      className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
      onClick={onClick}
    >
      {/* Card image */}
      <div className="relative h-48">
        <img
          src={service.imageUrl || 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'}
          alt={service.title}
          className="w-full h-full object-cover"
        />

        {/* Status badge */}
        <div className="absolute top-2 right-2">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(service.status)}`}>
            {getStatusLabel(service.status)}
          </span>
        </div>

        {/* Private badge */}
        {service.is_private && (
          <div className="absolute top-2 left-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-800 text-white">
              Privé
            </span>
          </div>
        )}
      </div>

      {/* Card content */}
      <div className="p-4">
        <h3 className="text-lg font-semibold text-neutral-900 mb-2 line-clamp-1">{service.title}</h3>
        <p className="text-neutral-600 text-sm mb-4 line-clamp-2">{service.description}</p>

        <div className="space-y-2 mb-4">
          {/* Price */}
          <div className="flex items-center text-sm">
            <DollarSign className="h-4 w-4 text-neutral-500 mr-2" />
            <span className="text-neutral-700 font-medium">{service.price} €</span>
          </div>

          {/* Execution time */}
          <div className="flex items-center text-sm">
            <Clock className="h-4 w-4 text-neutral-500 mr-2" />
            <span className="text-neutral-700">{service.execution_time}</span>
          </div>

          {/* Views and likes */}
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center">
              <Eye className="h-4 w-4 text-neutral-500 mr-1" />
              <span className="text-neutral-700">{service.views || 0}</span>
            </div>
            <div className="flex items-center">
              <ThumbsUp className="h-4 w-4 text-neutral-500 mr-1" />
              <span className="text-neutral-700">{service.likes || 0}</span>
            </div>
          </div>
        </div>

        {/* Categories */}
        {service.categories && Array.isArray(service.categories) && service.categories.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-3">
            {service.categories.slice(0, 3).map((category, index) => (
              <div key={index} className="flex items-center text-xs bg-neutral-100 text-neutral-700 px-2 py-1 rounded-full">
                <Tag className="h-3 w-3 mr-1" />
                <span>{category}</span>
              </div>
            ))}
            {service.categories.length > 3 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-neutral-100 text-neutral-800">
                +{service.categories.length - 3}
              </span>
            )}
          </div>
        )}

        {/* Created date */}
        <div className="text-xs text-neutral-500 mt-2">
          Créé le {formatDate(service.created_at)}
        </div>
      </div>
    </div>
  );
};

export default ServiceOfferCard;
