import React from 'react';
import { Calendar, Clock, DollarSign } from 'lucide-react';
import Avatar from '../ui/Avatar';
import Badge from '../ui/Badge';

export interface ProjectCardProps {
  id: number;
  title: string;
  description: string;
  budget: string;
  deadline: string;
  status: 'draft' | 'open' | 'in_progress' | 'completed' | 'cancelled';
  client?: {
    id: number;
    name: string;
    avatar?: string;
  };
  professional?: {
    id: number;
    name: string;
    avatar?: string;
  };
  applicationsCount?: number;
  skills?: string[];
  category?: string;
  categories?: string[];
  onClick?: () => void;
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  id,
  title,
  description,
  budget,
  deadline,
  status,
  client,
  professional,
  applicationsCount,
  skills,
  category,
  categories,
  onClick,
}) => {
  // Status badge configuration
  const statusConfig = {
    draft: { label: 'Brouillon', color: 'warning' },
    open: { label: 'Ouvert', color: 'success' },
    in_progress: { label: 'En cours', color: 'primary' },
    completed: { label: 'Terminé', color: 'neutral' },
    cancelled: { label: 'Annulé', color: 'danger' },
  };

  // Format deadline
  const formatDeadline = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  // Calculate days remaining
  const getDaysRemaining = (dateString: string) => {
    const deadline = new Date(dateString);
    const now = new Date();
    const diffTime = deadline.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return 'Délai dépassé';
    } else if (diffDays === 0) {
      return 'Dernier jour';
    } else {
      return `${diffDays} jour${diffDays > 1 ? 's' : ''} restant${diffDays > 1 ? 's' : ''}`;
    }
  };

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      // Navigate to project details page if no onClick handler is provided
      window.location.href = `/dashboard/projects/${id}`;
    }
  };

  return (
    <div
      className={`bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden cursor-pointer hover:shadow-md transition-shadow`}
      onClick={handleClick}
    >
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-lg font-semibold text-neutral-900 line-clamp-1">{title}</h3>
          <Badge color={statusConfig[status].color as any}>
            {statusConfig[status].label}
          </Badge>
        </div>

        <p className="text-neutral-600 text-sm mb-4 line-clamp-2">{description}</p>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="flex items-center">
            <DollarSign className="h-4 w-4 text-neutral-500 mr-2" />
            <span className="text-sm text-neutral-700 font-medium">{budget}</span>
          </div>

          <div className="flex items-center">
            <Calendar className="h-4 w-4 text-neutral-500 mr-2" />
            <span className="text-sm text-neutral-700">{formatDeadline(deadline)}</span>
          </div>
        </div>

        {/* Category */}
        {category && (
          <div className="mb-3">
            <Badge color="secondary">{category}</Badge>
          </div>
        )}

        {/* Categories */}
        {categories && categories.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {categories.map((cat, index) => (
              <Badge key={index} color="blue" className="text-xs">{cat}</Badge>
            ))}
          </div>
        )}

        {/* Skills */}
        {skills && skills.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {skills.map((skill, index) => (
              <Badge key={index} color="info" className="text-xs">{skill}</Badge>
            ))}
          </div>
        )}

        <div className="flex items-center text-sm text-neutral-500">
          <Clock className="h-4 w-4 mr-1" />
          <span>{getDaysRemaining(deadline)}</span>
        </div>
      </div>

      <div className="px-6 py-4 bg-neutral-50 border-t border-neutral-200 flex justify-between items-center">
        {client && (
          <div className="flex items-center">
            <Avatar
              src={client.avatar}
              fallback={client.name.charAt(0)}
              size="sm"
              className="mr-2"
            />
            <span className="text-sm text-neutral-700">{client.name}</span>
          </div>
        )}

        {professional && (
          <div className="flex items-center">
            <Avatar
              src={professional.avatar}
              fallback={professional.name.charAt(0)}
              size="sm"
              className="mr-2"
            />
            <span className="text-sm text-neutral-700">{professional.name}</span>
          </div>
        )}

        {applicationsCount !== undefined && (
          <span className="text-sm text-neutral-600">
            {applicationsCount} candidature{applicationsCount !== 1 ? 's' : ''}
          </span>
        )}
      </div>
    </div>
  );
};

export default ProjectCard;
