import { useState, useEffect } from "react";
import { API_BASE_URL } from '../config';

interface Achievement {
  id: number;
  freelance_profile_id: number;
  title: string;
  organization: string;
  date_obtained: string;
  description: string;
  file_path: string | null;
  achievement_url: string;
}

const Realisations = () => {
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [selectedAchievement, setSelectedAchievement] = useState<Achievement | null>(null);
  
  useEffect(() => {
    fetchAchievements();
  }, []);

  const fetchAchievements = async () => {
    try {
      const token = localStorage.getItem("token");
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/api/achievements`, {
        method: "GET",
        headers: { "Authorization": `Bearer ${token}` }
      });
      const data = await response.json();
      setAchievements(data.achievements);
    } catch (error) {
      console.error("Erreur lors de la récupération des réalisations", error);
    }
  };

  const handleChange = (field: keyof Achievement, value: string) => {
    setSelectedAchievement((prev) => prev ? { ...prev, [field]: value } : null);
  };

  const handleSave = async () => {
    if (!selectedAchievement) return;
    try {
      const token = localStorage.getItem("token");
      if (!token) return;

      const method = selectedAchievement.id ? "PUT" : "POST";
      const url = selectedAchievement.id 
        ? `${API_BASE_URL}/api/achievements/${selectedAchievement.id}` 
        : `${API_BASE_URL}/api/achievements`;
      
      const response = await fetch(url, {
        method,
        headers: { 
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}` 
        },
        body: JSON.stringify(selectedAchievement)
      });
      const data = await response.json();
      
      if (selectedAchievement.id) {
        setAchievements((prev) => prev.map(ach => ach.id === data.achievement.id ? data.achievement : ach));
      } else {
        setAchievements((prev) => [...prev, data.achievement]);
      }
      setSelectedAchievement(null);
    } catch (error) {
      console.error("Erreur lors de l'enregistrement de la réalisation", error);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      const token = localStorage.getItem("token");
      if (!token) return;

      await fetch(`${API_BASE_URL}/api/achievements/${id}`, {
        method: "DELETE",
        headers: { "Authorization": `Bearer ${token}` }
      });

      setAchievements((prev) => prev.filter(ach => ach.id !== id));
    } catch (error) {
      console.error("Erreur lors de la suppression de la réalisation", error);
    }
  };

  return (
    <div className="min-h-screen bg-white px-4 sm:px-6 lg:px-8">
      <h2 className="text-3xl font-bold mb-6 text-gray-800">Réalisations</h2>

      {achievements.length === 0 && (
        <div className="text-center text-gray-500 mb-6">Aucune réalisation trouvée.</div>
      )}

      <button 
        onClick={() => setSelectedAchievement({ id: 0, freelance_profile_id: 0, title: "", organization: "", date_obtained: "", description: "", file_path: null, achievement_url: "" })} 
        className="bg-green-500 text-white px-4 py-2 rounded mb-4"
      >
        Créer une nouvelle réalisation
      </button>

      <div className="mb-6">
        {achievements.map(ach => (
          <div key={ach.id} className="border p-4 mb-2 rounded-lg flex justify-between">
            <div>
              <h3 className="text-lg font-bold">{ach.title}</h3>
              <p className="text-gray-600">{ach.organization} - {new Date(ach.date_obtained).toLocaleDateString('fr-FR', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
              <p className="text-gray-600">{ach.description}</p>
            </div>
            <div className="flex space-x-2">
              <button onClick={() => setSelectedAchievement(ach)} className="bg-blue-500 text-white px-3 py-1 rounded">Modifier</button>
              <button onClick={() => handleDelete(ach.id)} className="bg-red-500 text-white px-3 py-1 rounded">Supprimer</button>
            </div>
          </div>
        ))}
      </div>

      {selectedAchievement && (
        <div className="border p-6 rounded-lg shadow">
          <h3 className="text-xl font-bold mb-4">{selectedAchievement.id ? "Modifier" : "Ajouter"} une réalisation</h3>
          <input type="text" placeholder="Titre" value={selectedAchievement.title} onChange={(e) => handleChange("title", e.target.value)} className="w-full p-2 border mb-2 rounded" />
          <input type="text" placeholder="Organisation" value={selectedAchievement.organization} onChange={(e) => handleChange("organization", e.target.value)} className="w-full p-2 border mb-2 rounded" />
          <input type="date" value={selectedAchievement.date_obtained} onChange={(e) => handleChange("date_obtained", e.target.value)} className="w-full p-2 border mb-2 rounded" />
          <textarea placeholder="Description" value={selectedAchievement.description} onChange={(e) => handleChange("description", e.target.value)} className="w-full p-2 border mb-2 rounded" />
          <input type="url" placeholder="URL" value={selectedAchievement.achievement_url} onChange={(e) => handleChange("achievement_url", e.target.value)} className="w-full p-2 border mb-2 rounded" />
          <div className="flex space-x-4 mt-4">
            <button onClick={handleSave} className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition">Enregistrer</button>
            <button onClick={() => setSelectedAchievement(null)} className="bg-gray-500 text-white p-2 rounded-lg">Annuler</button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Realisations;



// import { useState, useEffect } from "react";
// import { API_BASE_URL } from '../config';

// interface Achievement {
//   id: number;
//   freelance_profile_id: number;
//   title: string;
//   organization: string;
//   date_obtained: string;
//   description: string;
//   file_path: string | null;
//   achievement_url: string;
// }

// const Realisations = () => {
//   const [achievements, setAchievements] = useState<Achievement[]>([]);
//   const [previewFiles, setPreviewFiles] = useState<{ [key: number]: string | null }>({});

//   // Fonction pour récupérer les réalisations via l'API
//   const fetchAchievements = async () => {
//     try {
//       const token = localStorage.getItem("token");
//       if (!token) return;

//       const response = await fetch(`${API_BASE_URL}/api/achievements`, {
//         method: "GET",
//         headers: {
//           "Authorization": `Bearer ${token}`
//         }
//       });
//       const data = await response.json();
//       setAchievements(data.achievements);
//     } catch (error) {
//       console.error("Erreur lors de la récupération des réalisations", error);
//     }
//   };

//   // Charger les réalisations dès que la page est ouverte
//   useEffect(() => {
//     fetchAchievements();
//   }, []);

//   // Ajouter une nouvelle réalisation
//   const addAchievementSansAPI = () => {
//     const newAchievement: Achievement = {
//       id: 0, // L'ID sera attribué après l'ajout via l'API
//       freelance_profile_id: 1, // Id à ajuster
//       title: "",
//       organization: "",
//       date_obtained: "",
//       description: "",
//       file_path: null,
//       achievement_url: "",
//     };
//     setAchievements([...achievements, newAchievement]);
//   };

//   const addAchievement = async (newAchievement: Achievement) => {
//     // const newAchievement: Achievement = {
//     //   id: 0, // L'ID sera attribué après l'ajout via l'API
//     //   freelance_profile_id: 1, // Id à ajuster
//     //   title: "",
//     //   organization: "",
//     //   date_obtained: "",
//     //   description: "",
//     //   file_path: null,
//     //   achievement_url: "",
//     // };

//     try {
//       const token = localStorage.getItem("token");
//       if (!token) return;

//       const formData = new FormData();
//       formData.append("title", newAchievement.title);
//       formData.append("organization", newAchievement.organization);
//       formData.append("date_obtained", newAchievement.date_obtained);
//       formData.append("description", newAchievement.description);
//       if (newAchievement.file_path) {
//         const file = new Blob([newAchievement.file_path], { type: "application/pdf" });
//         formData.append("file", file);
//       }
//       formData.append("achievement_url", newAchievement.achievement_url);

//       const response = await fetch(`${API_BASE_URL}/api/achievements`, {
//         method: "POST",
//         headers: {
//           "Authorization": `Bearer ${token}`,
//         },
//         body: formData,
//       });

//       const data = await response.json();
//       console.error("reponse", data);
//       if (data.achievement) {
//         setAchievements((prev) => [...prev, data.achievement]);
//       }
//     } catch (error) {
//       console.error("Erreur lors de l'ajout de la réalisation", error);
//     }
//   };

//   // Gérer les changements dans le formulaire
//   const handleChange = (id: number, field: keyof Achievement, value: string) => {
//     setAchievements((prev) =>
//       prev.map((ach) => (ach.id === id ? { ...ach, [field]: value } : ach))
//     );
//   };

//   // Gestion du téléchargement de fichier
//   const handleFileUpload = (id: number, e: React.ChangeEvent<HTMLInputElement>) => {
//     const file = e.target.files?.[0];
//     if (file) {
//       const fileUrl = URL.createObjectURL(file);
//       setPreviewFiles((prev) => ({ ...prev, [id]: fileUrl }));
//       setAchievements((prev) =>
//         prev.map((ach) => (ach.id === id ? { ...ach, file_path: file.name } : ach))
//       );
//     }
//   };

//   // Mettre à jour une réalisation
//   const updateAchievement = async (id: number, achievement: Achievement) => {
//     try {
//       const token = localStorage.getItem("token");
//       if (!token) return;

//       const formData = new FormData();
//       formData.append("title", achievement.title);
//       formData.append("organization", achievement.organization);
//       formData.append("date_obtained", achievement.date_obtained);
//       formData.append("description", achievement.description);
//       if (achievement.file_path) {
//         const file = new Blob([achievement.file_path], { type: "application/pdf" });
//         formData.append("file", file);
//       }
//       formData.append("achievement_url", achievement.achievement_url);

//       const response = await fetch(`${API_BASE_URL}/api/achievements/${id}`, {
//         method: "PUT",
//         headers: {
//           "Authorization": `Bearer ${token}`
//         },
//         body: formData
//       });
//       const data = await response.json();
//       setAchievements((prev) =>
//         prev.map((ach) => (ach.id === id ? { ...data.achievement } : ach))
//       );
//     } catch (error) {
//       console.error("Erreur lors de la mise à jour de la réalisation", error);
//     }
//   };

//   // Supprimer une réalisation
//   const deleteAchievement = async (id: number) => {
//     try {
//       const token = localStorage.getItem("token");
//       if (!token) return;

//       const response = await fetch(`${API_BASE_URL}/api/achievements/${id}`, {
//         method: "DELETE",
//         headers: {
//           "Authorization": `Bearer ${token}`
//         }
//       });
//       const data = await response.json();
//       setAchievements(achievements.filter((ach) => ach.id !== id));
//     } catch (error) {
//       console.error("Erreur lors de la suppression de la réalisation", error);
//     }
//   };

//   return (
//     <div className="w-full md:w-2/3 lg:w-1/2 bg-white p-8 shadow-lg rounded-xl ml-6 mt-6">
//       <h2 className="text-3xl font-bold mb-6 text-gray-800">Réalisations</h2>

//       {achievements.map((ach) => (
//         <div key={ach.id} className="border p-4 mb-6 rounded-lg shadow-sm">
//           <h3 className="text-lg font-bold text-gray-700">{ach.title || `Réalisation ${ach.id}`}</h3>
//           <form className="space-y-4">
//             <div>
//               <label className="block text-gray-700 font-medium mb-1">Titre</label>
//               <input
//                 type="text"
//                 value={ach.title}
//                 onChange={(e) => handleChange(ach.id, "title", e.target.value)}
//                 className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500"
//                 placeholder="Ex: Certification AWS"
//               />
//             </div>

//             <div>
//               <label className="block text-gray-700 font-medium mb-1">Organisation</label>
//               <input
//                 type="text"
//                 value={ach.organization}
//                 onChange={(e) => handleChange(ach.id, "organization", e.target.value)}
//                 className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500"
//                 placeholder="Ex: Amazon Web Services"
//               />
//             </div>

//             <div>
//               <label className="block text-gray-700 font-medium mb-1">Date d'obtention</label>
//               <input
//                 type="date"
//                 value={ach.date_obtained}
//                 onChange={(e) => handleChange(ach.id, "date_obtained", e.target.value)}
//                 className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500"
//               />
//             </div>

//             <div>
//               <label className="block text-gray-700 font-medium mb-1">Description</label>
//               <textarea
//                 value={ach.description}
//                 onChange={(e) => handleChange(ach.id, "description", e.target.value)}
//                 className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500"
//                 placeholder="Détails sur la réalisation"
//               />
//             </div>

//             <div>
//               <label className="block text-gray-700 font-medium mb-1">URL de la réalisation</label>
//               <input
//                 type="url"
//                 value={ach.achievement_url}
//                 onChange={(e) => handleChange(ach.id, "achievement_url", e.target.value)}
//                 className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500"
//                 placeholder="http://exemple.com"
//               />
//             </div>

//             <div>
//               <label className="block text-gray-700 font-medium mb-1">Fichier de preuve</label>
//               <input
//                 type="file"
//                 accept="image/*,application/pdf"
//                 onChange={(e) => handleFileUpload(ach.id, e)}
//                 className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
//               />
//               {previewFiles[ach.id] && (
//                 <img
//                   src={previewFiles[ach.id]!}
//                   alt="Aperçu du fichier"
//                   className="mt-4 w-full h-40 object-cover rounded-lg shadow"
//                 />
//               )}
//             </div>

//             <div className="flex space-x-4 mt-4">
//             <button
//                 type="button"
//                 onClick={() => addAchievement(ach)}
//                 className="bg-blue-600 text-white p-2 rounded-md hover:bg-blue-700"
//               >
//                 Ajouter
//               </button>
//               <button
//                 type="button"
//                 onClick={() => updateAchievement(ach.id, ach)}
//                 className="bg-blue-600 text-white p-2 rounded-md hover:bg-blue-700"
//               >
//                 Modifier
//               </button>
//               <button
//                 type="button"
//                 onClick={() => deleteAchievement(ach.id)}
//                 className="bg-red-600 text-white p-2 rounded-md hover:bg-red-700"
//               >
//                 Supprimer
//               </button>
//             </div>
//           </form>
//         </div>
//       ))}

//       <button
//         onClick={addAchievementSansAPI}
//         className="w-full bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition mt-4"
//       >
//         ➕ Ajouter une réalisation
//       </button>
//     </div>
//   );
// };

// export default Realisations;



