import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { API_BASE_URL } from '../../config';
import { NotificationItemProps } from './NotificationItem';
import { createOfferNotification, OfferNotificationType, OfferNotificationData } from './types/OfferNotification';

interface NotificationContextType {
  notifications: NotificationItemProps[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  markAsRead: (id: number) => void;
  markAllAsRead: () => void;
  fetchNotifications: () => void;
  addOfferNotification: (type: OfferNotificationType, data: OfferNotificationData) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<NotificationItemProps[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const token = localStorage.getItem('token');

  // Calculate unread count
  const unreadCount = notifications.filter(notification => !notification.read).length;

  // Fetch notifications
  const fetchNotifications = async () => {
    if (!token) return;

    setLoading(true);
    try {
      // In a real implementation, you would fetch from your API
      // For now, we'll use mock data

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock notifications data
      const mockNotifications: NotificationItemProps[] = [
        {
          id: 1,
          title: 'Nouveau message',
          message: 'Thomas Martin vous a envoyé un message concernant votre projet "Création d\'un personnage 3D".',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
          read: false,
          type: 'message',
          link: '/discussions/201',
          sender: {
            id: 201,
            name: 'Thomas Martin',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
          },
        },
        {
          id: 2,
          title: 'Mise à jour de projet',
          message: 'Le projet "Animation d\'une scène d\'introduction" a été mis à jour avec de nouveaux fichiers.',
          timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
          read: true,
          type: 'project',
          link: '/offre/2',
        },
        {
          id: 3,
          title: 'Paiement reçu',
          message: 'Vous avez reçu un paiement de 500€ pour le projet "Modélisation d\'objets pour environnement virtuel".',
          timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
          read: false,
          type: 'payment',
          link: '/invoices',
        },
      ];

      setNotifications(mockNotifications);
      setError(null);
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError('Impossible de charger les notifications');
    } finally {
      setLoading(false);
    }
  };

  // Demander la permission pour les notifications système
  useEffect(() => {
    if ('Notification' in window && Notification.permission !== 'denied') {
      Notification.requestPermission();
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    if (token) {
      fetchNotifications();
    }
  }, [token]);

  // Set up polling for new notifications (every 1 minute)
  useEffect(() => {
    if (!token) return;

    const intervalId = setInterval(() => {
      fetchNotifications();
    }, 60000);

    return () => clearInterval(intervalId);
  }, [token]);

  // Mark notification as read
  const markAsRead = async (id: number) => {
    if (!token) return;

    try {
      // In a real implementation, you would call your API
      // For now, we'll update the state directly
      setNotifications(prevNotifications =>
        prevNotifications.map(notification =>
          notification.id === id
            ? { ...notification, read: true }
            : notification
        )
      );
    } catch (err) {
      console.error('Error marking notification as read:', err);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    if (!token) return;

    try {
      // In a real implementation, you would call your API
      // For now, we'll update the state directly
      setNotifications(prevNotifications =>
        prevNotifications.map(notification => ({
          ...notification,
          read: true,
        }))
      );
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
    }
  };

  // Ajouter une notification d'appel d'offre
  const addOfferNotification = (type: OfferNotificationType, data: OfferNotificationData) => {
    // Créer une nouvelle notification
    const newNotification = createOfferNotification(
      Date.now(), // Utiliser un timestamp comme ID temporaire
      type,
      data,
      new Date().toISOString(),
      false // Non lue par défaut
    );

    // Ajouter la notification à la liste
    setNotifications(prevNotifications => [newNotification, ...prevNotifications]);

    // Afficher une notification système si le navigateur le permet
    if ('Notification' in window && Notification.permission === 'granted') {
      const notification = new Notification(newNotification.title, {
        body: newNotification.message,
        icon: '/logo192.png',
      });

      // Rediriger vers le lien de la notification lorsqu'on clique dessus
      notification.onclick = () => {
        window.focus();
        if (newNotification.link) {
          window.location.href = newNotification.link;
        }
      };
    }
  };

  const value = {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    fetchNotifications,
    addOfferNotification,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export default NotificationContext;
