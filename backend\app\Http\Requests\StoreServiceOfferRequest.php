<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreServiceOfferRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // You should add your authorization logic here.
        // For example, check if the user has the permission to create service offers.
        return true; // Or your authorization logic, e.g., Auth::user()->can('create', ServiceOffer::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules()
{
    return [
        // Nom du service
        'title' => 'required|string|max:255',

        // Catégorie (sélecteur de catégories prédéfinies)
        'categories' => 'required|array',
        'categories.*' => 'string|max:255',

        // Description avec possibilité de mise en forme
        'description' => 'required|string',

        // Prix de base (USD)
        'price' => 'required|numeric|min:0',

        // Unité du prix
        'price_unit' => 'required|string|in:per_image,per_sqm,per_project',

        // Durée moyenne de réalisation
        'average_duration' => 'required|string|in:less_than_week,1_to_2_weeks,1_month,2_months,3_months',

        // Image de couverture
        'cover_image' => 'nullable|file|max:10240|mimes:jpeg,png,jpg,gif,svg,webp',

        // Catégorie d'image
        'image_category' => 'nullable|string|max:255',

        // Projet associé
        'associated_project_id' => 'nullable|exists:dashboard_projects,id',

        // Champs existants (optionnels maintenant)
        'execution_time' => 'nullable|string',
        'concepts' => 'nullable|string',
        'revisions' => 'nullable|string',
        'is_private' => 'boolean',
        'status' => 'required|string|in:published,draft,pending',
        'files' => 'nullable|array',
        'files.*' => 'file|max:10240|mimes:jpeg,png,jpg,gif,svg,webp,pdf,doc,docx,xls,xlsx,ppt,pptx,zip,rar',
    ];
}


    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages()
    {
        return [
            // Messages pour les nouveaux champs
            'title.required' => 'Le nom du service est obligatoire.',
            'title.string' => 'Le nom du service doit être une chaîne de caractères.',
            'title.max' => 'Le nom du service ne doit pas dépasser 255 caractères.',

            'categories.required' => 'La catégorie est obligatoire.',
            'categories.array' => 'Les catégories doivent être un tableau.',
            'categories.*.string' => 'Chaque catégorie doit être une chaîne de caractères.',
            'categories.*.max' => 'Une catégorie ne doit pas dépasser 255 caractères.',

            'description.required' => 'La description est obligatoire.',
            'description.string' => 'La description doit être une chaîne de caractères.',

            'price.required' => 'Le prix de base est obligatoire.',
            'price.numeric' => 'Le prix doit être un nombre.',
            'price.min' => 'Le prix doit être un nombre positif.',

            'price_unit.required' => 'L\'unité du prix est obligatoire.',
            'price_unit.in' => 'L\'unité du prix doit être : par image, par m² ou par projet.',

            'average_duration.required' => 'La durée moyenne de réalisation est obligatoire.',
            'average_duration.in' => 'La durée doit être : moins d\'une semaine, 1 à 2 semaines, 1 mois, 2 mois ou 3 mois.',

            'cover_image.file' => 'L\'image de couverture doit être un fichier.',
            'cover_image.max' => 'L\'image de couverture ne doit pas dépasser 10 Mo.',
            'cover_image.mimes' => 'L\'image de couverture doit être au format : jpeg, png, jpg, gif, svg ou webp.',

            'associated_project_id.exists' => 'Le projet associé sélectionné n\'existe pas.',
        ];
    }
}
