import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle } from 'lucide-react';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

/**
 * Composant ErrorBoundary pour capturer les erreurs JavaScript dans les composants enfants
 * et afficher une interface de secours au lieu de planter toute l'application
 */
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Mettre à jour l'état pour afficher l'interface de secours
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Vous pouvez également enregistrer l'erreur dans un service de rapport d'erreurs
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Appeler le gestionnaire d'erreurs personnalisé si fourni
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // Vous pouvez rendre n'importe quelle interface de secours personnalisée
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="p-6 bg-red-50 border border-red-200 rounded-lg text-center" role="alert">
          <div className="flex justify-center mb-4">
            <AlertTriangle className="h-12 w-12 text-red-500" aria-hidden="true" />
          </div>
          <h2 className="text-xl font-semibold text-red-800 mb-2">Une erreur est survenue</h2>
          <p className="text-red-700 mb-4">
            Nous sommes désolés, une erreur inattendue s'est produite.
          </p>
          <p className="text-sm text-red-600 mb-4">
            {this.state.error?.message || 'Erreur inconnue'}
          </p>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            Rafraîchir la page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
