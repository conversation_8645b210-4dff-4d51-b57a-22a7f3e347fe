// import { useNavigate } from "react-router-dom";
// import React, { useState, useEffect } from 'react';
// import { MoreHorizontal } from "lucide-react";
// import { API_BASE_URL } from '../config';
// import CreateContactModal from './CreateContactModal';

// interface Contact {
//     id: number;
//     user_id: number; 
//     name: string; 
//     email: string; 
//     phone: string; 
//     notes: string; 
// }

// const PageContact = () => {
//     const [contact, setContact] = useState<Contact[]>([]);
//     const [isModalOpen, setIsModalOpen] = useState(false);
//     const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
//     const [activeMenu, setActiveMenu] = useState<number | null>(null);

//     useEffect(() => {
//         const storedContact = localStorage.getItem('contact');
//         if (storedContact) {
//             const parsedContact = JSON.parse(storedContact);
//             console.log('Contacts mémorisés chargés :', parsedContact);
//             setContact(parsedContact);
//         }
//     }, []);

//     useEffect(() => {
//         if (contact.length > 0) {
//             localStorage.setItem('contact', JSON.stringify(contact));
//             console.log('Contacts mémorisés :', contact);
//         }
//     }, [contact]);

//     const handleAddContact = (newContact: Contact) => {
//         if (selectedContact) {
//             setContact((prevContact) =>
//                 prevContact.map((s) => (s.id === newContact.id ? newContact : s))
//             );
//         } else {
//             setContact((prevContact) => [...prevContact, newContact]);
//         }
//         setSelectedContact(null);
//         setIsModalOpen(false);
//     };

//     const handleDelete = (id: number) => {
//         const updatedContact = contact.filter((c) => c.id !== id);
//         setContact(updatedContact);
//         setActiveMenu(null);
//     };

//     const handleEdit = (contact: Contact) => {
//         setSelectedContact(contact);
//         setIsModalOpen(true);
//         setActiveMenu(null);
//     };

//     const handleCreateContact = () => {
//         setSelectedContact(null);
//         setIsModalOpen(true);
//     };

//     const toggleMenu = (id: number) => {
//         setActiveMenu(activeMenu === id ? null : id);
//     };

//     return (
//         <div className="w-full p-4 bg-white rounded-lg shadow-md mt-16">
//             <h1 className="text-3xl font-bold text-gray-900 mb-4">Mes Contacts</h1>

//             <button
//                 className="w-full px-6 py-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 flex items-center justify-center gap-2 mb-4"
//                 onClick={handleCreateContact}
//             >
//                 <span>Nouveau Contact</span>
//             </button>

//             <h4 className="text-normal text-gray-900 mb-2 mt-4">LISTE DES CONTACTS</h4>
//             {contact.map((c) => (
//                 <div
//                     key={c.id}
//                     className="flex items-center justify-between bg-white p-3 rounded-md mb-2 shadow-sm relative"
//                 >
//                     <div className="flex items-center space-x-3">
//                         <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
//                             <span className="text-gray-500">👤</span>
//                         </div>
//                         <span>{c.name}</span>
//                     </div>
//                     <button
//                         className="p-1 text-gray-500 hover:text-gray-700 relative"
//                         onClick={() => toggleMenu(c.id)}
//                     >
//                         <MoreHorizontal className="w-5 h-5" />
//                     </button>
                    
//                     {activeMenu === c.id && (
//                         <div className="absolute right-4 top-10 bg-white border rounded-md shadow-md w-32">
//                             <button
//                                 className="block w-full text-left px-4 py-2 hover:bg-gray-100"
//                                 onClick={() => handleEdit(c)}
//                             >
//                                 Modifier
//                             </button>
//                             <button
//                                 className="block w-full text-left px-4 py-2 text-red-500 hover:bg-gray-100"
//                                 onClick={() => handleDelete(c.id)}
//                             >
//                                 Supprimer
//                             </button>
//                         </div>
//                     )}
//                 </div>
//             ))}

//             {isModalOpen && (
//                 <CreateContactModal
//                     onClose={() => setIsModalOpen(false)}
//                     onAddContact={handleAddContact}
//                     existingContact={selectedContact}
//                 />
//             )}
//         </div>
//     );
// };

// export default PageContact;

import { useNavigate } from "react-router-dom";
import React, { useState, useEffect } from 'react';
import { MoreHorizontal } from "lucide-react";
import { API_BASE_URL } from '../config';
import CreateContactModal from './CreateContactModal';

interface Contact {
    id: number;
    user_id: number; 
    name: string; 
    email: string; 
    phone: string; 
    notes: string; 
}

const PageContact = () => {
    const [contacts, setContacts] = useState<Contact[]>([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
    const [activeMenu, setActiveMenu] = useState<number | null>(null);

    useEffect(() => {
        fetchContacts();
    }, []);

    // Récupération des contacts depuis l'API
    const fetchContacts = async () => {
        const token = localStorage.getItem('token');
        if (!token) return;

        try {
            const response = await fetch(`${API_BASE_URL}/api/contacts`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            const data = await response.json();

            if (response.ok) {
                setContacts(data.contacts);
                console.log('Contacts récupérés :', data.contacts);
            } else {
                console.error('Erreur lors de la récupération des contacts', data);
            }
        } catch (error) {
            console.error('Erreur lors de la récupération des contacts', error);
        }
    };

    // Suppression d'un contact via l'API
    const handleDelete = async (id: number) => {
        const token = localStorage.getItem('token');
        if (!token) return;

        try {
            const response = await fetch(`${API_BASE_URL}/api/contacts/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.ok) {
                console.log('Contact supprimé avec succès');
                setContacts((prevContacts) => prevContacts.filter((contact) => contact.id !== id));
                setActiveMenu(null);
            } else {
                const data = await response.json();
                console.error('Erreur lors de la suppression du contact', data);
            }
        } catch (error) {
            console.error('Erreur lors de la suppression du contact', error);
        }
    };

    const handleAddContact = (newContact: Contact) => {
        if (selectedContact) {
            setContacts((prevContacts) =>
                prevContacts.map((contact) => (contact.id === newContact.id ? newContact : contact))
            );
        } else {
            setContacts((prevContacts) => [...prevContacts, newContact]);
        }
        setSelectedContact(null);
        setIsModalOpen(false);
    };

    const handleEdit = (contact: Contact) => {
        setSelectedContact(contact);
        setIsModalOpen(true);
        setActiveMenu(null);
    };

    const handleCreateContact = () => {
        setSelectedContact(null);
        setIsModalOpen(true);
    };

    const toggleMenu = (id: number) => {
        setActiveMenu(activeMenu === id ? null : id);
    };

    return (
        <div className="w-full p-4 bg-white rounded-lg shadow-md mt-16">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Mes Contacts</h1>

            <button
                className="w-full px-6 py-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 flex items-center justify-center gap-2 mb-4"
                onClick={handleCreateContact}
            >
                <span>Nouveau Contact</span>
            </button>

            <h4 className="text-normal text-gray-900 mb-2 mt-4">LISTE DES CONTACTS</h4>
            {contacts.map((contact) => (
                <div
                    key={contact.id}
                    className="flex items-center justify-between bg-white p-3 rounded-md mb-2 shadow-sm relative"
                >
                    <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                            <span className="text-gray-500">👤</span>
                        </div>
                        <span>{contact.name}</span>
                    </div>
                    <button
                        className="p-1 text-gray-500 hover:text-gray-700 relative"
                        onClick={() => toggleMenu(contact.id)}
                    >
                        <MoreHorizontal className="w-5 h-5" />
                    </button>
                    
                    {activeMenu === contact.id && (
                        <div className="absolute right-4 top-10 bg-white border rounded-md shadow-md w-32 z-50">
                            <button
                                className="block w-full text-left px-4 py-2 hover:bg-gray-100"
                                onClick={() => handleEdit(contact)}
                            >
                                Modifier
                            </button>
                            <button
                                className="block w-full text-left px-4 py-2 text-red-500 hover:bg-gray-100"
                                onClick={() => handleDelete(contact.id)}
                            >
                                Supprimer
                            </button>
                        </div>
                    )}
                </div>
            ))}

            {isModalOpen && (
                <CreateContactModal
                    onClose={() => setIsModalOpen(false)}
                    onAddContact={handleAddContact}
                    existingContact={selectedContact}
                />
            )}
        </div>
    );
};

export default PageContact;

