import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSearch, faChevronDown } from "@fortawesome/free-solid-svg-icons";

const filters = [
  "Tout",
  "Modélisation 3D",
  "Animation 3D",
  "Rendu 3D",
  "Texturing et Shading",
  "Effets visuels (VFX)",
  "Conception de personnages 3D",
  "Environnements 3D",
  "Réalité virtuelle (VR)",
  "Réalité augmentée (AR)",
  "Simulations physiques",
  "Rigging 3D",
  "Lighting 3D",
  "Compositing 3D",
  "Design de produits 3D",
  "Architecture 3D",
  "Jeux vidéo 3D",
  "Cinéma 4D",
  "Blender",
  "ZBrush",
  "Substance Painter",
];

const sortOptions = ["Recommandations", "Les plus suivis", "Les plus appréciés"];

const IndependentFilters = () => {
  const [selectedFilter, setSelectedFilter] = useState("Tout");
  const [selectedSort, setSelectedSort] = useState("Recommandations");
  const [showSortMenu, setShowSortMenu] = useState(false);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 bg-white shadow-sm rounded-lg p-4 z-10">
      {/* En-tête avec Recherche, Titre et Tri alignés */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Indépendants disponibles</h2>

        <div className="relative flex-1 max-w-md">
          <input
            type="text"
            placeholder="Rechercher des créatifs..."
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
        </div>

        <div className="relative">
          <button
            className="flex items-center gap-2 px-4 py-2 border border-blue-500 rounded-full text-blue-600 font-medium focus:outline-none"
            onClick={() => setShowSortMenu(!showSortMenu)}
          >
            Trier <span className="font-bold">{selectedSort}</span>
            <FontAwesomeIcon icon={faChevronDown} className="text-blue-500" />
          </button>
          {showSortMenu && (
            <div className="absolute right-0 mt-2 bg-white shadow-md rounded-lg overflow-hidden w-48">
              {sortOptions.map((option, index) => (
                <button
                  key={index}
                  className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                  onClick={() => {
                    setSelectedSort(option);
                    setShowSortMenu(false);
                  }}
                >
                  {option}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Barre de filtres */}
      <div className="flex items-center gap-2 overflow-x-auto pb-2">
        {filters.map((filter, index) => (
          <button
            key={index}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors whitespace-nowrap ${
              selectedFilter === filter
                ? "bg-blue-600 text-white"
                : "bg-gray-200 text-gray-800 hover:bg-gray-300"
            }`}
            onClick={() => setSelectedFilter(filter)}
          >
            {filter}
          </button>
        ))}
      </div>
    </div>
  );
};

export default IndependentFilters;
