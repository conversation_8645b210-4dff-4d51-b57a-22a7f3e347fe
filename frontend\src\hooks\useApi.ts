import { useState, useCallback } from 'react';
import { useToast } from '../context/ToastContext';

interface ApiOptions {
  showSuccessToast?: boolean;
  showErrorToast?: boolean;
  successMessage?: string;
  errorMessage?: string;
}

/**
 * Hook personnalisé pour la gestion des requêtes API
 * @param apiFunction Fonction API à exécuter
 * @param options Options de configuration
 */
const useApi = <T, P extends any[]>(
  apiFunction: (...args: P) => Promise<T>,
  options: ApiOptions = {}
) => {
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const toast = useToast();

  const {
    showSuccessToast = false,
    showErrorToast = true,
    successMessage = 'Opération réussie',
    errorMessage = 'Une erreur est survenue'
  } = options;

  const execute = useCallback(
    async (...args: P): Promise<T | null> => {
      try {
        setIsLoading(true);
        setError(null);

        const result = await apiFunction(...args);
        setData(result);

        if (showSuccessToast) {
          toast.showToast('success', successMessage);
        }

        return result;
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        setError(error);

        if (showErrorToast) {
          toast.showToast('error', error.message || errorMessage);
        }

        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [apiFunction, showSuccessToast, showErrorToast, successMessage, errorMessage, toast]
  );

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setIsLoading(false);
  }, []);

  return {
    data,
    error,
    isLoading,
    execute,
    reset
  };
};

export default useApi;
