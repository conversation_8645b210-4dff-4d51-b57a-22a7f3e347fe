import React, { useState, useEffect } from 'react';
import { MoreHorizontal } from 'lucide-react';
// import CreateMembersModal from './CreateMembersModal';

interface Members {
  id: number;
  title: string;
  author: string;
  imageUrl: string;
  description: string;
  executionTime: string;
  concepts: string;
  revisions: string;
  categories: string[];
  isPrivate: boolean;
  likes: number;
  views: number;
}

const EquipeMembers = () => {
  const [Memberss, setMemberss] = useState<Members[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedMembers, setSelectedMembers] = useState<Members | null>(null);

  useEffect(() => {
    const storedMemberss = localStorage.getItem('Memberss');
    if (storedMemberss) {
      const parsedMemberss = JSON.parse(storedMemberss);
      console.log('Memberss mémorisés chargés :', parsedMemberss);
      setMemberss(parsedMemberss);
    }
  }, []);

  useEffect(() => {
    if (Memberss.length > 0) {
      localStorage.setItem('Memberss', JSON.stringify(Memberss));
      console.log('Memberss mémorisés :', Memberss);
    }
  }, [Memberss]);

  const handleAddMembers = (Members: Members) => {
    if (selectedMembers) {
      setMemberss((prevMemberss) =>
        prevMemberss.map((s) => (s.id === Members.id ? Members : s))
      );
    } else {
      setMemberss((prevMemberss) => [...prevMemberss, Members]);
    }
    setSelectedMembers(null);
    setIsModalOpen(false);
  };

  const handleDelete = (id: number) => {
    const updatedMemberss = Memberss.filter((Members) => Members.id !== id);
    setMemberss(updatedMemberss);
  };

  const handleEdit = (Members: Members) => {
    setSelectedMembers(Members);
    setIsModalOpen(true);
  };

  const handleCreateMembers = () => {
    setSelectedMembers(null);
    setIsModalOpen(true);
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Mes Equipes</h1>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {Memberss.map((Members) => (
          <div
            key={Members.id}
            className="relative bg-white shadow-md rounded-md overflow-hidden"
          >
            <img
              src={Members.imageUrl}
              alt={Members.title}
              className="w-full h-48 object-cover"
            />
            <div className="p-4">
              <h2 className="text-lg font-semibold">{Members.title}</h2>
              <p className="text-gray-500">{Members.author}</p>
              <p className="text-gray-500">{Members.description}</p>
            </div>
            <div className="absolute top-2 right-2 z-10">
              <div className="relative group">
                <button className="p-2 bg-white rounded-full shadow">
                  <MoreHorizontal className="w-5 h-5 text-gray-700" />
                </button>
                <div className="absolute right-0 mt-2 w-40 bg-white shadow-lg rounded-md hidden group-hover:block">
                  <button
                    className="w-full text-left px-4 py-2 hover:bg-gray-100"
                    onClick={() => handleEdit(Members)}
                  >
                    Modifier
                  </button>
                  <button
                    className="w-full text-left px-4 py-2 hover:bg-gray-100 text-red-600"
                    onClick={() => handleDelete(Members.id)}
                  >
                    Supprimer
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}

        <div
          className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 p-8 rounded-md cursor-pointer"
          onClick={handleCreateMembers}
        >
          <div className="bg-blue-100 text-blue-500 rounded-full p-4 mb-2">
            <span className="text-3xl">+</span>
          </div>
          <h2 className="text-lg font-semibold">Créer un Membre</h2>
        </div>
      </div>

      {/* {isModalOpen && (
        <CreateMembersModal
          onClose={() => setIsModalOpen(false)}
          onAddMembers={handleAddMembers}
          existingMembers={selectedMembers}
        />
      )} */}
    </div>
  );
};

export default EquipeMembers;
